{"cells": [{"cell_type": "markdown", "id": "883aa1cc-9797-4659-8041-5558b531df7b", "metadata": {}, "source": ["# <center>Part2 DeepSeekV3预训练流程"]}, {"cell_type": "markdown", "id": "0ccee010-70ab-4197-88db-9650df0da33d", "metadata": {}, "source": ["![](https://cdn.eso.org/images/original/science-stars-formation-banner1.jpg)"]}, {"cell_type": "markdown", "id": "a49d7477-5876-4443-be3a-6a220f7d7750", "metadata": {}, "source": ["## 0 DeepSeekv3训练必备硬件与学习路线"]}, {"cell_type": "markdown", "id": "35a0ae4d-ab1b-4baa-9b7f-1b0beff314ca", "metadata": {}, "source": ["预训练是大语言模型构建过程中非常核心的环节、曾经它是一支“高岭之花”、其高昂的成本、复杂的流程让大部分公司望而却步，但随着DeepSeek系列模型的推出、随着unsloth框架频频推出更低精度的量化版本模型、以及随着国产GPU和海外GPU不断加入更低精度训练和推理专属的计算模块、大模型训练的成本越来越低、构建私有模型、构建专属模型成为许多企业和政府机构的实际需求。\n", "\n", "在开源模型众多、各类量化模型层出不穷的今天，为什么我们的课程还要耗费巨大经历、为大家研发预训练技术、甚至是走通DeepSeekv3的预训练全流程？这是因为——\n", "\n", "1. <font color=\"green\">**预训练能够弥补微调（Fine-tuning）或 RAG（Retrieval-Augmented Generation）的能力不足、为行业创造真正专业的“垂类模型”，随着训练成本越来越低，预训练技术的需求会逐渐增长**</font>。微调和RAG虽然能提升模型在特定任务上的表现，但它们都无法从根本上改变模型的知识储备和表示能力，在下面的情况中你会需要预训练、而不止微调——\n", "> - **知识密集型任务**：例如医学诊断、法律咨询、金融分析等领域，单纯的微调无法让模型掌握足够的领域知识，而在预训练阶段加入大规模医学、法律等专业文献可以显著提升模型的基础能力、构造真正的“垂类模型”。\n", "> - **新概念引入、知识灌注**：比如医疗AI模型需要理解新的病症、新的治疗方法，仅靠微调可能无法让模型真正掌握这些知识，需要在预训练过程中引入相关数据并进行大规模建模。\n", "> - **推理能力的培养**：微调和 RAG 通常更偏向于“记忆”已有知识，而 复杂推理能力（如数学、逻辑推理、代码理解等）必须依赖预训练阶段的架构设计、数据分布、目标函数优化等。\n", "\n", "2. <font color=\"green\">**DeepSeekv3带来了众多关键预训练技术、理解这些技术对未来掌握大规模模型训练至关重要**</font>。DeepSeekv3的MTP（Multi-token Prediction）技巧、以及Tensor + Pipeline + ZeRO 1 Data + Experts 4种类型的并行都需要在预训练过程中实现、而这些技术未来或许成为预训练领域非常核心的根基技术。\n", "\n", "3. <font color=\"green\">**这是我们首次实现工业级预训练项目、从数据准备到预训练的全流程、我们都配备了让你能够拓展到100B模型的预训练流程供你学习**</font>。这份价值百万的经验与拓展代码保障了学习的实用性、它成为你落地项目、研究和求职过程中非常高价值的课程内容。"]}, {"cell_type": "markdown", "id": "82360a6c-2459-4dca-8cbd-245139408ff5", "metadata": {}, "source": ["### 0.1 授课形式与必备前置基础"]}, {"cell_type": "markdown", "id": "e3c93b9b-7d92-47ea-a256-b9ba88583ae9", "metadata": {}, "source": ["在之前的课程中、我们已经基于MateConv Mini详解了预训练过程中的各类流程以及优化流程、预训练课程本身已有14+小时内容，因此本次课程中涉及的预训练内容将不会再细致展开探讨训练原理、而是按照如下流程安排——\n", "\n", "1. 4小时带伙伴们跑通DeepSeek预训练全套代码、中间不穿插详细原理详解\n", "2. 2~4小时解读DeepSeekv3预训练中MTP、如何兼容多种并行等等创新点\n", "\n", "因此、<font color=\"red\">**如果你需要的是前4小时跑通代码的流程、那你几乎无需任何基础知识即可学习**</font>、只需按照我所设置的流程一步步运行代码即可。\n", "\n", "<font color=\"red\">**如果你的目标是彻底吃透DeepSeek预训练流程、那你在跑通代码的同时，还会需要之前我为DeepSeek课程所设置的一系列基础**</font>——\n", "\n", "> **1 【Transformer】1～4、10～16**\n", "\n", "如果你不理解参数超参数的区别、如果你不理解QKV是如何诞生、你不能轻松说出Decoder-only架构的训练和推理有什么区别、你不能理解生成模型的输出层是怎么输出文字的、那你需要学习Transformer内容。\n", "\n", "> **2 【llama】1～2、4～6、8～9、11～14、17～19**\n", "\n", "DeepSeek架构与我们实现的LlaMA架构高度相似、在LlaMA架构中我们详解了MOE混合专家模型、详解了KV缓存机制、详解了门控机制、这些都是DeepSeek模型的关键基础，掌握LLaMA架构能让你在学习deepseek时事半功倍！\n", "\n", "> **3 【分布式预训练】0.1、0.2、1～4，12~后续全部内容**\n", "\n", "在分布式预训练章节中，我们搭建了基于Llama + MOE架构自建的【对话模型MateConv】、其环境搭建、数据收集、训练流程和训练脚本将会在后续的DeepSeekv3课程和DeepSeekR1课程中发挥巨大作用，了解经典的预训练流程将会对你有很大的帮助！如果你有足够的时间，可以将整个分布式预训练流程看完。\n", "\n", "> **4 【DeepSeekV3】全部已有直播内容**\n", "\n", "在之前的DeepSeek课程中我们详解了DeepSeek的模型文件`deepseekv3_model.py`，这一脚本正是我们接下来要进行训练的基础脚本。因此掌握DeepSeekv3架构原理会对你后续进行预训练有较好的帮助。"]}, {"cell_type": "markdown", "id": "3c3ee181-f8ba-417f-8641-08b1822431f0", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "f47fa2f5-b6e5-4aef-a97b-e84ab882d7d3", "metadata": {}, "source": ["### 0.2 所需硬件基础及Autodl租赁指南"]}, {"cell_type": "markdown", "id": "08809cf2-5c83-472f-ab27-d71b0bcb9a31", "metadata": {}, "source": ["我的设备为、AutoDL租赁的——\n", "\n", "```python\n", "####################################################\n", "####   4 x 64 vCPU Intel(R) Xeon(R) Gold 6430   ####\n", "####   RAM 4 x 120G                             ####\n", "####   4 x RTX4090, VRAM 4 x 24G                ####\n", "####   Hard Disk Drive 1000G                    ####\n", "####################################################\n", "```\n", "\n", "该设备的成本越为8.2r/小时、AutoDL支持的最大卡位是1机8卡、因此如果你和我一样使用4090，那你的成本最多是17r/小时。硬件无上限、当前代码支持A800、A100、RTX6000等显卡、如果你有更好的设备，也欢迎使用。\n", "\n", "**你所需要的最低硬盘要求与我一样是1000G，最次也要有6~700G，CPU和显卡要求是我的一半、这是能够运行多进程分布式的最低要求**。最低要求的租赁成本约为每小时4r，数据处理所需时长大约为60小时、在我所设置的超参数下里所需的训练时长大约也为60小时（5个epoch）。\n", "\n", "```python\n", "####################################################\n", "####   2 x 32 vCPU 型号任选                      ####\n", "####   RAM 2 x 80G                              ####\n", "####   2 x RTX4090, VRAM 2 x 24G                ####\n", "####   Hard Disk Drive 1000G                    ####\n", "####################################################\n", "```\n", "\n", "你可以通过缩小模型尺寸、或者缩小所使用的数据尺寸来完成整个训练流程、这样可以大幅缩减你所需的数据处理和训练时间。另外，**也可选择按量计费中的无卡模式开机（每小时只需0.1r，硬盘依然可以使用）**，无卡模式下不会启动GPU、配备的cpu等级也比较低、因此价格十分便宜，你可以在这个模式下先确定所有代码都到位、可尝试在此基础上进行数据预处理、代码编写、上传下载文件等等占用时间的操作。\n", "\n", "同时，在选择环境及基础配置时我选择的是 ↓\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/117.png)\n", "\n", "这一选择下默认的python是12.3版本，但是cuda版本比较合适。\n", "\n", "- <font color=\"red\">**什么时候需要开始租赁设备呢？租设备流程在哪里？**</font>\n", "\n", "建议是可以把课程听完、对整个流程有自己的认知后、再租赁设备、照着课件开始一步步进行运行。当然，如果你GPU预算十分充足、也可以跟着课程一起开启GPU。\n", "\n", "租设备流程看【第二阶段 环境搭建与分布式预训练】中的这两节内容 ↓ 但需要注意的是，这两节内容中已对环境进行了一些配置，在我们进行deepseekv3预训练的时候我们会重新进行配置，因此你只需要按照这个流程租好设备、设置好final shell等流程即可，**无需follow之前课程中对环境进行的配置**。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/118.png)\n", "\n", "到这个环节就好了 ↓ 无需配置requirements.txt。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/120.png)"]}, {"cell_type": "markdown", "id": "0245aff4-8f2b-4f73-86b6-b35401fa3fd9", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "c84e31d6-52fb-41ea-89e4-24043c9aa6c2", "metadata": {}, "source": ["## 1 DeepSeekv3 mini参数设置与技术选型"]}, {"cell_type": "markdown", "id": "38388650-2c5a-4334-977c-b042bd99ccc4", "metadata": {}, "source": ["### 1.1 技术框架与训练框架选型"]}, {"cell_type": "markdown", "id": "13ec2243-84f4-484a-a1ca-4d62c686cd4f", "metadata": {}, "source": ["在如此低的硬件设备要求下，我们是如何实现工业中的训练流程的？在工业中，我们通常有以下三种模型规模与尺寸划分 ↓\n", "\n", "| **模型规模**         | **1B~7B** | **7B~72B**  | **72B以上**  |\n", "|----------------------|--------------------------------------|--------------------------------------|--------------------------------------|\n", "| **数据 Token 量**    | **100B~750B tokens** | **750B ~ 10T tokens** | **10T~15T** |\n", "| **数据存储存储需求**   | **400GB~3TB** | **3TB~30TB** | **30TB~45TB** |\n", "\n", "在这样的规模划分下，通常所使用的技术框架有 ↓\n", "\n", "| 训练流程 | 适用于1B-7B模型<br>（数据400G-3T）      | 适用于7B~72B模型<br>（数据3T-30TB）   | 备注 |\n", "|----------------------|----------------------|----------------------|----------------------|\n", "| **数据获取方式**      | 手动下载      |hfd+Aria2+git-lfs+jq+Ray<br>多线程并行拉取/分布式拉取  | --- |\n", " | **数据存储方式**  | 硬盘存储<br>允许是csv等格式 | 服务器存储、硬盘或NAS、S3、Ceph，存储格式jsonl/bin | 如果数据量更大、可以补充Apache Parquet + Hive Metastore框架|\n", "| **数据处理框架**      | polars、json          | HuggingFaceDatasets、PyArrow、Json、使用PackedDataset等方式 | --- |\n", "| **数据清理与去重**    | 正则匹配、长短过滤、人工检查          | DataJucier |---|\n", "| **Tokenization** | BPE + tiktoken         | 通常自定义BPE Tokenizer| --- |\n", "| **训练框架**         | PyTorch、Huggingface Transformers | 单机多卡用DeepSpeed、Megatron-LM、多机多卡用FSDP、Colossal-AI | --- |\n", "| **训练并行策略**      | 无并行或数据并行 | 3D 并行（TP+PP+DP）+<br> EP + ZeRO系列并行 |---|\n", "| **混合精度训练**      | 无（FP32）             | FP16 / BF16 / FP8<br>低精度优化（QLoRA） | --- |\n", "| **优化器**           | AdamW               | 单机多卡 Adamw、多机多卡FSDP Optimizer |---  |\n", "| **推理加速框架**     | 无                     | TensorRT-LLM、vLLM、Triton | ---  |\n", "| **部署方式**         | 单机或ONNX Runtime 部署 | 多节点/多卡分布式推理<br>（TensorRT-LLM + vLLM）      | --- |\n", "| **模型存储格式**     | PyTorch .pt或HF Transformers Format| Safetensors或GGUF | ---|\n", "\n", "---\n", "<br>\n", "<center><b>本次在课程中，我们实现的是<font color = \"red\">单机多卡分布式、支持7~72B模型训练</font>的大型模型训练流程，我们实现的模型尺寸为<font color = \"red\">0.3B DeepSeekv3 mini，为支持MTP的自定义DeepSeekv3模型，</font>我们是所使用的数据规模为<font color = \"red\">200G原始数据</font>。我们使用的是<font color = \"red\">小尺寸模型 + 大训练框架的组合策略</font>、在保证大家能够学到行业实际训练流程的同时、尽量压缩所需的算力和训练成本。</b></center>\n", "<br>\n", "\n", "---\n", "\n", "故而，**在本次预训练流程中、我们最终所使用的技术选型如下 ↓**"]}, {"cell_type": "markdown", "id": "8441aac6-17b6-49b5-a0d8-5ac7575a143d", "metadata": {}, "source": ["| 训练流程 | 适用于1B-7B模型<br>（数据400G-3T）      | 适用于7B~72B模型<br>（数据3T-30TB）   | 备注 |\n", "|----------------------|----------------------|----------------------|----------------------|\n", "| **数据获取方式**      | 手动下载      |<font color=\"red\">**hfd+Aria2+git-lfs+jq+Ray<br>多线程并行拉取/分布式拉取**  | --- |\n", " | **数据存储方式**  | 硬盘存储<br>允许是csv等格式 | <font color=\"red\">**服务器存储、硬盘**</font>或NAS、S3、Ceph<font color=\"red\">**存储格式jsonl/bin**</font> | 如果数据量更大、可以补充Apache Parquet + Hive Metastore框架|\n", "| **数据处理框架**      | polars、json          | <font color=\"red\">**HuggingFaceDatasets、PyArrow、Json、使用PackedDataset等方式** | --- |\n", "| **数据清理与去重**    | 正则匹配、长短过滤、人工检查          | <font color=\"red\">**DataJucier** |---|\n", "| **Tokenization** | BPE + tiktoken         | <font color=\"red\">**DeepSeek开源tokenizer**| --- |\n", "| **训练框架**         | PyTorch、Huggingface Transformers | <font color=\"red\">**单机多卡用DeepSpeed**</font>、Megatron-LM、多机多卡用FSDP、Colossal-AI | Megatron不支持ZeRO-1并行因此最终还是选择DS，另外我们将额外提供一套FSDP代码供大家学习和参考 |\n", "| **训练并行策略**      | 无并行或数据并行 | <font color=\"red\">**3D 并行（TP+PP+DP）+<br> EP + ZeRO系列并行** |---|\n", "| **混合精度训练**      | 无（FP32）             | <font color=\"red\">**FP16 / BF16**</font> / FP8<br><font color=\"red\">**各类低精度优化**</font> | --- |\n", "| **优化器**           | AdamW               | <font color=\"red\">**单机多卡 Adamw**</font>、多机多卡FSDP Optimizer |---  |\n", "| **推理加速框架**     | 无                     | <font color=\"red\">**TensorRT-LLM、vLLM、Triton** | ---  |\n", "| **部署方式**         | 单机或ONNX Runtime 部署 | 多节点/<font color=\"red\">**多卡分布式推理<br>TensorRT-LLM + vLLM**      | --- |\n", "| **模型存储格式**     | PyTorch .pt或HF Transformers Format| <font color=\"red\">**Safetensors或GGUF** | ---|"]}, {"cell_type": "markdown", "id": "d601c61b-c416-4df9-9c1b-7dc869b62e00", "metadata": {}, "source": ["### 1.2 架构梳理与模型超参数设置"]}, {"cell_type": "markdown", "id": "7b5f0e4e-9e1a-4826-826c-fa0013406309", "metadata": {}, "source": ["本次训练的DeepSeekV3架构是基于DeepSeekv3开源技术报告、以及DeepSeekV3在Huggingface页面所公开的推理模型所构建——\n", "\n", "<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/73-.png\" alt=\"image-20250107200502389\" style=\"zoom:30%;\" />\n", "\n", "在课程《Part 1 DeepSeekv3深度原理与架构精讲》中我们已详细讲解过该架构。在这个架构中、我们实现了MLA、LoRA KV缓存、DeepSeekMOE（带辅助损失）、支持动态长文本的YARN、专家并行、张量并行、管道并行、ZeRO-1数据并行、以及MTP（Multi-Token Prediction）。受技术限制没能实现的是DeepSeekMOE改进的偏置免负载均衡技术、DualPipe、FP8量化处理以及PF8量化后的一系列精度提升操作。\n", "\n", "最终我们实现的模型架构存储在文件`deepseekv3_mtp_model.py`中，且配备的参数如下 ↓"]}, {"cell_type": "markdown", "id": "eec0631a-cf2e-42e3-ac33-61de950cf426", "metadata": {}, "source": ["```python\n", "@dataclass\n", "class ModelArgs(PretrainedConfig):\n", "    \"\"\"\n", "    0.3B - Data class for defining model arguments and hyperparameters.\n", "    \"\"\"\n", "    def __init__(self, **kwargs):\n", "        super().__init__(**kwargs)\n", "        # 增加kwargs.get以兼容代码中的属性引用\n", "        self.max_batch_size = kwargs.get(\"max_batch_size\", 8)\n", "        self.max_seq_len = kwargs.get(\"max_seq_len\", 4096)\n", "        self.dtype = kwargs.get(\"dtype\", \"bf16\")\n", "        self.vocab_size = kwargs.get(\"vocab_size\", 128000) # 按照deepseek的tokenizer重新修改\n", "        self.dim = kwargs.get(\"dim\", 1024) \n", "        self.inter_dim = kwargs.get(\"inter_dim\", 2048)  \n", "        self.moe_inter_dim = kwargs.get(\"moe_inter_dim\", 512) #MoE的inter_dim\n", "        self.n_layers = kwargs.get(\"n_layers\", 8) \n", "        self.n_dense_layers = kwargs.get(\"n_dense_layers\", 1)\n", "        self.n_heads = kwargs.get(\"n_heads\", 8) \n", "        # MoE\n", "        self.use_moe = kwargs.get(\"use_moe\", 1)\n", "        self.n_routed_experts = kwargs.get(\"n_routed_experts\", 4) #必须是gpu的倍数 \n", "        self.n_shared_experts = kwargs.get(\"n_shared_experts\", 1)  \n", "        self.n_activated_experts = kwargs.get(\"n_activated_experts\", 2) \n", "        self.n_expert_groups = kwargs.get(\"n_expert_groups\", 1)\n", "        self.n_limited_groups = kwargs.get(\"n_limited_groups\", 1)\n", "        self.score_func = kwargs.get(\"score_func\", \"softmax\")\n", "        self.route_scale = kwargs.get(\"route_scale\", 1.0)\n", "        # MLA\n", "        self.q_lora_rank = kwargs.get(\"q_lora_rank\", 0)\n", "        self.kv_lora_rank = kwargs.get(\"kv_lora_rank\", 256)  \n", "        self.qk_nope_head_dim = kwargs.get(\"qk_nope_head_dim\", 64)  \n", "        self.qk_rope_head_dim = kwargs.get(\"qk_rope_head_dim\", 32)  \n", "        self.v_head_dim = kwargs.get(\"v_head_dim\", 64)\n", "        # YARN\n", "        self.original_seq_len = kwargs.get(\"original_seq_len\", 4096)\n", "        self.rope_theta = kwargs.get(\"rope_theta\", 10000.0)\n", "        self.rope_factor = kwargs.get(\"rope_factor\", 40)\n", "        self.beta_fast = kwargs.get(\"beta_fast\", 32)\n", "        self.beta_slow = kwargs.get(\"beta_slow\", 1)\n", "        self.mscale = kwargs.get(\"mscale\", 1.0)\n", "        # MTP\n", "        self.n_mtp_depths = kwargs.get(\"n_mtp_depths\", 2)\n", "```\n", "\n", "这是更多小伙伴可以跑的0.15B参数模型的配置 ↓\n", "\n", "```python\n", "class ModelArgs(PretrainedConfig):\n", "    \"\"\"\n", "    0.15B - Data class for defining model arguments and hyperparameters.\n", "    \"\"\"\n", "    def __init__(self, **kwargs):\n", "        super().__init__(**kwargs)\n", "        # 增加kwargs.get以兼容代码中的属性引用\n", "        self.max_batch_size = kwargs.get(\"max_batch_size\", 8)\n", "        self.max_seq_len = kwargs.get(\"max_seq_len\", 2048)\n", "        self.dtype = kwargs.get(\"dtype\", \"bf16\")\n", "        self.vocab_size = kwargs.get(\"vocab_size\", 128000) # 按照deepseek的tokenizer重新修改\n", "        self.dim = kwargs.get(\"dim\", 512) \n", "        self.inter_dim = kwargs.get(\"inter_dim\", 1024)  \n", "        self.moe_inter_dim = kwargs.get(\"moe_inter_dim\", 256) #MoE的inter_dim\n", "        self.n_layers = kwargs.get(\"n_layers\", 4) \n", "        self.n_dense_layers = kwargs.get(\"n_dense_layers\", 1)\n", "        self.n_heads = kwargs.get(\"n_heads\", 8)\n", "        # MoE\n", "        self.use_moe = kwargs.get(\"use_moe\", 1)\n", "        self.n_routed_experts = kwargs.get(\"n_routed_experts\", 4) #必须是gpu的倍数 \n", "        self.n_shared_experts = kwargs.get(\"n_shared_experts\", 1)  \n", "        self.n_activated_experts = kwargs.get(\"n_activated_experts\", 2) \n", "        self.n_expert_groups = kwargs.get(\"n_expert_groups\", 1)\n", "        self.n_limited_groups = kwargs.get(\"n_limited_groups\", 1)\n", "        self.score_func = kwargs.get(\"score_func\", \"softmax\")\n", "        self.route_scale = kwargs.get(\"route_scale\", 1.0)\n", "        # MLA\n", "        self.q_lora_rank = kwargs.get(\"q_lora_rank\", 0)\n", "        self.kv_lora_rank = kwargs.get(\"kv_lora_rank\", 64)  \n", "        self.qk_nope_head_dim = kwargs.get(\"qk_nope_head_dim\", 64)  \n", "        self.qk_rope_head_dim = kwargs.get(\"qk_rope_head_dim\", 32)  \n", "        self.v_head_dim = kwargs.get(\"v_head_dim\", 64)\n", "        # YARN\n", "        self.original_seq_len = kwargs.get(\"original_seq_len\", 2048)\n", "        self.rope_theta = kwargs.get(\"rope_theta\", 10000.0)\n", "        self.rope_factor = kwargs.get(\"rope_factor\", 40)\n", "        self.beta_fast = kwargs.get(\"beta_fast\", 32)\n", "        self.beta_slow = kwargs.get(\"beta_slow\", 1)\n", "        self.mscale = kwargs.get(\"mscale\", 1.0)\n", "        # MTP\n", "        self.n_mtp_depths = kwargs.get(\"n_mtp_depths\", 1)\n", "```\n", "同时，我在`config.py`中提供了1B大小以及3B大小对应的参数，可供大家参考。需要注意的是，`config.py`中的参数无法直接导入、如果你需要使用特定模型的参数、则必须复制粘贴`config.py`中的类到模型文件中。"]}, {"cell_type": "markdown", "id": "e0695c42-b9b3-4086-b468-052d3d99b45f", "metadata": {}, "source": ["| **模型规模**         | **预估训练时间** |\n", "|----------------------|--------------------------------------|\n", "| **0.02B参数 + 30G数据 MateConvMini**    | 15个epoch，约18~22个小时，90分钟/epoch |\n", "| **0.15B参数 + 60G数据 MiniDeepSeek**   | 15个epoch，约4天时间，6~7小时/epoch |\n", "| **0.3B参数 + 100G数据 MiniDeepSeek**   | 10个epoch，约1周时间，14小时/epoch|\n", "| **0.3B参数 + 300G数据 MiniDeepSeek**   | 10个epoch，约2周时间，30小时/epoch|\n", "\n", "数据量直接关系到一个epoch中有多少需要处理的数据、因此数据量增加会让模型的训练时间线性增加。模型规模的增加就复杂得多，一般认为模型规模增加之后、训练时间不会线性增加、而会指数级 + 线性混合增加，因此模型变大之后所需的时间会爆炸性增长。"]}, {"cell_type": "markdown", "id": "2956541e-6609-4eb7-a447-50b9224aa0f8", "metadata": {}, "source": ["## 2 DeepSeekv3训练与数据处理环境搭建"]}, {"cell_type": "markdown", "id": "20c6bf0c-819f-4e7f-b12c-78dd85b44234", "metadata": {}, "source": ["- **数据预训练阶段所需全部文件**"]}, {"cell_type": "markdown", "id": "51920a93-8073-4d37-892e-5bd557bf9cec", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/119.png)"]}, {"attachments": {}, "cell_type": "markdown", "id": "1290a1ac-cfbd-4b8d-889f-cec0e11c7f82", "metadata": {}, "source": ["文件结构如下 ↓\n", "```python\n", "minideepseek/\n", "├── requirements_minids.txt      # 预训练所需的环境\n", "├── test_v3model.py              # 测试deepseev3_mtp_model.py能否跑通\n", "├── logger_utils.txt             # 进行logger控制和调试的代码\n", "├── pretrain.py                  # 预训练代码\n", "│\n", "├── model/                       # model文件夹、存放模型文件\n", "│   ├── config.py\n", "│   ├── convert.py\n", "│   ├── deepseev3_mtp_model.py   # 要训练的模型脚本，加上了mtp\n", "│   ├── fp8_cast_bf16.py\n", "│   ├── kernel.py\n", "│\n", "├── data/                        # data文件夹\n", "│   ├── print_datas.py           # 对下载好的数据进行状态打印的脚本\n", "│   ├── analyze_jsonl.py         # 查询各jsonl文件统计指标的脚本\n", "│   ├── data-juicer-main.zip     # data-juicer库安装所需文件\n", "│   ├── delete_files.sh          # 删除数据、降低数据大小的脚本\n", "│   ├── hfd_revised.sh           # 分布式并行数据下载脚本（防429报错版）\n", "│   ├── slimpajama_preclean.py   # slimpajama清理脚本\n", "│   ├── pdfprocess.py            # pdf批量处理流程\n", "│   ├── step1_pretrain_basic_dataprocess.py   # 数据处理第一步：JSONL化脚本\n", "│   ├── data_jucier/             # 在step2做所使用的数据处理工具\n", "│   ├── final_data/              # 存放最终处理好的数据    \n", "│   ├── step2/                                # 数据处理第二步：数据清洗脚本\n", "│   │   ├── minidsv3_starcoder.yaml           # 各个数据自身的yaml文件\n", "│   │   ├── minidsv3_text_ape.yaml\n", "│   │   ├── minidsv3_text_openr1.yaml\n", "│   │   ├── minidsv3_text_skypile.yaml\n", "│   │   ├── minidsv3_text_slimpajama.yaml\n", "│   │   ├── requirements_step2.txt           # 第二步运行所需的环境\n", "│   │   ├── run_step2_ape.sh                 # 各个数据上运行DJ的sh脚本\n", "│   │   ├── run_step2_openr1.sh\n", "│   │   ├── run_step2_skypile.sh\n", "│   │   ├── run_step2_slimpajama.sh\n", "│   │   ├── run_step2_starcoder.sh\n", "│\n", "├── tokenizer/                  # tokenizer文件夹\n", "│   ├── lit_gpt/                # 辅助打包的库lit_gpt\n", "│   ├── test_tokenizer.py       # 测试tokenizer是否正常运行的代码\n", "│   ├── tokenizer.json          # deepseek开源的tokenizer架构本身\n", "│   ├── tokenizer_config.json   # deepseek开源的tokenizer配置文件\n", "│   ├── step3_prepare_data_for_pretrain.py   # 数据处理第三步：Tokenizer脚本\n", "│   ├── step3_prepare_data_for_pretrain_upsample.py   # 将数据在tokenizer环节进行上采样的步骤\n", "│   ├── step4_dataset.py        # 将数据抽样、打包成bin文件的脚本\n", "\n", "```"]}, {"cell_type": "markdown", "id": "e0af4c34-a8da-43e6-bea7-f310fcc97ab3", "metadata": {}, "source": ["- **在服务器上创建虚拟环境**"]}, {"cell_type": "markdown", "id": "2e25bb65-8546-48ec-9913-34b1bdbe1d3b", "metadata": {}, "source": ["DeepSeekV3的训练分为数据处理、预训练、后训练（SFT微调+RL）共4个流程，在执行这4个流程时我们可能会涉及大量的libraries，为了避免各类libraries之间的冲突、我们往往会针对特定的流程建立特定的环境。在本次训练过程中，我将建立如下虚拟环境 ↓\n", "\n", "| 序号 | 环境名称                | 主要用途             | 依赖文件                 |\n", "|----|----------------------|------------------|----------------------|\n", "| 1  | `mnds_training`      | 预训练的主环境，对torch、triton、tiktoken版本都提出了特定要求        | `requirements_minids.txt` |\n", "| 2  | `mnds_data_clean_step2_` | 在数据预处理中、datajucier所运行的环境，对python版本、transformers版本有特定要求      | `requirements_step2.txt`  |\n", "| 3  | `mnds_sft`           | SFT 微调环境      | `requirements_sft.txt`    |\n", "| 4  | `mnds_grpo`          | 强化微调环境，对trl、python、transformers都提出了要求  | `requirements_grpo.txt`   |"]}, {"cell_type": "markdown", "id": "78471971-c5c5-4afa-9aa6-5a394180c620", "metadata": {}, "source": ["这些环境共同存在于服务器上、与root环境区分开来，当我们执行不同的任务时，我们需要在不同的环境之间切换。不过，所有这些环境都共享相同的硬盘和文件存储空间，因此无论你在哪个虚拟环境里，你都可以访问特定的数据和脚本文件、只是启用这些脚本的环境和python不同。"]}, {"cell_type": "markdown", "id": "0afd5204-3069-4e26-af89-2b2ab12d05e1", "metadata": {}, "source": ["当你打开finalshell、已经进入你的服务器后，就可以开始部署环境了。本次我们先部署mnds_training和mnds_data_clean_step2两个环境 ↓"]}, {"cell_type": "markdown", "id": "550d38bc-a542-4178-9abc-cf1f67c302e5", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/120.png)"]}, {"cell_type": "markdown", "id": "f9cfab4b-1369-4d66-a635-0d77559b7b32", "metadata": {}, "source": ["- **基础训练环境创建**\n", "\n", "```shell\n", "# 创建minideepseek与v3两层目录\n", "mkdir ~/autodl-tmp/minideepseek/\n", "cd ~/autodl-tmp/minideepseek\n", "mkdir ~/autodl-tmp/minideepseek/v3\n", "cd ~/autodl-tmp/minideepseek/v3\n", "\n", "# 建立虚拟环境mnds_training\n", "# 用source激活该环境\n", "# 该环境位于v3目录下，每次要激活该环境时必须先回到v3目录下\n", "python3 -m venv mnds_training\n", "source mnds_training/bin/activate\n", "```\n", "\n", "确认自己在v3目录中后、手动将本地目录中的data、model、requirements_miniminds、test_v3model等文件全部上传。注意，不要直接上传整个minideepseek文件夹、会破坏相应的环境。\n", "\n", "```shell\n", "# mnds_training环境配置\n", "# 其中requirements_minids.txt在v3目录下\n", "pip install --upgrade pip\n", "pip install --upgrade pip setuptools wheel\n", "pip install -r requirements_minids.txt -i https://pypi.tuna.tsinghua.edu.cn/simple\n", "\n", "# 检验模型能否顺利运行\n", "touch test_v3model.py\n", "python test_v3model.py\n", "```"]}, {"cell_type": "markdown", "id": "97a3f706-d263-4869-9ded-98914fe43023", "metadata": {}, "source": ["得到如下结果后表示模型能够在当前环境下运行、还能够看到当前模型的总参数量 ↓\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/121_.png)"]}, {"cell_type": "markdown", "id": "5400551d-0217-4889-8472-82843f89374e", "metadata": {}, "source": ["- **data_jucier专用数据预处理环境搭建**"]}, {"cell_type": "markdown", "id": "76bdb519-d9ef-4916-ba2b-5bde84a3bf76", "metadata": {}, "source": ["```shell\n", "# 先退出原本的mnds_training环境\n", "# 回到~/autodl-tmp/minideepseek/v3\n", "deactivate\n", "cd ~/autodl-tmp/minideepseek/v3\n", "\n", "# 确保你有python3.10的环境\n", "sudo apt update\n", "sudo apt install python3.10 python3.10-venv python3.10-dev\n", "\n", "# 建立虚拟环境 - 数据清洗用环境mnds_data_clean_step2_\n", "python3.10 -m venv mnds_data_clean_step2_\n", "source mnds_data_clean_step2_/bin/activate\n", "\n", "# 设置huggingface镜像\n", "export HF_ENDPOINT=https://hf-mirror.com\n", "\n", "# 确保你的data目录下已上传step2所有文件\n", "# 配置mnds_data_clean_step2环境\n", "pip install --upgrade pip\n", "pip install --upgrade pip setuptools wheel\n", "sudo apt-get install -y build-essential cmake\n", "\n", "# 安装环境requirements_step2.txt\n", "pip install -r ~/autodl-tmp/minideepseek/v3/data/step2/requirements_step2.txt -i https://pypi.tuna.tsinghua.edu.cn/simple\n", "\n", "# 确保你的data/step2目录下已上传data-jucier-main.zip\n", "# 配置data-jucier\n", "cd ~/autodl-tmp/minideepseek/v3/data\n", "unzip data-juicer-main.zip\n", "mv data-juicer-main data-juicer\n", "cd ~/autodl-tmp/minideepseek/v3/data/data-juicer\n", "pip install -v -e .\n", "\n", "# 建立存储数据新目录\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/deep_clean\n", "\n", "# 建立缓存目录\n", "mkdir /root/autodl-tmp/minideepseek/v3/data/data_jucier_cache\n", "mkdir /root/autodl-tmp/minideepseek/v3/data/data_jucier_cache/temp\n", "\n", "```"]}, {"cell_type": "markdown", "id": "7ae76948-6efc-485c-b334-fafd023de5d5", "metadata": {}, "source": ["## 3 巨量数据构建与并行下载实战"]}, {"cell_type": "markdown", "id": "31c561b6-486d-4b1c-8168-37e59ce4661e", "metadata": {}, "source": ["### 3.1 DeepSeekV3预训练数据的组成"]}, {"cell_type": "markdown", "id": "80bb8fdf-a9b5-4d16-8967-be677ed6e5f7", "metadata": {}, "source": ["在DeepSeekv3构建过程中，我们使用了58%中文、21%英文及其他语言、15%代码、5%数学的方式进行构建，总数据集大小约为200G。关于数据的组合、DeepSeekV3本身是这样确定的 ↓\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/122.png)\n", "\n", "为了降低训练成本，我们选择了在数据集打包时就已经经过一定清洗的数据、而没有选择完全没清洗过的数据。当然、这些数据还要再经过清洗流程才能够使用、但耗费的时间与人力会远远低于直接在raw data上进行清洗。\n", "\n", "| 数据集编号 | 数据集名称                 | 数据属性 | 数据量级           | 存储格式         | 是否经过数据清洗 |\n", "|-----------|----------------------------|--------------------------------|--------------------|------------------|------------------|\n", "| 1         | Skypile-150B                | 中文文本                       | 117G/620GB             | JSONL            | 是               |\n", "| 2         | SlimPajama                   | 以英文为主的混合语言文本        | 42G/1TB               | JSONL（压缩）    | 是               |\n", "| 3         | Starcoder                    | 代码数据                        | 30GB/768GB    | Parquet          | 是               |\n", "| 4         | OpenR1Math                   | 数学CoT数据                        | 8G+            | JSONL            | 是               |\n", "| 5         | APE210K                      | 中文数学CoT数据                    | 49MB | JSONL            | 是               |\n", "| *6         | FinPDFs                      | 从一系列数据分析报告/财报rawdata中提取出的数据                    | 50GB | 多模态PDFs            | 否               |"]}, {"cell_type": "markdown", "id": "f709b051-d1c2-47b9-86ae-fd7b9539f683", "metadata": {}, "source": ["由于每个数据集的存储格式不同、因此数据集所占用的内存大小本身并不完全代表数据量的大小，但依照存储的格式来判断，通常来说数据量是 csv < Parquet < JSON < JSONL < Pyarrow Parquets）。由于尺寸不同、因此我们一般会参考数据所含的tokens总量。**但是在将数据转化成标准化的jsonl之前，我们很难确定单一jsonl文件所含的tokens总量是多少，我们甚至很难确认我们下载的文件本身的大小是多少**，因此在数据下载完成后，你可以在下载好的目录下方进行下面的命令、来查看你具体下载了多少数据。"]}, {"cell_type": "markdown", "id": "9b6efa39-f458-4478-84ae-124ff23171b4", "metadata": {}, "source": ["```shell\n", "cd /root/autodl-tmp/minideepseek/v3/data\n", "du -sh */\n", "```"]}, {"cell_type": "markdown", "id": "b2e31362-ec35-41a6-925d-0f5bbe6b4433", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "61b9d886-f60f-432e-a112-a0d2a07b48a5", "metadata": {}, "source": ["### 3.2 Hu<PERSON><PERSON>的申请"]}, {"cell_type": "markdown", "id": "fa3a438f-0d6c-462f-a687-2d7231fb9526", "metadata": {}, "source": ["**首先要在数据页面进行email注册**（不一定所有数据都需要，但是starcoder需要）——\n", "\n", "> ![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/17.png)"]}, {"cell_type": "markdown", "id": "6800def9-64fc-4e8b-86ed-2c5399697ed5", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "22caf3a2-2339-4105-b245-2cc86ab529b6", "metadata": {}, "source": ["然后要进行权限申请，不仅要针对代码类数据进行特定的token申请，还需要针对普通数据进行下载类token的申请。"]}, {"cell_type": "markdown", "id": "3eeac959-0781-4a5a-b96a-8bc51f02948f", "metadata": {}, "source": ["代码数据一般都是量非常大的数据、其特点不仅在于数据量大、还需要我们规定特定的编程语言进行下载，因此CLI代码会与传统文字数据集略有差别。首先，代码数据集的下载会需要权限、因此我们要先申请相应的access token——"]}, {"cell_type": "markdown", "id": "088c9d9b-8301-49b4-8d11-609c4391cc73", "metadata": {}, "source": ["1. **挂上梯子、注册Huggingface账号**👉https://huggingface.co/\n", "\n", "2. **登录后、在个人profile页面找到自己的username**\n", "\n", "> - 点击右上角——\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/51.png)\n", "\n", "> - 复制username——\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/52.png)\n", "\n", "3. **登录后、在这个页面下建立属于自己的AccessToken**👉https://huggingface.co/settings/tokens"]}, {"cell_type": "markdown", "id": "fb4dd6ca-d785-48d1-af01-50efba15ca9e", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/53.png)"]}, {"cell_type": "markdown", "id": "4f248c60-5934-415a-8366-12cd2a66e707", "metadata": {}, "source": ["4. **在下面的页面中、建立token名字、并且为所需要的数据集申请权限**"]}, {"cell_type": "markdown", "id": "8d773b19-243b-42e3-8745-564c4f4fdd92", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/54_.png)"]}, {"cell_type": "markdown", "id": "e392d7ac-f508-48be-b2ec-7207c3e700e5", "metadata": {}, "source": ["点击create token、获得token后直接复制，**你将不会有第二次复制token的机会、因此务必要在这个时候复制token**。"]}, {"cell_type": "markdown", "id": "5a211940-c5d1-4a72-a042-930e99a2e792", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/56.png)"]}, {"cell_type": "markdown", "id": "27276b7f-fb2f-4bf5-ac13-f81aaec62699", "metadata": {}, "source": ["token建好后，可以在token页面中看到、但此时你只能看到你的token名称、将不能再复制具体的access token码了。"]}, {"cell_type": "markdown", "id": "fb694681-d3ba-40cb-9cb4-82c48f39255c", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/57.png)"]}, {"cell_type": "markdown", "id": "d5b4ee6b-0903-4f44-9b48-9f76b2ed480a", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "d0e4f79f-ac29-49c4-8f6c-9a5033f6c1ae", "metadata": {}, "source": ["### 3.3 预训练数据的多线程并行下载"]}, {"cell_type": "markdown", "id": "b28dce08-1606-4b57-888c-2a15d5263c17", "metadata": {}, "source": ["在开始下载之前、请务必确保你的data文件夹中已经有了hfd_revised.sh文件，你需要手动上传它，它是经过修改后、专门用来防止429报错的脚本。"]}, {"cell_type": "markdown", "id": "0d0575f2-f24f-4f31-87e6-3c138faa82bb", "metadata": {}, "source": ["```shell\n", "# 先退出虚拟环境\n", "# 回到~/autodl-tmp/minideepseek/v3\n", "# 激活训练环境，下载的数据和数据处理第一阶段都是在训练环境中运行\n", "deactivate\n", "cd ~/autodl-tmp/minideepseek/v3\n", "source mnds_training/bin/activate\n", "\n", "# 创建v3下方一系列目录\n", "#mkdir ~/autodl-tmp/minideepseek/v3/model\n", "#mkdir ~/autodl-tmp/minideepseek/v3/data\n", "\n", "# 进入data目录、准备开始下载数据\n", "cd ~/autodl-tmp/minideepseek/v3/data\n", "\n", "# 设置镜像站\n", "export HF_ENDPOINT=https://hf-mirror.com\n", "\n", "#下载hfd.sh文件到当前的目录\n", "wget https://hf-mirror.com/hfd/hfd.sh\n", "\n", "#文件hfd.sh被赋予执行权限\n", "chmod a+x hfd.sh                                    \n", "\n", "#更新软件包索引\n", "sudo apt update\n", "\n", "#安装aria2c\n", "sudo apt install aria2\n", "\n", "#安装git-lfs\n", "sudo apt install git-lfs \n", "\n", "#安装jq辅助下载\n", "sudo apt install jq -y \n", "\n", "# 设置目录文件\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/skypile          \n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/slimpajama\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/starcoder\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/openr1\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/ape210k\n", "\n", "# 确保hfd_revised.sh在~/autodl-tmp/minideepseek/v3/data目录下\n", "# 赋予hfd_revised.sh调用权限\n", "chmod a+x hfd_revised.sh\n", "```"]}, {"cell_type": "markdown", "id": "a151863f-a2f7-460c-85e2-5c6459e2ac82", "metadata": {}, "source": ["#### 3.3.1 多线程并行下载脚本"]}, {"cell_type": "markdown", "id": "48305fae-2fc6-4388-b389-e167e80116e9", "metadata": {}, "source": ["- **skypile**：https://huggingface.co/datasets/Skywork/SkyPile-150B/"]}, {"cell_type": "markdown", "id": "c6adb493-a076-4f0c-9cf4-b18dc213c359", "metadata": {}, "source": ["你可以用正则表达式选择你要下载的文件 ↓"]}, {"cell_type": "markdown", "id": "97b75ae0-72d7-4793-a3fe-3d3ead3450e0", "metadata": {}, "source": ["```shell\n", "# 只下载一部分、包括243G文件\n", "# 注意更换你自己的hf token ip\n", "#【Time Warning：4线程、5文件并行条件下3小时】\n", "#【确保你的系统可以执行4线程、5文件并行拉取】\n", "./hfd_revised.sh Skywork/SkyPile-150B --dataset \\\n", "  --include \"data/2023-14_zh_middle_*.jsonl\" \\\n", "  --include \"data/2023-14_zh_head_*.jsonl\" \\\n", "  --include \"data/2023-06_zh_middle_*.jsonl\" \\\n", "  --include \"data/2023-06_zh_head_*.jsonl\" \\\n", "  --include \"data/2022-49_zh_middle_*.jsonl\" \\\n", "  --include \"data/2022-49_zh_head_*.jsonl\" \\\n", "  --include \"data/2022-40_zh_middle_*.jsonl\" \\\n", "  --include \"data/2022-40_zh_head_*.jsonl\" \\\n", "  --include \"data/2022-33_zh_middle_*.jsonl\" \\\n", "  --include \"data/2022-33_zh_head_*.jsonl\" \\\n", "  --include \"data/2022-27_zh_middle_*.jsonl\" \\\n", "  --include \"data/2022-27_zh_head_*.jsonl\" \\\n", "  --include \"data/2022-21_zh_middle_*.jsonl\" \\\n", "  --include \"data/2022-21_zh_head_*.jsonl\" \\\n", "  --include \"data/2022-05_zh_middle_*.jsonl\" \\\n", "  --include \"data/2022-05_zh_head_*.jsonl\" \\\n", "  --hf_username TsaiTsai0929 \\\n", "  --hf_token hf_xxxxxx \\\n", "  --tool aria2c \\\n", "  -x 4 -j 5\\\n", "  --local-dir ~/autodl-tmp/minideepseek/v3/data/skypile\n", "```"]}, {"cell_type": "markdown", "id": "f3f84b30-3459-46bc-b9e1-fd3f28075617", "metadata": {}, "source": ["下载完毕之后目录显示为`/root/autodl-tmp/minideepseek/v3/data/skypile/data`，自带一层更深的data目录，具体数据为 ↓"]}, {"cell_type": "markdown", "id": "ee8de140-9a73-41a5-9fe9-8cfc5d5cdea1", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/124.png)"]}, {"cell_type": "markdown", "id": "27a92fb2-ecf6-4d1f-b6c8-e997e9c2a599", "metadata": {}, "source": ["- **<PERSON>**：https://huggingface.co/datasets/cerebras/SlimPajama-627B/"]}, {"cell_type": "markdown", "id": "fb17ba6b-5320-4912-bd96-fcedd8cc0aa8", "metadata": {}, "source": ["slimpajama单一数据太小、启用多线程或文件并行很快就会触发429报错、因此只能单一文件缓缓下载。\n", "\n", "```shell\n", "# 只下载一部分、包括178G文件\n", "# 你可以根据自己的需求调整所需的目录\n", "#【Time Warning：10小时】\n", "./hfd_revised.sh cerebras/SlimPajama-627B --dataset \\\n", "  --include \"train/chunk1/*\" \\\n", "  --include \"train/chunk2/*\" \\\n", "  --include \"train/chunk3/*\" \\\n", "  --hf_username TsaiTsai0929 \\\n", "  --hf_token hf_xxxxxx \\\n", "  --tool aria2c \\\n", "  -x 1 -j 1 \\\n", "  --local-dir ~/autodl-tmp/minideepseek/v3/data/slimpajama\n", "```"]}, {"cell_type": "markdown", "id": "4eb03215-1c89-4be2-bc49-01887cb2067b", "metadata": {}, "source": ["下载完毕之后目录显示为`/root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1`，自带更深的train/chunk1目录，具体数据为 ↓"]}, {"cell_type": "markdown", "id": "abd4c6d5-af96-4c58-9d4f-9aa7a8d09a68", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/125.png)"]}, {"cell_type": "markdown", "id": "1b6dd9e1-33cc-4bab-8f34-a44796383cfe", "metadata": {}, "source": ["- **Starcoder**：https://huggingface.co/datasets/bigcode/starcoderdata"]}, {"cell_type": "markdown", "id": "7ad6f1bd-fbd8-48ba-a8cc-2a1350c67940", "metadata": {}, "source": ["```shell\n", "##########################################################\n", "## 该数据下载之前、务必进行email注册、以及申请单独的token  ##\n", "##########################################################\n", "\n", "# 下载部分数据、选择了大约16种语言，大约150G\n", "#【Time Warning：4线程、5并行下2小时】\n", "#【确保你的系统可以执行4线程、5文件并行拉取】\n", "./hfd_revised.sh bigcode/starcoderdata --dataset \\\n", "  --include \"python/*\" \\\n", "  --include \"sql/*\" \\\n", "  --include \"matlab/*\" \\\n", "  --include \"javascript/*\" \\\n", "  --include \"java/*\" \\\n", "  --include \"json/*\" \\\n", "  --include \"c/*\" \\\n", "  --include \"rust/*\" \\\n", "  --include \"go/*\" \\\n", "  --include \"typescript/*\" \\\n", "  --include \"kotlin*\" \\\n", "  --include \"swift/*\" \\\n", "  --include \"julia/*\" \\\n", "  --include \"markdown/*\" \\\n", "  --include \"html/*\" \\\n", "  --hf_username TsaiTsai0929 \\\n", "  --hf_token hf_xxxx \\\n", "  --tool aria2c \\\n", "  -x 4 -j 5 \\\n", "  --local-dir ~/autodl-tmp/minideepseek/v3/data/starcoder\n", "```"]}, {"cell_type": "markdown", "id": "8ffdc921-77e8-4bf6-9098-419953393b1a", "metadata": {}, "source": ["下载完毕之后目录显示为`/root/autodl-tmp/minideepseek/v3/data/starcoder/`，目录下会有大量编程语言独立的文件夹，具体数据为 ↓"]}, {"cell_type": "markdown", "id": "11b08054-8757-448a-8c6a-2c5262b14aa6", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/126.png)"]}, {"cell_type": "markdown", "id": "1f082796-8135-4299-9709-28a2904ef256", "metadata": {}, "source": ["在每个编程文件夹的内部，会看到parquet文件 ↓"]}, {"cell_type": "markdown", "id": "a7c16206-2b0d-4622-b248-c2fb2095417b", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/127.png)"]}, {"cell_type": "markdown", "id": "0b8cf017-4bf1-4684-b9d1-cdd05e55881d", "metadata": {}, "source": ["- **OpenR1-Math-220K**：https://huggingface.co/datasets/open-r1/OpenR1-Math-220k"]}, {"cell_type": "markdown", "id": "3f612bad-7c7d-4170-81f5-2bdcf5015b60", "metadata": {}, "source": ["```shell\n", "#【Time Warning：4线程、5并行下5分钟】\n", "./hfd_revised.sh open-r1/OpenR1-Math-220k --dataset \\\n", "  --include \"all/*\" \\\n", "  --hf_username TsaiTsai0929 \\\n", "  --hf_token hf_xxxx \\\n", "  --tool aria2c \\\n", "  -x 4 -j 5 \\\n", "  --local-dir ~/autodl-tmp/minideepseek/v3/data/openr1\n", "```"]}, {"cell_type": "markdown", "id": "3e31a0bf-3cad-4b76-ad29-773975e38a5e", "metadata": {}, "source": ["下载完毕之后目录显示为`/root/autodl-tmp/minideepseek/v3/data/openr1/all`，自带更深一层的目录all，具体数据为 ↓"]}, {"cell_type": "markdown", "id": "7ec448ae-6d06-48ee-aec4-318f01295cea", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/128.png)"]}, {"cell_type": "markdown", "id": "4b430191-2963-41c0-8df1-c5b8fdd53944", "metadata": {}, "source": ["- **APE220K**：https://huggingface.co/datasets/open-r1/OpenR1-Math-220k"]}, {"cell_type": "markdown", "id": "e87c847b-2e41-4b69-9ce2-5fde932f2edf", "metadata": {}, "source": ["```shell\n", "#【Time Warning：10s】\n", "./hfd_revised.sh MU-NLPC/Calc-ape210k --dataset \\\n", "  --include \"data/*\" \\\n", "  --hf_username TsaiTsai0929 \\\n", "  --hf_token hf_xxxx \\\n", "  --tool aria2c \\\n", "  -x 4 -j 5 \\\n", "  --local-dir ~/autodl-tmp/minideepseek/v3/data/ape210k\n", "```"]}, {"cell_type": "markdown", "id": "8480618a-24da-4bb5-b91f-74fa6aa698b8", "metadata": {}, "source": ["下载完毕之后目录显示为`/root/autodl-tmp/minideepseek/v3/data/ape210k/data`，自带更深一层的目录data，具体数据为 ↓"]}, {"cell_type": "markdown", "id": "4233df56-88fb-465f-ad7b-ae29b476471c", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/129.png)"]}, {"cell_type": "markdown", "id": "37bbc668-c6ae-4e56-b738-b50c48a28906", "metadata": {}, "source": ["#### 3.3.2 下载后数据量与比例控制\n", "\n", "下载完成后、你可以通过下面的代码来控制实际数据量 ↓"]}, {"cell_type": "markdown", "id": "00a2c366-ac7c-4051-acf8-321a1030f9d3", "metadata": {}, "source": ["```shell\n", "# 进入data文件夹\n", "cd ~/autodl-tmp/minideepseek/v3/data\n", "\n", "# 查看这个目录下的一级文件夹所占内存大小\n", "du -h --max-depth=1 ~/autodl-tmp/minideepseek/v3/data\n", "```\n", "\n", "这样你就可以看到 ↓ **由于我已经对数据进行了筛选，因此你刚下载时看到的数据大小会与我截图中不一致**。"]}, {"cell_type": "markdown", "id": "47ca9e69-a2ab-4788-b9d5-201ebcf4dd52", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/130.png)"]}, {"cell_type": "markdown", "id": "7bd7c13b-11e0-443d-b0b1-56d81d87d596", "metadata": {}, "source": ["如果你希望降低数据总量大小，或者调整数据比例，你可以使用`delete_files.sh`代码。这段代码可以随机删除目录下的文件，**一旦删除无法撤销、只能重新下载，因此务必谨慎**。在finalshell中点击文件、即可打开脚本，可以按照下面的方式修改，并按ctrl s进行保存。"]}, {"cell_type": "markdown", "id": "d54badaf-3f4d-4ea5-a581-912ef32b4184", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/131.png)"]}, {"cell_type": "markdown", "id": "b8233b48-c645-4924-b827-e7c5b191bdc7", "metadata": {}, "source": ["当设置好你需要的目录后，运行下面的代码 ↓ 即可开始对特定目录进行按比例删除，以此来控制特定数据集的大小。当然，你也可以手动点进文件夹对数据进行删除、然后再查看数据相应的比例与大小 ↓"]}, {"cell_type": "markdown", "id": "fb06742b-8e9d-4b08-92da-cd0e79c00382", "metadata": {}, "source": ["```shell\n", "# 进入data文件夹\n", "cd ~/autodl-tmp/minideepseek/v3/data\n", "\n", "# 运行文件删除脚本\n", "chmod a+x detele_files.sh\n", "bash delete_files.sh\n", "\n", "# 再查看数据的大小与比例\n", "du -h --max-depth=1 ~/autodl-tmp/minideepseek/v3/data\n", "```"]}, {"cell_type": "markdown", "id": "2ab9f2cc-f526-4229-abd0-b29fee2e6c46", "metadata": {}, "source": ["#### 3.3.3 如何查看/读取特定数据？"]}, {"cell_type": "markdown", "id": "6d77d9f5-30b9-4f6d-9464-4ac71ac77285", "metadata": {}, "source": ["在finalshell的linux环境中打开jupyter、运行`print_data.ipy`即可看到打印的结果（打开jupyter的过程参考九天老师在环境部署中所写的指南，要使用SSH隧道工具哦）↓"]}, {"cell_type": "markdown", "id": "544df724-eb0a-490e-992f-ba4a5dd11e97", "metadata": {}, "source": ["```shell\n", "cd ~/autodl-tmp/minideepseek/v3\n", "source mnds_training/bin/activate\n", "\n", "# 创建<PERSON><PERSON><PERSON>\n", "pip install jupyterlab\n", "pip install ipykepip install ipykernelrnel\n", "python -m ipykernel install --user --name mnds_traning --display-name \"Python (mnds_traning)\"\n", "\n", "# 启动jupyter lab\n", "jupyter lab --allow-root\n", "```"]}, {"cell_type": "markdown", "id": "3fba3873-b2b7-40ce-890b-80907e5a5440", "metadata": {}, "source": ["**（以下的代码粘贴自`print_data.ipy`文件，实际你需要在shell环境中开启jupyter去执行该代码才能看到和我一样的结果）**"]}, {"cell_type": "markdown", "id": "e4b87ecf-1058-49a0-97b9-f22d2fe86017", "metadata": {}, "source": ["- **skypile**"]}, {"cell_type": "code", "execution_count": 1, "id": "7c3265f2-f3b2-4311-a54e-1ec30c5e3702", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"text\": \"近日，由微软(中国)有限公司携手实验室合作伙伴张江集团打造的微软人工智能和物联网实验室，即微软第四个全球实验室在张江人工智能岛正式启用并投入运营。蜜度信息(媒体大数据专家)同ABB、泛亚汽车(大型跨国企业)等30家企业，一道成为实验室首批赋能企业。\\n微软人工智能和物联网实验室，是微软为推动人工智能和物联网解决方案及应用的创新、研发和产业化而专门设立的全球性研发机构，旨在通过为企业和合作伙伴开发物联网产品和解决方案提供全方位支持，切实推动技术创新与制造、零售、医疗、金融、城市建设等行业数字化转型的深度融合和创新发展。\\n据了解，包括蜜度信息在内的首批30家被微软人工智能和物联网实验室赋能的企业，将在未来3-6个月得到微软的全方位支持，包括实验室提供的硬件及软件资源、微软云服务资源、接入微软生态系统与大中小产业资源实现联动等，同时这些被赋能的企业也将享受张江集团提供的创新及投资服务支持。\\n蜜度信息，作为新浪微博投资的媒体大数据公司，每日采集微博、微信、新闻、报刊、政务、外媒、博客、论坛、视频、网站、客户端等全网11大信息源，其中，且独家拥有新浪微博全量政务舆情数据。据统计，蜜度信息日新增1亿+条数据。基于海量数据，蜜度信息通过产、学、研合作的方式，与不同机构分别推出了网络传播热度指数、情绪地图、新媒体账号影响力排行榜等一系列颇有影响力的产品。而接下来，在微软人工智能和物联网实验室的加持之下，蜜度信息也将进一步发挥其在数据采集与自然语言处理上全快准稳的优势，成为国内媒体大数据应用领域的佼佼者。（完）\\n\"\n", "}\n"]}], "source": ["import json\n", "\n", "# 指定 JSONL 文件路径\n", "file_path = \"/root/autodl-tmp/minideepseek/v3/data/skypile/data/2022-40_zh_head_0000.jsonl\"\n", "\n", "# 读取 JSONL 文件的第一行\n", "with open(file_path, \"r\", encoding=\"utf-8\") as file:\n", "    first_line = file.readline().strip()  # 读取第一行并去掉首尾空格或换行符\n", "    first_json = json.loads(first_line)  # 解析 JSON 格式\n", "\n", "# 打印第一行内容\n", "print(json.dumps(first_json, indent=4, ensure_ascii=False))  # 格式化打印 JSON 数据"]}, {"cell_type": "markdown", "id": "38c4e65a-6ee8-4e2c-b874-1f6d7ffc67de", "metadata": {}, "source": ["- **starcoder**"]}, {"cell_type": "code", "execution_count": 6, "id": "f53917c8-f52f-49fa-b6a9-705a9654424b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['max_stars_repo_path', 'max_stars_repo_name', 'max_stars_count', 'id', 'content'], dtype='object')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 指定 Parquet 文件路径\n", "file_path = \"/root/autodl-tmp/minideepseek/v3/data/starcoder/python/train-00003-of-00059.parquet\"\n", "\n", "# 读取 Parquet 文件\n", "df = pd.read_parquet(file_path, engine=\"pyarrow\")\n", "\n", "# 设置 Pandas 选项，确保完整显示内容\n", "pd.set_option(\"display.max_columns\", None)  # 显示所有列\n", "pd.set_option(\"display.max_colwidth\", None)  # 显示所有单元格内容\n", "pd.set_option(\"display.width\", 1000)  # 设置输出宽度，防止换行\n", "\n", "# 展示表格结构\n", "\n", "df.columns"]}, {"cell_type": "code", "execution_count": 7, "id": "96f4a24b-d7f5-4237-90e9-b84a6f5b21f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"from functools import partial\\nfrom utils.fmap_visualize import FeatureMapVis_siamese,FeatureMapVis\\nfrom pathlib import Path\\nfrom utils.fmap_visualize import show_tensor, show_img\\nimport numpy as np\\nimport os\\nimport cv2\\nimport torch\\nimport torchvision\\n#from models.Models_tmp import SiameseCGNet_v2\\n\\ndef forward(self, img, img_metas=None, return_loss=False, **kwargs):\\n    outs = self.forward(img)\\n    return outs\\n\\n\\ndef create_featuremap_vis(model=None, use_gpu=True, init_shape=(768, 768, 3)):\\n    #model.forward = partial(forward, model)\\n    featurevis = FeatureMapVis_siamese(model, use_gpu)\\n    featurevis.set_hook_style(init_shape[2], init_shape[:2])\\n    return featurevis\\n\\n\\ndef _show_save_data(featurevis, input, img_orig, feature_indexs, filepath, is_show, output_dir):\\n    show_datas = []\\n    for feature_index in feature_indexs:\\n        feature_map = featurevis.run(input, feature_index=feature_index)[0]\\n        data = show_tensor(feature_map[0], resize_hw=input.shape[:2], show_split=False, is_show=False)[0]\\n        am_data = cv2.addWeighted(data, 0.5, img_orig, 0.5, 0)\\n        show_datas.append(am_data)\\n    if is_show:\\n        show_img(show_datas)\\n    if output_dir is not None:\\n        filename = os.path.join(output_dir,\\n                                Path(filepath).name\\n                                )\\n        if len(show_datas) == 1:\\n            cv2.imwrite(show_datas[0], filename)\\n        else:\\n            for i in range(len(show_datas)):\\n                fname, suffix = os.path.splitext(filename)\\n                cv2.imwrite(fname + '_{}'.format(str(i)) + suffix,show_datas[i])\\n\\ndef _show_save_data_siamese(featurevis, input, img_orig, feature_indexs, fmaps = None):\\n    show_datas = []\\n    imgs = img_orig.detach().cpu().numpy().transpose([0, 2, 3, 1])\\n    if fmaps is None:\\n        for feature_index in feature_indexs:\\n            feature_maps = featurevis.run(input, feature_index=feature_index)[0]\\n            am_data = []\\n            for img,feature_map in zip(imgs,feature_maps[:3]):\\n                data = show_tensor(feature_map, resize_hw=input.shape[2:], show_split=False, is_show=False)[0]\\n                am_data.append(255-data)\\n            show_datas.append(am_data)\\n    else:\\n        for i in range(len(fmaps)):\\n            am_data = []\\n            for img, feature_map in zip(imgs, fmaps[i]):\\n                data = show_tensor(feature_map, resize_hw=input.shape[2:], show_split=False, is_show=False)[0]\\n                am_data.append(data)\\n            show_datas.append(am_data)\\n    return show_datas\\n\\ndef show_featuremap_from_imgs(featurevis, feature_indexs, img_dir, is_show, output_dir):\\n    if not isinstance(feature_indexs, (list, tuple)):\\n        feature_indexs = [feature_indexs]\\n    img_paths = [os.path.join(img_dir, x) for x in os.listdir(img_dir) if 'jpg' in x]\\n    for path in img_paths:\\n        img = cv2.imread(path)\\n        # 这里是输入模型前的图片处理\\n        input = img.astype(np.float32).copy()\\n        # 显示特征图\\n        _show_save_data(featurevis, input, img, feature_indexs, path, is_show, output_dir)\\n\\n\\n\\nif __name__ == '__main__':\\n    img_dir = '.'\\n    out_dir = './out'\\n\\n    init_shape = (1024, 1024, 3)  # 值不重要，只要前向一遍网络时候不报错即可\\n    feature_index = [32, 70, 96, 155]  # 可视化的层索引\\n    use_gpu = True\\n    is_show = False\\n    model = torchvision.models.resnet50(pretrained=True)  # 这里创建模型\\n    #model = torch.load(path)\\n    if use_gpu:\\n        model.cuda()\\n    featurevis = create_featuremap_vis(model, use_gpu, init_shape)\\n    show_featuremap_from_imgs(featurevis, feature_index, img_dir, is_show, out_dir)\\n\""]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 显示第一行\n", "df.loc[0,\"content\"]"]}, {"cell_type": "code", "execution_count": 8, "id": "490b606f-9c0e-4720-b09f-1d8a3c441047", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>pre { line-height: 125%; }\n", "td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", ".output_html .hll { background-color: #ffffcc }\n", ".output_html { background: #f8f8f8; }\n", ".output_html .c { color: #3D7B7B; font-style: italic } /* Comment */\n", ".output_html .err { border: 1px solid #F00 } /* Error */\n", ".output_html .k { color: #008000; font-weight: bold } /* Keyword */\n", ".output_html .o { color: #666 } /* Operator */\n", ".output_html .ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */\n", ".output_html .cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */\n", ".output_html .cp { color: #9C6500 } /* Comment.Preproc */\n", ".output_html .cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */\n", ".output_html .c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */\n", ".output_html .cs { color: #3D7B7B; font-style: italic } /* Comment.Special */\n", ".output_html .gd { color: #A00000 } /* Generic.Deleted */\n", ".output_html .ge { font-style: italic } /* Generic.Emph */\n", ".output_html .ges { font-weight: bold; font-style: italic } /* Generic.EmphStrong */\n", ".output_html .gr { color: #E40000 } /* Generic.Error */\n", ".output_html .gh { color: #000080; font-weight: bold } /* Generic.Heading */\n", ".output_html .gi { color: #008400 } /* Generic.Inserted */\n", ".output_html .go { color: #717171 } /* Generic.Output */\n", ".output_html .gp { color: #000080; font-weight: bold } /* Generic.Prompt */\n", ".output_html .gs { font-weight: bold } /* Generic.Strong */\n", ".output_html .gu { color: #800080; font-weight: bold } /* Generic.Subheading */\n", ".output_html .gt { color: #04D } /* Generic.Traceback */\n", ".output_html .kc { color: #008000; font-weight: bold } /* Keyword.Constant */\n", ".output_html .kd { color: #008000; font-weight: bold } /* Keyword.Declaration */\n", ".output_html .kn { color: #008000; font-weight: bold } /* Keyword.Namespace */\n", ".output_html .kp { color: #008000 } /* Keyword.Pseudo */\n", ".output_html .kr { color: #008000; font-weight: bold } /* Keyword.Reserved */\n", ".output_html .kt { color: #B00040 } /* Keyword.Type */\n", ".output_html .m { color: #666 } /* Literal.Number */\n", ".output_html .s { color: #BA2121 } /* Literal.String */\n", ".output_html .na { color: #687822 } /* Name.Attribute */\n", ".output_html .nb { color: #008000 } /* Name.Builtin */\n", ".output_html .nc { color: #00F; font-weight: bold } /* Name.Class */\n", ".output_html .no { color: #800 } /* Name.Constant */\n", ".output_html .nd { color: #A2F } /* Name.Decorator */\n", ".output_html .ni { color: #717171; font-weight: bold } /* Name.Entity */\n", ".output_html .ne { color: #CB3F38; font-weight: bold } /* Name.Exception */\n", ".output_html .nf { color: #00F } /* Name.Function */\n", ".output_html .nl { color: #767600 } /* Name.Label */\n", ".output_html .nn { color: #00F; font-weight: bold } /* Name.Namespace */\n", ".output_html .nt { color: #008000; font-weight: bold } /* Name.Tag */\n", ".output_html .nv { color: #19177C } /* Name.Variable */\n", ".output_html .ow { color: #A2F; font-weight: bold } /* Operator.Word */\n", ".output_html .w { color: #BBB } /* Text.Whitespace */\n", ".output_html .mb { color: #666 } /* Literal.Number.Bin */\n", ".output_html .mf { color: #666 } /* Literal.Number.Float */\n", ".output_html .mh { color: #666 } /* Literal.Number.Hex */\n", ".output_html .mi { color: #666 } /* Literal.Number.Integer */\n", ".output_html .mo { color: #666 } /* Literal.Number.Oct */\n", ".output_html .sa { color: #BA2121 } /* Literal.String.Affix */\n", ".output_html .sb { color: #BA2121 } /* Literal.String.Backtick */\n", ".output_html .sc { color: #BA2121 } /* Literal.String.Char */\n", ".output_html .dl { color: #BA2121 } /* Literal.String.Delimiter */\n", ".output_html .sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */\n", ".output_html .s2 { color: #BA2121 } /* Literal.String.Double */\n", ".output_html .se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */\n", ".output_html .sh { color: #BA2121 } /* Literal.String.Heredoc */\n", ".output_html .si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */\n", ".output_html .sx { color: #008000 } /* Literal.String.Other */\n", ".output_html .sr { color: #A45A77 } /* Literal.String.Regex */\n", ".output_html .s1 { color: #BA2121 } /* Literal.String.Single */\n", ".output_html .ss { color: #19177C } /* Literal.String.Symbol */\n", ".output_html .bp { color: #008000 } /* Name.Builtin.Pseudo */\n", ".output_html .fm { color: #00F } /* Name.Function.Magic */\n", ".output_html .vc { color: #19177C } /* Name.Variable.Class */\n", ".output_html .vg { color: #19177C } /* Name.Variable.Global */\n", ".output_html .vi { color: #19177C } /* Name.Variable.Instance */\n", ".output_html .vm { color: #19177C } /* Name.Variable.Magic */\n", ".output_html .il { color: #666 } /* Literal.Number.Integer.Long */</style><div class=\"highlight\"><pre><span></span><span class=\"kn\">from</span><span class=\"w\"> </span><span class=\"nn\">functools</span><span class=\"w\"> </span><span class=\"kn\">import</span> <span class=\"n\">partial</span>\n", "<span class=\"kn\">from</span><span class=\"w\"> </span><span class=\"nn\">utils.fmap_visualize</span><span class=\"w\"> </span><span class=\"kn\">import</span> <span class=\"n\">FeatureMapVis_siamese</span><span class=\"p\">,</span><span class=\"n\">FeatureMapVis</span>\n", "<span class=\"kn\">from</span><span class=\"w\"> </span><span class=\"nn\">pathlib</span><span class=\"w\"> </span><span class=\"kn\">import</span> <span class=\"n\">Path</span>\n", "<span class=\"kn\">from</span><span class=\"w\"> </span><span class=\"nn\">utils.fmap_visualize</span><span class=\"w\"> </span><span class=\"kn\">import</span> <span class=\"n\">show_tensor</span><span class=\"p\">,</span> <span class=\"n\">show_img</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">numpy</span><span class=\"w\"> </span><span class=\"k\">as</span><span class=\"w\"> </span><span class=\"nn\">np</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">os</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">cv2</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">torch</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">torchvision</span>\n", "<span class=\"c1\">#from models.Models_tmp import SiameseCGNet_v2</span>\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">forward</span><span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"p\">,</span> <span class=\"n\">img</span><span class=\"p\">,</span> <span class=\"n\">img_metas</span><span class=\"o\">=</span><span class=\"kc\">None</span><span class=\"p\">,</span> <span class=\"n\">return_loss</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">,</span> <span class=\"o\">**</span><span class=\"n\">kwargs</span><span class=\"p\">):</span>\n", "    <span class=\"n\">outs</span> <span class=\"o\">=</span> <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">forward</span><span class=\"p\">(</span><span class=\"n\">img</span><span class=\"p\">)</span>\n", "    <span class=\"k\">return</span> <span class=\"n\">outs</span>\n", "\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">create_featuremap_vis</span><span class=\"p\">(</span><span class=\"n\">model</span><span class=\"o\">=</span><span class=\"kc\">None</span><span class=\"p\">,</span> <span class=\"n\">use_gpu</span><span class=\"o\">=</span><span class=\"kc\">True</span><span class=\"p\">,</span> <span class=\"n\">init_shape</span><span class=\"o\">=</span><span class=\"p\">(</span><span class=\"mi\">768</span><span class=\"p\">,</span> <span class=\"mi\">768</span><span class=\"p\">,</span> <span class=\"mi\">3</span><span class=\"p\">)):</span>\n", "    <span class=\"c1\">#model.forward = partial(forward, model)</span>\n", "    <span class=\"n\">featurevis</span> <span class=\"o\">=</span> <span class=\"n\">FeatureMapVis_siamese</span><span class=\"p\">(</span><span class=\"n\">model</span><span class=\"p\">,</span> <span class=\"n\">use_gpu</span><span class=\"p\">)</span>\n", "    <span class=\"n\">featurevis</span><span class=\"o\">.</span><span class=\"n\">set_hook_style</span><span class=\"p\">(</span><span class=\"n\">init_shape</span><span class=\"p\">[</span><span class=\"mi\">2</span><span class=\"p\">],</span> <span class=\"n\">init_shape</span><span class=\"p\">[:</span><span class=\"mi\">2</span><span class=\"p\">])</span>\n", "    <span class=\"k\">return</span> <span class=\"n\">featurevis</span>\n", "\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">_show_save_data</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">img_orig</span><span class=\"p\">,</span> <span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"n\">filepath</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"p\">,</span> <span class=\"n\">output_dir</span><span class=\"p\">):</span>\n", "    <span class=\"n\">show_datas</span> <span class=\"o\">=</span> <span class=\"p\">[]</span>\n", "    <span class=\"k\">for</span> <span class=\"n\">feature_index</span> <span class=\"ow\">in</span> <span class=\"n\">feature_indexs</span><span class=\"p\">:</span>\n", "        <span class=\"n\">feature_map</span> <span class=\"o\">=</span> <span class=\"n\">featurevis</span><span class=\"o\">.</span><span class=\"n\">run</span><span class=\"p\">(</span><span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">feature_index</span><span class=\"o\">=</span><span class=\"n\">feature_index</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "        <span class=\"n\">data</span> <span class=\"o\">=</span> <span class=\"n\">show_tensor</span><span class=\"p\">(</span><span class=\"n\">feature_map</span><span class=\"p\">[</span><span class=\"mi\">0</span><span class=\"p\">],</span> <span class=\"n\">resize_hw</span><span class=\"o\">=</span><span class=\"nb\">input</span><span class=\"o\">.</span><span class=\"n\">shape</span><span class=\"p\">[:</span><span class=\"mi\">2</span><span class=\"p\">],</span> <span class=\"n\">show_split</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "        <span class=\"n\">am_data</span> <span class=\"o\">=</span> <span class=\"n\">cv2</span><span class=\"o\">.</span><span class=\"n\">addWeighted</span><span class=\"p\">(</span><span class=\"n\">data</span><span class=\"p\">,</span> <span class=\"mf\">0.5</span><span class=\"p\">,</span> <span class=\"n\">img_orig</span><span class=\"p\">,</span> <span class=\"mf\">0.5</span><span class=\"p\">,</span> <span class=\"mi\">0</span><span class=\"p\">)</span>\n", "        <span class=\"n\">show_datas</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"n\">am_data</span><span class=\"p\">)</span>\n", "    <span class=\"k\">if</span> <span class=\"n\">is_show</span><span class=\"p\">:</span>\n", "        <span class=\"n\">show_img</span><span class=\"p\">(</span><span class=\"n\">show_datas</span><span class=\"p\">)</span>\n", "    <span class=\"k\">if</span> <span class=\"n\">output_dir</span> <span class=\"ow\">is</span> <span class=\"ow\">not</span> <span class=\"kc\">None</span><span class=\"p\">:</span>\n", "        <span class=\"n\">filename</span> <span class=\"o\">=</span> <span class=\"n\">os</span><span class=\"o\">.</span><span class=\"n\">path</span><span class=\"o\">.</span><span class=\"n\">join</span><span class=\"p\">(</span><span class=\"n\">output_dir</span><span class=\"p\">,</span>\n", "                                <span class=\"n\">Path</span><span class=\"p\">(</span><span class=\"n\">filepath</span><span class=\"p\">)</span><span class=\"o\">.</span><span class=\"n\">name</span>\n", "                                <span class=\"p\">)</span>\n", "        <span class=\"k\">if</span> <span class=\"nb\">len</span><span class=\"p\">(</span><span class=\"n\">show_datas</span><span class=\"p\">)</span> <span class=\"o\">==</span> <span class=\"mi\">1</span><span class=\"p\">:</span>\n", "            <span class=\"n\">cv2</span><span class=\"o\">.</span><span class=\"n\">imwrite</span><span class=\"p\">(</span><span class=\"n\">show_datas</span><span class=\"p\">[</span><span class=\"mi\">0</span><span class=\"p\">],</span> <span class=\"n\">filename</span><span class=\"p\">)</span>\n", "        <span class=\"k\">else</span><span class=\"p\">:</span>\n", "            <span class=\"k\">for</span> <span class=\"n\">i</span> <span class=\"ow\">in</span> <span class=\"nb\">range</span><span class=\"p\">(</span><span class=\"nb\">len</span><span class=\"p\">(</span><span class=\"n\">show_datas</span><span class=\"p\">)):</span>\n", "                <span class=\"n\">fname</span><span class=\"p\">,</span> <span class=\"n\">suffix</span> <span class=\"o\">=</span> <span class=\"n\">os</span><span class=\"o\">.</span><span class=\"n\">path</span><span class=\"o\">.</span><span class=\"n\">splitext</span><span class=\"p\">(</span><span class=\"n\">filename</span><span class=\"p\">)</span>\n", "                <span class=\"n\">cv2</span><span class=\"o\">.</span><span class=\"n\">imwrite</span><span class=\"p\">(</span><span class=\"n\">fname</span> <span class=\"o\">+</span> <span class=\"s1\">&#39;_</span><span class=\"si\">{}</span><span class=\"s1\">&#39;</span><span class=\"o\">.</span><span class=\"n\">format</span><span class=\"p\">(</span><span class=\"nb\">str</span><span class=\"p\">(</span><span class=\"n\">i</span><span class=\"p\">))</span> <span class=\"o\">+</span> <span class=\"n\">suffix</span><span class=\"p\">,</span><span class=\"n\">show_datas</span><span class=\"p\">[</span><span class=\"n\">i</span><span class=\"p\">])</span>\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">_show_save_data_siamese</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">img_orig</span><span class=\"p\">,</span> <span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"n\">fmaps</span> <span class=\"o\">=</span> <span class=\"kc\">None</span><span class=\"p\">):</span>\n", "    <span class=\"n\">show_datas</span> <span class=\"o\">=</span> <span class=\"p\">[]</span>\n", "    <span class=\"n\">imgs</span> <span class=\"o\">=</span> <span class=\"n\">img_orig</span><span class=\"o\">.</span><span class=\"n\">detach</span><span class=\"p\">()</span><span class=\"o\">.</span><span class=\"n\">cpu</span><span class=\"p\">()</span><span class=\"o\">.</span><span class=\"n\">numpy</span><span class=\"p\">()</span><span class=\"o\">.</span><span class=\"n\">transpose</span><span class=\"p\">([</span><span class=\"mi\">0</span><span class=\"p\">,</span> <span class=\"mi\">2</span><span class=\"p\">,</span> <span class=\"mi\">3</span><span class=\"p\">,</span> <span class=\"mi\">1</span><span class=\"p\">])</span>\n", "    <span class=\"k\">if</span> <span class=\"n\">fmaps</span> <span class=\"ow\">is</span> <span class=\"kc\">None</span><span class=\"p\">:</span>\n", "        <span class=\"k\">for</span> <span class=\"n\">feature_index</span> <span class=\"ow\">in</span> <span class=\"n\">feature_indexs</span><span class=\"p\">:</span>\n", "            <span class=\"n\">feature_maps</span> <span class=\"o\">=</span> <span class=\"n\">featurevis</span><span class=\"o\">.</span><span class=\"n\">run</span><span class=\"p\">(</span><span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">feature_index</span><span class=\"o\">=</span><span class=\"n\">feature_index</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "            <span class=\"n\">am_data</span> <span class=\"o\">=</span> <span class=\"p\">[]</span>\n", "            <span class=\"k\">for</span> <span class=\"n\">img</span><span class=\"p\">,</span><span class=\"n\">feature_map</span> <span class=\"ow\">in</span> <span class=\"nb\">zip</span><span class=\"p\">(</span><span class=\"n\">imgs</span><span class=\"p\">,</span><span class=\"n\">feature_maps</span><span class=\"p\">[:</span><span class=\"mi\">3</span><span class=\"p\">]):</span>\n", "                <span class=\"n\">data</span> <span class=\"o\">=</span> <span class=\"n\">show_tensor</span><span class=\"p\">(</span><span class=\"n\">feature_map</span><span class=\"p\">,</span> <span class=\"n\">resize_hw</span><span class=\"o\">=</span><span class=\"nb\">input</span><span class=\"o\">.</span><span class=\"n\">shape</span><span class=\"p\">[</span><span class=\"mi\">2</span><span class=\"p\">:],</span> <span class=\"n\">show_split</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "                <span class=\"n\">am_data</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"mi\">255</span><span class=\"o\">-</span><span class=\"n\">data</span><span class=\"p\">)</span>\n", "            <span class=\"n\">show_datas</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"n\">am_data</span><span class=\"p\">)</span>\n", "    <span class=\"k\">else</span><span class=\"p\">:</span>\n", "        <span class=\"k\">for</span> <span class=\"n\">i</span> <span class=\"ow\">in</span> <span class=\"nb\">range</span><span class=\"p\">(</span><span class=\"nb\">len</span><span class=\"p\">(</span><span class=\"n\">fmaps</span><span class=\"p\">)):</span>\n", "            <span class=\"n\">am_data</span> <span class=\"o\">=</span> <span class=\"p\">[]</span>\n", "            <span class=\"k\">for</span> <span class=\"n\">img</span><span class=\"p\">,</span> <span class=\"n\">feature_map</span> <span class=\"ow\">in</span> <span class=\"nb\">zip</span><span class=\"p\">(</span><span class=\"n\">imgs</span><span class=\"p\">,</span> <span class=\"n\">fmaps</span><span class=\"p\">[</span><span class=\"n\">i</span><span class=\"p\">]):</span>\n", "                <span class=\"n\">data</span> <span class=\"o\">=</span> <span class=\"n\">show_tensor</span><span class=\"p\">(</span><span class=\"n\">feature_map</span><span class=\"p\">,</span> <span class=\"n\">resize_hw</span><span class=\"o\">=</span><span class=\"nb\">input</span><span class=\"o\">.</span><span class=\"n\">shape</span><span class=\"p\">[</span><span class=\"mi\">2</span><span class=\"p\">:],</span> <span class=\"n\">show_split</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "                <span class=\"n\">am_data</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"n\">data</span><span class=\"p\">)</span>\n", "            <span class=\"n\">show_datas</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"n\">am_data</span><span class=\"p\">)</span>\n", "    <span class=\"k\">return</span> <span class=\"n\">show_datas</span>\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">show_featuremap_from_imgs</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"n\">img_dir</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"p\">,</span> <span class=\"n\">output_dir</span><span class=\"p\">):</span>\n", "    <span class=\"k\">if</span> <span class=\"ow\">not</span> <span class=\"nb\">isinstance</span><span class=\"p\">(</span><span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"p\">(</span><span class=\"nb\">list</span><span class=\"p\">,</span> <span class=\"nb\">tuple</span><span class=\"p\">)):</span>\n", "        <span class=\"n\">feature_indexs</span> <span class=\"o\">=</span> <span class=\"p\">[</span><span class=\"n\">feature_indexs</span><span class=\"p\">]</span>\n", "    <span class=\"n\">img_paths</span> <span class=\"o\">=</span> <span class=\"p\">[</span><span class=\"n\">os</span><span class=\"o\">.</span><span class=\"n\">path</span><span class=\"o\">.</span><span class=\"n\">join</span><span class=\"p\">(</span><span class=\"n\">img_dir</span><span class=\"p\">,</span> <span class=\"n\">x</span><span class=\"p\">)</span> <span class=\"k\">for</span> <span class=\"n\">x</span> <span class=\"ow\">in</span> <span class=\"n\">os</span><span class=\"o\">.</span><span class=\"n\">listdir</span><span class=\"p\">(</span><span class=\"n\">img_dir</span><span class=\"p\">)</span> <span class=\"k\">if</span> <span class=\"s1\">&#39;jpg&#39;</span> <span class=\"ow\">in</span> <span class=\"n\">x</span><span class=\"p\">]</span>\n", "    <span class=\"k\">for</span> <span class=\"n\">path</span> <span class=\"ow\">in</span> <span class=\"n\">img_paths</span><span class=\"p\">:</span>\n", "        <span class=\"n\">img</span> <span class=\"o\">=</span> <span class=\"n\">cv2</span><span class=\"o\">.</span><span class=\"n\">imread</span><span class=\"p\">(</span><span class=\"n\">path</span><span class=\"p\">)</span>\n", "        <span class=\"c1\"># 这里是输入模型前的图片处理</span>\n", "        <span class=\"nb\">input</span> <span class=\"o\">=</span> <span class=\"n\">img</span><span class=\"o\">.</span><span class=\"n\">astype</span><span class=\"p\">(</span><span class=\"n\">np</span><span class=\"o\">.</span><span class=\"n\">float32</span><span class=\"p\">)</span><span class=\"o\">.</span><span class=\"n\">copy</span><span class=\"p\">()</span>\n", "        <span class=\"c1\"># 显示特征图</span>\n", "        <span class=\"n\">_show_save_data</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">img</span><span class=\"p\">,</span> <span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"n\">path</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"p\">,</span> <span class=\"n\">output_dir</span><span class=\"p\">)</span>\n", "\n", "\n", "\n", "<span class=\"k\">if</span> <span class=\"vm\">__name__</span> <span class=\"o\">==</span> <span class=\"s1\">&#39;__main__&#39;</span><span class=\"p\">:</span>\n", "    <span class=\"n\">img_dir</span> <span class=\"o\">=</span> <span class=\"s1\">&#39;.&#39;</span>\n", "    <span class=\"n\">out_dir</span> <span class=\"o\">=</span> <span class=\"s1\">&#39;./out&#39;</span>\n", "\n", "    <span class=\"n\">init_shape</span> <span class=\"o\">=</span> <span class=\"p\">(</span><span class=\"mi\">1024</span><span class=\"p\">,</span> <span class=\"mi\">1024</span><span class=\"p\">,</span> <span class=\"mi\">3</span><span class=\"p\">)</span>  <span class=\"c1\"># 值不重要，只要前向一遍网络时候不报错即可</span>\n", "    <span class=\"n\">feature_index</span> <span class=\"o\">=</span> <span class=\"p\">[</span><span class=\"mi\">32</span><span class=\"p\">,</span> <span class=\"mi\">70</span><span class=\"p\">,</span> <span class=\"mi\">96</span><span class=\"p\">,</span> <span class=\"mi\">155</span><span class=\"p\">]</span>  <span class=\"c1\"># 可视化的层索引</span>\n", "    <span class=\"n\">use_gpu</span> <span class=\"o\">=</span> <span class=\"kc\">True</span>\n", "    <span class=\"n\">is_show</span> <span class=\"o\">=</span> <span class=\"kc\">False</span>\n", "    <span class=\"n\">model</span> <span class=\"o\">=</span> <span class=\"n\">torchvision</span><span class=\"o\">.</span><span class=\"n\">models</span><span class=\"o\">.</span><span class=\"n\">resnet50</span><span class=\"p\">(</span><span class=\"n\">pretrained</span><span class=\"o\">=</span><span class=\"kc\">True</span><span class=\"p\">)</span>  <span class=\"c1\"># 这里创建模型</span>\n", "    <span class=\"c1\">#model = torch.load(path)</span>\n", "    <span class=\"k\">if</span> <span class=\"n\">use_gpu</span><span class=\"p\">:</span>\n", "        <span class=\"n\">model</span><span class=\"o\">.</span><span class=\"n\">cuda</span><span class=\"p\">()</span>\n", "    <span class=\"n\">featurevis</span> <span class=\"o\">=</span> <span class=\"n\">create_featuremap_vis</span><span class=\"p\">(</span><span class=\"n\">model</span><span class=\"p\">,</span> <span class=\"n\">use_gpu</span><span class=\"p\">,</span> <span class=\"n\">init_shape</span><span class=\"p\">)</span>\n", "    <span class=\"n\">show_featuremap_from_imgs</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"n\">feature_index</span><span class=\"p\">,</span> <span class=\"n\">img_dir</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"p\">,</span> <span class=\"n\">out_dir</span><span class=\"p\">)</span>\n", "</pre></div>\n"], "text/latex": ["\\begin{Verbatim}[commandchars=\\\\\\{\\}]\n", "\\PY{k+kn}{from}\\PY{+w}{ }\\PY{n+nn}{functools}\\PY{+w}{ }\\PY{k+kn}{import} \\PY{n}{partial}\n", "\\PY{k+kn}{from}\\PY{+w}{ }\\PY{n+nn}{utils}\\PY{n+nn}{.}\\PY{n+nn}{fmap\\PYZus{}visualize}\\PY{+w}{ }\\PY{k+kn}{import} \\PY{n}{FeatureMapVis\\PYZus{}siamese}\\PY{p}{,}\\PY{n}{FeatureMapVis}\n", "\\PY{k+kn}{from}\\PY{+w}{ }\\PY{n+nn}{pathlib}\\PY{+w}{ }\\PY{k+kn}{import} \\PY{n}{Path}\n", "\\PY{k+kn}{from}\\PY{+w}{ }\\PY{n+nn}{utils}\\PY{n+nn}{.}\\PY{n+nn}{fmap\\PYZus{}visualize}\\PY{+w}{ }\\PY{k+kn}{import} \\PY{n}{show\\PYZus{}tensor}\\PY{p}{,} \\PY{n}{show\\PYZus{}img}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{numpy}\\PY{+w}{ }\\PY{k}{as}\\PY{+w}{ }\\PY{n+nn}{np}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{os}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{cv2}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{torch}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{torchvision}\n", "\\PY{c+c1}{\\PYZsh{}from models.Models\\PYZus{}tmp import SiameseCGNet\\PYZus{}v2}\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{forward}\\PY{p}{(}\\PY{n+nb+bp}{self}\\PY{p}{,} \\PY{n}{img}\\PY{p}{,} \\PY{n}{img\\PYZus{}metas}\\PY{o}{=}\\PY{k+kc}{None}\\PY{p}{,} \\PY{n}{return\\PYZus{}loss}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{,} \\PY{o}{*}\\PY{o}{*}\\PY{n}{kwargs}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{n}{outs} \\PY{o}{=} \\PY{n+nb+bp}{self}\\PY{o}{.}\\PY{n}{forward}\\PY{p}{(}\\PY{n}{img}\\PY{p}{)}\n", "    \\PY{k}{return} \\PY{n}{outs}\n", "\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{create\\PYZus{}featuremap\\PYZus{}vis}\\PY{p}{(}\\PY{n}{model}\\PY{o}{=}\\PY{k+kc}{None}\\PY{p}{,} \\PY{n}{use\\PYZus{}gpu}\\PY{o}{=}\\PY{k+kc}{True}\\PY{p}{,} \\PY{n}{init\\PYZus{}shape}\\PY{o}{=}\\PY{p}{(}\\PY{l+m+mi}{768}\\PY{p}{,} \\PY{l+m+mi}{768}\\PY{p}{,} \\PY{l+m+mi}{3}\\PY{p}{)}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{c+c1}{\\PYZsh{}model.forward = partial(forward, model)}\n", "    \\PY{n}{featurevis} \\PY{o}{=} \\PY{n}{FeatureMapVis\\PYZus{}siamese}\\PY{p}{(}\\PY{n}{model}\\PY{p}{,} \\PY{n}{use\\PYZus{}gpu}\\PY{p}{)}\n", "    \\PY{n}{featurevis}\\PY{o}{.}\\PY{n}{set\\PYZus{}hook\\PYZus{}style}\\PY{p}{(}\\PY{n}{init\\PYZus{}shape}\\PY{p}{[}\\PY{l+m+mi}{2}\\PY{p}{]}\\PY{p}{,} \\PY{n}{init\\PYZus{}shape}\\PY{p}{[}\\PY{p}{:}\\PY{l+m+mi}{2}\\PY{p}{]}\\PY{p}{)}\n", "    \\PY{k}{return} \\PY{n}{featurevis}\n", "\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{\\PYZus{}show\\PYZus{}save\\PYZus{}data}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{img\\PYZus{}orig}\\PY{p}{,} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{n}{filepath}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{p}{,} \\PY{n}{output\\PYZus{}dir}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{n}{show\\PYZus{}datas} \\PY{o}{=} \\PY{p}{[}\\PY{p}{]}\n", "    \\PY{k}{for} \\PY{n}{feature\\PYZus{}index} \\PY{o+ow}{in} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{:}\n", "        \\PY{n}{feature\\PYZus{}map} \\PY{o}{=} \\PY{n}{featurevis}\\PY{o}{.}\\PY{n}{run}\\PY{p}{(}\\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{feature\\PYZus{}index}\\PY{o}{=}\\PY{n}{feature\\PYZus{}index}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "        \\PY{n}{data} \\PY{o}{=} \\PY{n}{show\\PYZus{}tensor}\\PY{p}{(}\\PY{n}{feature\\PYZus{}map}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\\PY{p}{,} \\PY{n}{resize\\PYZus{}hw}\\PY{o}{=}\\PY{n+nb}{input}\\PY{o}{.}\\PY{n}{shape}\\PY{p}{[}\\PY{p}{:}\\PY{l+m+mi}{2}\\PY{p}{]}\\PY{p}{,} \\PY{n}{show\\PYZus{}split}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "        \\PY{n}{am\\PYZus{}data} \\PY{o}{=} \\PY{n}{cv2}\\PY{o}{.}\\PY{n}{addWeighted}\\PY{p}{(}\\PY{n}{data}\\PY{p}{,} \\PY{l+m+mf}{0.5}\\PY{p}{,} \\PY{n}{img\\PYZus{}orig}\\PY{p}{,} \\PY{l+m+mf}{0.5}\\PY{p}{,} \\PY{l+m+mi}{0}\\PY{p}{)}\n", "        \\PY{n}{show\\PYZus{}datas}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{n}{am\\PYZus{}data}\\PY{p}{)}\n", "    \\PY{k}{if} \\PY{n}{is\\PYZus{}show}\\PY{p}{:}\n", "        \\PY{n}{show\\PYZus{}img}\\PY{p}{(}\\PY{n}{show\\PYZus{}datas}\\PY{p}{)}\n", "    \\PY{k}{if} \\PY{n}{output\\PYZus{}dir} \\PY{o+ow}{is} \\PY{o+ow}{not} \\PY{k+kc}{None}\\PY{p}{:}\n", "        \\PY{n}{filename} \\PY{o}{=} \\PY{n}{os}\\PY{o}{.}\\PY{n}{path}\\PY{o}{.}\\PY{n}{join}\\PY{p}{(}\\PY{n}{output\\PYZus{}dir}\\PY{p}{,}\n", "                                \\PY{n}{Path}\\PY{p}{(}\\PY{n}{filepath}\\PY{p}{)}\\PY{o}{.}\\PY{n}{name}\n", "                                \\PY{p}{)}\n", "        \\PY{k}{if} \\PY{n+nb}{len}\\PY{p}{(}\\PY{n}{show\\PYZus{}datas}\\PY{p}{)} \\PY{o}{==} \\PY{l+m+mi}{1}\\PY{p}{:}\n", "            \\PY{n}{cv2}\\PY{o}{.}\\PY{n}{imwrite}\\PY{p}{(}\\PY{n}{show\\PYZus{}datas}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\\PY{p}{,} \\PY{n}{filename}\\PY{p}{)}\n", "        \\PY{k}{else}\\PY{p}{:}\n", "            \\PY{k}{for} \\PY{n}{i} \\PY{o+ow}{in} \\PY{n+nb}{range}\\PY{p}{(}\\PY{n+nb}{len}\\PY{p}{(}\\PY{n}{show\\PYZus{}datas}\\PY{p}{)}\\PY{p}{)}\\PY{p}{:}\n", "                \\PY{n}{fname}\\PY{p}{,} \\PY{n}{suffix} \\PY{o}{=} \\PY{n}{os}\\PY{o}{.}\\PY{n}{path}\\PY{o}{.}\\PY{n}{splitext}\\PY{p}{(}\\PY{n}{filename}\\PY{p}{)}\n", "                \\PY{n}{cv2}\\PY{o}{.}\\PY{n}{imwrite}\\PY{p}{(}\\PY{n}{fname} \\PY{o}{+} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{\\PYZus{}}\\PY{l+s+si}{\\PYZob{}\\PYZcb{}}\\PY{l+s+s1}{\\PYZsq{}}\\PY{o}{.}\\PY{n}{format}\\PY{p}{(}\\PY{n+nb}{str}\\PY{p}{(}\\PY{n}{i}\\PY{p}{)}\\PY{p}{)} \\PY{o}{+} \\PY{n}{suffix}\\PY{p}{,}\\PY{n}{show\\PYZus{}datas}\\PY{p}{[}\\PY{n}{i}\\PY{p}{]}\\PY{p}{)}\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{\\PYZus{}show\\PYZus{}save\\PYZus{}data\\PYZus{}siamese}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{img\\PYZus{}orig}\\PY{p}{,} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{n}{fmaps} \\PY{o}{=} \\PY{k+kc}{None}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{n}{show\\PYZus{}datas} \\PY{o}{=} \\PY{p}{[}\\PY{p}{]}\n", "    \\PY{n}{imgs} \\PY{o}{=} \\PY{n}{img\\PYZus{}orig}\\PY{o}{.}\\PY{n}{detach}\\PY{p}{(}\\PY{p}{)}\\PY{o}{.}\\PY{n}{cpu}\\PY{p}{(}\\PY{p}{)}\\PY{o}{.}\\PY{n}{numpy}\\PY{p}{(}\\PY{p}{)}\\PY{o}{.}\\PY{n}{transpose}\\PY{p}{(}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{,} \\PY{l+m+mi}{2}\\PY{p}{,} \\PY{l+m+mi}{3}\\PY{p}{,} \\PY{l+m+mi}{1}\\PY{p}{]}\\PY{p}{)}\n", "    \\PY{k}{if} \\PY{n}{fmaps} \\PY{o+ow}{is} \\PY{k+kc}{None}\\PY{p}{:}\n", "        \\PY{k}{for} \\PY{n}{feature\\PYZus{}index} \\PY{o+ow}{in} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{:}\n", "            \\PY{n}{feature\\PYZus{}maps} \\PY{o}{=} \\PY{n}{featurevis}\\PY{o}{.}\\PY{n}{run}\\PY{p}{(}\\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{feature\\PYZus{}index}\\PY{o}{=}\\PY{n}{feature\\PYZus{}index}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "            \\PY{n}{am\\PYZus{}data} \\PY{o}{=} \\PY{p}{[}\\PY{p}{]}\n", "            \\PY{k}{for} \\PY{n}{img}\\PY{p}{,}\\PY{n}{feature\\PYZus{}map} \\PY{o+ow}{in} \\PY{n+nb}{zip}\\PY{p}{(}\\PY{n}{imgs}\\PY{p}{,}\\PY{n}{feature\\PYZus{}maps}\\PY{p}{[}\\PY{p}{:}\\PY{l+m+mi}{3}\\PY{p}{]}\\PY{p}{)}\\PY{p}{:}\n", "                \\PY{n}{data} \\PY{o}{=} \\PY{n}{show\\PYZus{}tensor}\\PY{p}{(}\\PY{n}{feature\\PYZus{}map}\\PY{p}{,} \\PY{n}{resize\\PYZus{}hw}\\PY{o}{=}\\PY{n+nb}{input}\\PY{o}{.}\\PY{n}{shape}\\PY{p}{[}\\PY{l+m+mi}{2}\\PY{p}{:}\\PY{p}{]}\\PY{p}{,} \\PY{n}{show\\PYZus{}split}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "                \\PY{n}{am\\PYZus{}data}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{l+m+mi}{255}\\PY{o}{\\PYZhy{}}\\PY{n}{data}\\PY{p}{)}\n", "            \\PY{n}{show\\PYZus{}datas}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{n}{am\\PYZus{}data}\\PY{p}{)}\n", "    \\PY{k}{else}\\PY{p}{:}\n", "        \\PY{k}{for} \\PY{n}{i} \\PY{o+ow}{in} \\PY{n+nb}{range}\\PY{p}{(}\\PY{n+nb}{len}\\PY{p}{(}\\PY{n}{fmaps}\\PY{p}{)}\\PY{p}{)}\\PY{p}{:}\n", "            \\PY{n}{am\\PYZus{}data} \\PY{o}{=} \\PY{p}{[}\\PY{p}{]}\n", "            \\PY{k}{for} \\PY{n}{img}\\PY{p}{,} \\PY{n}{feature\\PYZus{}map} \\PY{o+ow}{in} \\PY{n+nb}{zip}\\PY{p}{(}\\PY{n}{imgs}\\PY{p}{,} \\PY{n}{fmaps}\\PY{p}{[}\\PY{n}{i}\\PY{p}{]}\\PY{p}{)}\\PY{p}{:}\n", "                \\PY{n}{data} \\PY{o}{=} \\PY{n}{show\\PYZus{}tensor}\\PY{p}{(}\\PY{n}{feature\\PYZus{}map}\\PY{p}{,} \\PY{n}{resize\\PYZus{}hw}\\PY{o}{=}\\PY{n+nb}{input}\\PY{o}{.}\\PY{n}{shape}\\PY{p}{[}\\PY{l+m+mi}{2}\\PY{p}{:}\\PY{p}{]}\\PY{p}{,} \\PY{n}{show\\PYZus{}split}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "                \\PY{n}{am\\PYZus{}data}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{n}{data}\\PY{p}{)}\n", "            \\PY{n}{show\\PYZus{}datas}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{n}{am\\PYZus{}data}\\PY{p}{)}\n", "    \\PY{k}{return} \\PY{n}{show\\PYZus{}datas}\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{show\\PYZus{}featuremap\\PYZus{}from\\PYZus{}imgs}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{n}{img\\PYZus{}dir}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{p}{,} \\PY{n}{output\\PYZus{}dir}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{k}{if} \\PY{o+ow}{not} \\PY{n+nb}{isinstance}\\PY{p}{(}\\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{p}{(}\\PY{n+nb}{list}\\PY{p}{,} \\PY{n+nb}{tuple}\\PY{p}{)}\\PY{p}{)}\\PY{p}{:}\n", "        \\PY{n}{feature\\PYZus{}indexs} \\PY{o}{=} \\PY{p}{[}\\PY{n}{feature\\PYZus{}indexs}\\PY{p}{]}\n", "    \\PY{n}{img\\PYZus{}paths} \\PY{o}{=} \\PY{p}{[}\\PY{n}{os}\\PY{o}{.}\\PY{n}{path}\\PY{o}{.}\\PY{n}{join}\\PY{p}{(}\\PY{n}{img\\PYZus{}dir}\\PY{p}{,} \\PY{n}{x}\\PY{p}{)} \\PY{k}{for} \\PY{n}{x} \\PY{o+ow}{in} \\PY{n}{os}\\PY{o}{.}\\PY{n}{listdir}\\PY{p}{(}\\PY{n}{img\\PYZus{}dir}\\PY{p}{)} \\PY{k}{if} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{jpg}\\PY{l+s+s1}{\\PYZsq{}} \\PY{o+ow}{in} \\PY{n}{x}\\PY{p}{]}\n", "    \\PY{k}{for} \\PY{n}{path} \\PY{o+ow}{in} \\PY{n}{img\\PYZus{}paths}\\PY{p}{:}\n", "        \\PY{n}{img} \\PY{o}{=} \\PY{n}{cv2}\\PY{o}{.}\\PY{n}{imread}\\PY{p}{(}\\PY{n}{path}\\PY{p}{)}\n", "        \\PY{c+c1}{\\PYZsh{} 这里是输入模型前的图片处理}\n", "        \\PY{n+nb}{input} \\PY{o}{=} \\PY{n}{img}\\PY{o}{.}\\PY{n}{astype}\\PY{p}{(}\\PY{n}{np}\\PY{o}{.}\\PY{n}{float32}\\PY{p}{)}\\PY{o}{.}\\PY{n}{copy}\\PY{p}{(}\\PY{p}{)}\n", "        \\PY{c+c1}{\\PYZsh{} 显示特征图}\n", "        \\PY{n}{\\PYZus{}show\\PYZus{}save\\PYZus{}data}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{img}\\PY{p}{,} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{n}{path}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{p}{,} \\PY{n}{output\\PYZus{}dir}\\PY{p}{)}\n", "\n", "\n", "\n", "\\PY{k}{if} \\PY{n+nv+vm}{\\PYZus{}\\PYZus{}name\\PYZus{}\\PYZus{}} \\PY{o}{==} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{\\PYZus{}\\PYZus{}main\\PYZus{}\\PYZus{}}\\PY{l+s+s1}{\\PYZsq{}}\\PY{p}{:}\n", "    \\PY{n}{img\\PYZus{}dir} \\PY{o}{=} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{.}\\PY{l+s+s1}{\\PYZsq{}}\n", "    \\PY{n}{out\\PYZus{}dir} \\PY{o}{=} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{./out}\\PY{l+s+s1}{\\PYZsq{}}\n", "\n", "    \\PY{n}{init\\PYZus{}shape} \\PY{o}{=} \\PY{p}{(}\\PY{l+m+mi}{1024}\\PY{p}{,} \\PY{l+m+mi}{1024}\\PY{p}{,} \\PY{l+m+mi}{3}\\PY{p}{)}  \\PY{c+c1}{\\PYZsh{} 值不重要，只要前向一遍网络时候不报错即可}\n", "    \\PY{n}{feature\\PYZus{}index} \\PY{o}{=} \\PY{p}{[}\\PY{l+m+mi}{32}\\PY{p}{,} \\PY{l+m+mi}{70}\\PY{p}{,} \\PY{l+m+mi}{96}\\PY{p}{,} \\PY{l+m+mi}{155}\\PY{p}{]}  \\PY{c+c1}{\\PYZsh{} 可视化的层索引}\n", "    \\PY{n}{use\\PYZus{}gpu} \\PY{o}{=} \\PY{k+kc}{True}\n", "    \\PY{n}{is\\PYZus{}show} \\PY{o}{=} \\PY{k+kc}{False}\n", "    \\PY{n}{model} \\PY{o}{=} \\PY{n}{torchvision}\\PY{o}{.}\\PY{n}{models}\\PY{o}{.}\\PY{n}{resnet50}\\PY{p}{(}\\PY{n}{pretrained}\\PY{o}{=}\\PY{k+kc}{True}\\PY{p}{)}  \\PY{c+c1}{\\PYZsh{} 这里创建模型}\n", "    \\PY{c+c1}{\\PYZsh{}model = torch.load(path)}\n", "    \\PY{k}{if} \\PY{n}{use\\PYZus{}gpu}\\PY{p}{:}\n", "        \\PY{n}{model}\\PY{o}{.}\\PY{n}{cuda}\\PY{p}{(}\\PY{p}{)}\n", "    \\PY{n}{featurevis} \\PY{o}{=} \\PY{n}{create\\PYZus{}featuremap\\PYZus{}vis}\\PY{p}{(}\\PY{n}{model}\\PY{p}{,} \\PY{n}{use\\PYZus{}gpu}\\PY{p}{,} \\PY{n}{init\\PYZus{}shape}\\PY{p}{)}\n", "    \\PY{n}{show\\PYZus{}featuremap\\PYZus{}from\\PYZus{}imgs}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n}{feature\\PYZus{}index}\\PY{p}{,} \\PY{n}{img\\PYZus{}dir}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{p}{,} \\PY{n}{out\\PYZus{}dir}\\PY{p}{)}\n", "\\end{Verbatim}\n"], "text/plain": ["from functools import partial\n", "from utils.fmap_visualize import FeatureMapVis_siamese,FeatureMapVis\n", "from pathlib import Path\n", "from utils.fmap_visualize import show_tensor, show_img\n", "import numpy as np\n", "import os\n", "import cv2\n", "import torch\n", "import torchvision\n", "#from models.Models_tmp import SiameseCGNet_v2\n", "\n", "def forward(self, img, img_metas=None, return_loss=False, **kwargs):\n", "    outs = self.forward(img)\n", "    return outs\n", "\n", "\n", "def create_featuremap_vis(model=None, use_gpu=True, init_shape=(768, 768, 3)):\n", "    #model.forward = partial(forward, model)\n", "    featurevis = FeatureMapVis_siamese(model, use_gpu)\n", "    featurevis.set_hook_style(init_shape[2], init_shape[:2])\n", "    return featurevis\n", "\n", "\n", "def _show_save_data(featurevis, input, img_orig, feature_indexs, filepath, is_show, output_dir):\n", "    show_datas = []\n", "    for feature_index in feature_indexs:\n", "        feature_map = featurevis.run(input, feature_index=feature_index)[0]\n", "        data = show_tensor(feature_map[0], resize_hw=input.shape[:2], show_split=False, is_show=False)[0]\n", "        am_data = cv2.addWeighted(data, 0.5, img_orig, 0.5, 0)\n", "        show_datas.append(am_data)\n", "    if is_show:\n", "        show_img(show_datas)\n", "    if output_dir is not None:\n", "        filename = os.path.join(output_dir,\n", "                                Path(filepath).name\n", "                                )\n", "        if len(show_datas) == 1:\n", "            cv2.imwrite(show_datas[0], filename)\n", "        else:\n", "            for i in range(len(show_datas)):\n", "                fname, suffix = os.path.splitext(filename)\n", "                cv2.imwrite(fname + '_{}'.format(str(i)) + suffix,show_datas[i])\n", "\n", "def _show_save_data_siamese(featurevis, input, img_orig, feature_indexs, fmaps = None):\n", "    show_datas = []\n", "    imgs = img_orig.detach().cpu().numpy().transpose([0, 2, 3, 1])\n", "    if fmaps is None:\n", "        for feature_index in feature_indexs:\n", "            feature_maps = featurevis.run(input, feature_index=feature_index)[0]\n", "            am_data = []\n", "            for img,feature_map in zip(imgs,feature_maps[:3]):\n", "                data = show_tensor(feature_map, resize_hw=input.shape[2:], show_split=False, is_show=False)[0]\n", "                am_data.append(255-data)\n", "            show_datas.append(am_data)\n", "    else:\n", "        for i in range(len(fmaps)):\n", "            am_data = []\n", "            for img, feature_map in zip(imgs, fmaps[i]):\n", "                data = show_tensor(feature_map, resize_hw=input.shape[2:], show_split=False, is_show=False)[0]\n", "                am_data.append(data)\n", "            show_datas.append(am_data)\n", "    return show_datas\n", "\n", "def show_featuremap_from_imgs(featurevis, feature_indexs, img_dir, is_show, output_dir):\n", "    if not isinstance(feature_indexs, (list, tuple)):\n", "        feature_indexs = [feature_indexs]\n", "    img_paths = [os.path.join(img_dir, x) for x in os.listdir(img_dir) if 'jpg' in x]\n", "    for path in img_paths:\n", "        img = cv2.imread(path)\n", "        # 这里是输入模型前的图片处理\n", "        input = img.astype(np.float32).copy()\n", "        # 显示特征图\n", "        _show_save_data(featurevis, input, img, feature_indexs, path, is_show, output_dir)\n", "\n", "\n", "\n", "if __name__ == '__main__':\n", "    img_dir = '.'\n", "    out_dir = './out'\n", "\n", "    init_shape = (1024, 1024, 3)  # 值不重要，只要前向一遍网络时候不报错即可\n", "    feature_index = [32, 70, 96, 155]  # 可视化的层索引\n", "    use_gpu = True\n", "    is_show = False\n", "    model = torchvision.models.resnet50(pretrained=True)  # 这里创建模型\n", "    #model = torch.load(path)\n", "    if use_gpu:\n", "        model.cuda()\n", "    featurevis = create_featuremap_vis(model, use_gpu, init_shape)\n", "    show_featuremap_from_imgs(featurevis, feature_index, img_dir, is_show, out_dir)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Ipython display来展示\n", "\n", "from IPython.display import display, Code\n", "\n", "code_str = df.loc[0,\"content\"]\n", "display(Code(code_str, language=\"python\"))"]}, {"cell_type": "markdown", "id": "192cf7ca-1d01-4469-bfae-e77ed69de12f", "metadata": {}, "source": ["- **<PERSON><PERSON><PERSON><PERSON>**"]}, {"cell_type": "code", "execution_count": 17, "id": "e1b6d24e-89fa-4dd1-ae94-5c0da4f9cd1b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1/example_train_0.jsonl.zst: 45705517 bytes \n"]}], "source": ["# 先解压\n", "!zstd -d /root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1/example_train_0.jsonl.zst -o /root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1/example_train_0_.jsonl"]}, {"cell_type": "code", "execution_count": 18, "id": "2d81a41f-bc34-494b-b2b7-dd20c7ea7c0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一行 JSON 数据： {\n", "    \"text\": \"<PERSON><PERSON><PERSON><PERSON> Returns To Write And Direct 'Star Wars: Episode IX'\\n09/12/2017 11:11 am ET Updated Sep 12, 2017\\nThe return of the J.J.\\n<PERSON><PERSON> <PERSON>\\nUPDATE: 4:00 p.m. ET — In addition to the director news, \\\"Star Wars\\\" announced that the premiere date for \\\"Episode IX\\\" will be Dec. 20, 2019.\\nStar Wars: Episode IX is scheduled for release on December 20, 2019. pic.twitter.com/rDBqmuHX89\\n— Star Wars (@starwars) September 12, 2017\\nThe Force was with <PERSON><PERSON><PERSON><PERSON> when he launched the new set of \\\"Star Wars\\\" films with \\\"The Force Awakens,\\\" so now <PERSON> is bringing him back.\\nAs Deadline reported on Tuesday, and according to a press release on StarWars.com, <PERSON> will return to write and direct \\\"Star Wars: Episode IX.\\\" The statement reads:\\nA post shared by Star Wars (@starwars) on Sep 12, 2017 at 7:28am PDT\\nAfter Disney unexpectedly parted ways with former \\\"Episode IX\\\" director <PERSON> earlier this month, rumors that <PERSON><PERSON>, who is directing \\\"Star Wars: The Last Jedi,\\\" would take over surfaced. But Deadline reports that <PERSON> decided not to take the offer to direct.\\nOn <PERSON>' hiring, Lucasfilm President <PERSON> said, \\\"With 'The Force Awakens,' J<PERSON>J<PERSON> delivered everything we could have possibly hoped for, and I am so excited that he is coming back to close out this trilogy.\\\"\\nAfter what we saw in \\\"Force Awakens,\\\" we're pretty excited about it, too. We just hope they call it \\\"The Return of the J.J.\\\"\\nThere Were 2 Royal Moments You Might Have Missed At Biden's Inauguration Joe Biden Removed Trump's Diet Coke Button, Twitter Bubbled Up With Jokes Princess Charlene Defends New Buzzcut Hairstyle: 'It Was My Decision' Katy Perry Closes Out Biden's Inauguration Celebration With A Literal Bang\\n'Star Wars' Postage Stamps\\nEntertainment Editor, HuffPost\\nMovies Star Wars J.J. Abrams\",\n", "    \"meta\": {\n", "        \"redpajama_set_name\": \"RedPajamaCommonCrawl\"\n", "    }\n", "}\n"]}], "source": ["import json\n", "\n", "output_file = \"/root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1/example_train_0_.jsonl\"\n", "\n", "# 读取 `.jsonl` 文件的第一行\n", "with open(output_file, \"r\", encoding=\"utf-8\") as jsonl_file:\n", "    first_line = jsonl_file.readline().strip()\n", "    if first_line:\n", "        try:\n", "            json_data = json.loads(first_line)\n", "            print(\"第一行 JSON 数据：\", json.dumps(json_data, indent=4, ensure_ascii=False))\n", "        except json.JSONDecodeError:\n", "            print(\"❌ JSON 格式错误！原始数据：\", first_line)\n", "    else:\n", "        print(\"❌ 读取失败，文件为空！\")"]}, {"cell_type": "markdown", "id": "8bc3e2ef-597e-43d2-bc60-67e8d6d99c0a", "metadata": {}, "source": ["- **openr1**"]}, {"cell_type": "markdown", "id": "b9207fc8-aac1-46d3-8109-704406eb6324", "metadata": {}, "source": ["在huggingface页面上也可看到相应的结构 → https://huggingface.co/datasets/open-r1/OpenR1-Math-220k"]}, {"cell_type": "code", "execution_count": 19, "id": "8ea73776-83dd-4f54-863f-a600b3794600", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一行数据：\n", " {\n", "    \"problem\":\"3. As shown in the figure, the side lengths of $\\\\triangle A B C$ are $A B=14, B C$ $=16, A C=26, P$ is a point on the angle bisector $A D$ of $\\\\angle A$, and $B P \\\\perp A D, M$ is the midpoint of $B C$. Find the value of $P M$ $\\\\qquad$\",\n", "    \"solution\":\"3. 6 .\\n\\nFrom the figure, take $B^{\\\\prime}$ on $A C$ such that $A B^{\\\\prime}=A B=14$, then $B^{\\\\prime} C=12$. Since $\\\\triangle A B B^{\\\\prime}$ is an isosceles triangle, we know that the intersection point of $B B^{\\\\prime}$ and $A D$ is $P$ (concurrency of five lines), so $P$ is the midpoint of $B B^{\\\\prime}$.\",\n", "    \"answer\":\"6\",\n", "    \"problem_type\":\"Geometry\",\n", "    \"question_type\":\"math-word-problem\",\n", "    \"source\":\"cn_contest\",\n", "    \"uuid\":\"3fd1e4c6-8bd4-53d2-b40a-7c72360707e4\",\n", "    \"is_reasoning_complete\":[\n", "        true,\n", "        true\n", "    ],\n", "    \"generations\":[\n", "        \"<think>\\nA<PERSON>right, let's tackle this geometry problem. So, we have triangle ABC with sides AB = 14, BC = 16, and AC = 26. Point P is on the angle bisector AD of angle A, and BP is perpendicular to AD. M is the midpoint of BC. We need to find the length of PM. Hmm, okay, let's start by visualizing the triangle and the given points.\\n\\nFirst, I should recall some properties related to angle bisectors and midpoints. The angle bisector theorem states that the angle bisector divides the opposite side in the ratio of the adjacent sides. So, in triangle ABC, AD is the angle bisector of angle A, so it should divide BC into segments proportional to AB and AC. That is, BD\\/DC = AB\\/AC = 14\\/26 = 7\\/13. Since BC is 16, we can find BD and DC.\\n\\nLet me calculate BD and DC. Let BD = 7k and DC = 13k. Then BD + DC = 7k + 13k = 20k = 16. So, k = 16\\/20 = 4\\/5. Therefore, BD = 7*(4\\/5) = 28\\/5 = 5.6 and DC = 13*(4\\/5) = 52\\/5 = 10.4. So, BD = 5.6 and DC = 10.4. Got that.\\n\\nNow, since M is the midpoint of BC, BM = MC = 16\\/2 = 8. So, <PERSON> divides BC into two equal parts of 8 each. So, <PERSON> is located 8 units from B and C.\\n\\nNow, we need to find PM. To do this, perhaps coordinate geometry might help. Let me set up a coordinate system. Let me place point A at the origin (0,0) for simplicity. Then, we can place point B somewhere in the plane, point C somewhere else, and compute coordinates accordingly. But since we have lengths, maybe using coordinates would allow us to compute everything algebraically.\\n\\nAlternatively, maybe using vectors or trigonometry. Let me see. But coordinate geometry seems feasible here.\\n\\nLet me proceed step by step. Let's set point A at (0,0). Let me place AD along the x-axis for simplicity since AD is an angle bisector. Wait, but angle bisector is not necessarily along the x-axis unless we position the triangle that way. Alternatively, maybe align AD along the x-axis. Let me try that.\\n\\nSo, let's set point A at (0,0), and let AD be along the positive x-axis. Then, point D is on BC, and AD is the angle bisector. Then, we can find coordinates of B and C such that AD is the x-axis.\\n\\nWait, but maybe that complicates things because BC is not along any axis. Alternatively, maybe place point B at (0,0), point C at (16,0), but then point A is somewhere above the x-axis. But given that AB = 14 and AC = 26, that might be manageable.\\n\\nWait, if I place B at (0,0), C at (16,0), then BC is along the x-axis. Then, point A is somewhere above the x-axis. Then, AB = 14, AC = 26. Let's find the coordinates of A. Let me call the coordinates of A as (x, y). Then, distance from A to B is sqrt(x^2 + y^2) = 14, and distance from A to C is sqrt((x - 16)^2 + y^2) = 26.\\n\\nSo, we have two equations:\\n\\n1) x² + y² = 14² = 196\\n\\n2) (x - 16)² + y² = 26² = 676\\n\\nSubtracting equation 1 from equation 2:\\n\\n(x - 16)² + y² - x² - y² = 676 - 196\\n\\nExpand (x - 16)^2: x² -32x +256\\n\\nSo, x² -32x +256 + y² -x² - y² = 480\\n\\nSimplify: -32x +256 = 480\\n\\nThen, -32x = 480 -256 = 224\\n\\nSo, x = -224 \\/32 = -7. So, x = -7. Therefore, the coordinates of A are (-7, y). Then, from equation 1, x² + y² = 196. So, (-7)^2 + y² = 196 => 49 + y² =196 => y²=147 => y= sqrt(147)=7*sqrt(3). So, coordinates of A are (-7, 7√3).\\n\\nOkay, so let's recap. Coordinates:\\n\\n- B is at (0,0)\\n\\n- C is at (16,0)\\n\\n- A is at (-7, 7√3)\\n\\nThen, AD is the angle bisector of angle A. We found earlier that BD = 5.6 and DC =10.4, so D is located 5.6 units from B along BC. Since BC is from (0,0) to (16,0), so D is at (5.6, 0). Because starting from B at (0,0), moving 5.6 units along the x-axis gives D at (5.6, 0).\\n\\nSo, AD is the line from A (-7,7√3) to D (5.6,0). Let me write 5.6 as 28\\/5 to keep it exact. 5.6 = 28\\/5. So, D is at (28\\/5, 0).\\n\\nWe need to find the equation of AD. Let's compute the slope of AD first. The slope m is (0 - 7√3)\\/(28\\/5 - (-7)) = (-7√3)\\/(28\\/5 +35\\/5) = (-7√3)\\/(63\\/5) = (-7√3)*(5\\/63) = (-35√3)\\/63 = (-5√3)\\/9.\\n\\nTherefore, the equation of AD is y - 7√3 = (-5√3)\\/9 (x +7). Let's confirm that. Starting at point A (-7,7√3), slope -5√3\\/9.\\n\\nAlternatively, parametric equations for AD. Since it's a line from A to D, we can parametrize it as t going from 0 to 1:\\n\\nx(t) = -7 + t*(28\\/5 +7) = -7 + t*(28\\/5 +35\\/5) = -7 + t*(63\\/5)\\n\\ny(t) = 7√3 + t*(0 -7√3) = 7√3 -7√3 t\\n\\nBut perhaps another approach. Since P is a point on AD such that BP is perpendicular to AD. So, BP ⊥ AD.\\n\\nGiven that, perhaps we can find the coordinates of P by finding where BP is perpendicular to AD.\\n\\nSo, if we can express BP as a line that is perpendicular to AD and passes through B, then the intersection of BP with AD is point P.\\n\\nWait, BP is perpendicular to AD, so BP is a line starting at B (0,0) and going in the direction perpendicular to AD. Since AD has slope -5√3\\/9, the perpendicular slope is the negative reciprocal, which is 9\\/(5√3) = 3√3\\/5.\\n\\nTherefore, the equation of BP is y = (3√3\\/5)x.\\n\\nNow, find the intersection of BP with AD. AD has equation y = (-5√3\\/9)(x +7) +7√3. Wait, let me rederive the equation of AD to be sure.\\n\\nPoint A is (-7,7√3), point D is (28\\/5,0). So, slope m = (0 -7√3)\\/(28\\/5 +7) = (-7√3)\\/(63\\/5) = -5√3\\/9 as before. So, equation of AD: y -7√3 = -5√3\\/9 (x +7). So, y = (-5√3\\/9)(x +7) +7√3.\\n\\nLet's compute that:\\n\\ny = (-5√3\\/9)x - (35√3\\/9) +7√3\\n\\nConvert 7√3 to 63√3\\/9:\\n\\ny = (-5√3\\/9)x -35√3\\/9 +63√3\\/9 = (-5√3\\/9)x +28√3\\/9\\n\\nSo, equation of AD: y = (-5√3\\/9)x +28√3\\/9\\n\\nEquation of BP: y = (3√3\\/5)x\\n\\nFind intersection point P between BP and AD. Set the two equations equal:\\n\\n(3√3\\/5)x = (-5√3\\/9)x +28√3\\/9\\n\\nMultiply both sides by 45 to eliminate denominators:\\n\\n45*(3√3\\/5)x = 45*(-5√3\\/9)x +45*(28√3\\/9)\\n\\nSimplify:\\n\\n9*3√3 x = -5*5√3 x + 5*28√3\\n\\n27√3 x = -25√3 x +140√3\\n\\nBring all terms to left:\\n\\n27√3 x +25√3 x -140√3 =0\\n\\n52√3 x =140√3\\n\\nDivide both sides by √3:\\n\\n52x =140\\n\\nx=140\\/52=35\\/13≈2.692\\n\\nThen, substitute x=35\\/13 into BP equation: y=(3√3\\/5)*(35\\/13)= (105√3)\\/65=21√3\\/13≈2.886\\n\\nSo, coordinates of P are (35\\/13,21√3\\/13)\\n\\nNow, coordinates of M, the midpoint of BC. Since B is at (0,0), C at (16,0), then M is at ((0+16)\\/2, (0+0)\\/2)=(8,0)\\n\\nSo, M is at (8,0). Now, need to find PM. Coordinates of P are (35\\/13,21√3\\/13), coordinates of M are (8,0). The distance PM is sqrt[(8 -35\\/13)^2 + (0 -21√3\\/13)^2]\\n\\nFirst, compute 8 -35\\/13: 8=104\\/13, so 104\\/13 -35\\/13=69\\/13\\n\\nThen, compute the distance squared: (69\\/13)^2 + (21√3\\/13)^2\\n\\nCalculate each term:\\n\\n(69\\/13)^2 = (69^2)\\/(13^2)=4761\\/169\\n\\n(21√3\\/13)^2= (21^2)*(3)\\/(13^2)=441*3\\/169=1323\\/169\\n\\nSum: 4761 +1323=6084\\n\\nThus, sqrt(6084\\/169)=sqrt(36*169)\\/13=6*13\\/13=6\\n\\nWait, wait. 6084 divided by 169. Let me compute that. 169*36=6084. Because 169*30=5070, 169*6=1014, 5070+1014=6084. So sqrt(6084\\/169)=sqrt(36)=6.\\n\\nSo, PM=6. So, the answer is 6. Therefore, PM=6.\\n\\nWait, that seems straightforward. Let me just verify the calculations step by step to make sure.\\n\\nFirst, coordinates of A, B, C:\\n\\nB (0,0), C (16,0), A (-7,7√3). Correct, since AB distance is sqrt((-7-0)^2 + (7√3 -0)^2)=sqrt(49 + 147)=sqrt(196)=14. Correct. AC distance is sqrt((16 +7)^2 + (0 -7√3)^2)=sqrt(23² + (7√3)^2)=sqrt(529 +147)=sqrt(676)=26. Correct.\\n\\nCoordinates of D: BD=28\\/5=5.6, so D is at (5.6,0)=(28\\/5,0). Correct.\\n\\nEquation of AD: slope calculated as -5√3\\/9, equation y = (-5√3\\/9)x +28√3\\/9. Correct.\\n\\nEquation of BP: perpendicular to AD, slope 3√3\\/5, passing through B (0,0), so y=(3√3\\/5)x. Correct.\\n\\nIntersection: solving (3√3\\/5)x = (-5√3\\/9)x +28√3\\/9. Multiplying through by 45, got 27√3 x = -25√3 x +140√3, leading to 52√3 x=140√3, so x=140\\/52=35\\/13. Then y=3√3\\/5*(35\\/13)=21√3\\/13. Correct coordinates of P.\\n\\nMidpoint M is (8,0). Distance PM: sqrt[(8 -35\\/13)^2 + (0 -21√3\\/13)^2]. Calculated 8 -35\\/13=69\\/13. Squared, (69\\/13)^2=4761\\/169. Then (21√3\\/13)^2=1323\\/169. Sum=6084\\/169=36. sqrt(36)=6. So PM=6. All steps check out. So, the answer is 6.\\n\\n**Final Answer**\\n\\\\boxed{6}\\n<\\/think>\\n\\nGiven triangle \\\\( \\\\triangle ABC \\\\) with side lengths \\\\( AB = 14 \\\\), \\\\( BC = 16 \\\\), and \\\\( AC = 26 \\\\). Point \\\\( P \\\\) is on the angle bisector \\\\( AD \\\\) of \\\\( \\\\angle A \\\\), and \\\\( BP \\\\perp AD \\\\). \\\\( M \\\\) is the midpoint of \\\\( BC \\\\). We need to find the value of \\\\( PM \\\\).\\n\\n1. **Using the Angle Bisector Theorem**:\\n   - The angle bisector \\\\( AD \\\\) divides \\\\( BC \\\\) in the ratio \\\\( AB : AC = 14 : 26 = 7 : 13 \\\\).\\n   - Therefore, \\\\( BD = \\\\frac{7}{20} \\\\times 16 = \\\\frac{28}{5} \\\\) and \\\\( DC = \\\\frac{13}{20} \\\\times 16 = \\\\frac{52}{5} \\\\).\\n\\n2. **Coordinates Setup**:\\n   - Place \\\\( B \\\\) at \\\\( (0, 0) \\\\), \\\\( C \\\\) at \\\\( (16, 0) \\\\), and find coordinates of \\\\( A \\\\).\\n   - Solving the system of equations for distances \\\\( AB = 14 \\\\) and \\\\( AC = 26 \\\\), we find \\\\( A \\\\) at \\\\( (-7, 7\\\\sqrt{3}) \\\\).\\n\\n3. **Coordinates of \\\\( D \\\\)**:\\n   - \\\\( D \\\\) is the point on \\\\( BC \\\\) such that \\\\( BD = \\\\frac{28}{5} \\\\), so \\\\( D \\\\) is at \\\\( \\\\left( \\\\frac{28}{5}, 0 \\\\right) \\\\).\\n\\n4. **Equations of Lines**:\\n   - Slope of \\\\( AD \\\\): \\\\( \\\\frac{0 - 7\\\\sqrt{3}}{\\\\frac{28}{5} + 7} = -\\\\frac{5\\\\sqrt{3}}{9} \\\\).\\n   - Equation of \\\\( AD \\\\): \\\\( y = -\\\\frac{5\\\\sqrt{3}}{9}x + \\\\frac{28\\\\sqrt{3}}{9} \\\\).\\n   - Slope of \\\\( BP \\\\) (perpendicular to \\\\( AD \\\\)): \\\\( \\\\frac{9}{5\\\\sqrt{3}} = \\\\frac{3\\\\sqrt{3}}{5} \\\\).\\n   - Equation of \\\\( BP \\\\): \\\\( y = \\\\frac{3\\\\sqrt{3}}{5}x \\\\).\\n\\n5. **Intersection of \\\\( BP \\\\) and \\\\( AD \\\\)**:\\n   - Solving \\\\( \\\\frac{3\\\\sqrt{3}}{5}x = -\\\\frac{5\\\\sqrt{3}}{9}x + \\\\frac{28\\\\sqrt{3}}{9} \\\\):\\n     - \\\\( x = \\\\frac{35}{13} \\\\), \\\\( y = \\\\frac{21\\\\sqrt{3}}{13} \\\\).\\n   - Coordinates of \\\\( P \\\\): \\\\( \\\\left( \\\\frac{35}{13}, \\\\frac{21\\\\sqrt{3}}{13} \\\\right) \\\\).\\n\\n6. **Midpoint \\\\( M \\\\)**:\\n   - \\\\( M \\\\) is the midpoint of \\\\( BC \\\\), so \\\\( M \\\\) is at \\\\( (8, 0) \\\\).\\n\\n7. **Distance \\\\( PM \\\\)**:\\n   - Using distance formula: \\\\( PM = \\\\sqrt{\\\\left( 8 - \\\\frac{35}{13} \\\\right)^2 + \\\\left( 0 - \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} \\\\).\\n   - Simplifying, \\\\( PM = \\\\sqrt{\\\\left( \\\\frac{69}{13} \\\\right)^2 + \\\\left( \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} = \\\\sqrt{\\\\frac{6084}{169}} = 6 \\\\).\\n\\nThus, the value of \\\\( PM \\\\) is \\\\(\\\\boxed{6}\\\\).\",\n", "        \"<think>\\nOkay, so I need to find the length of PM in triangle ABC where AB=14, BC=16, AC=26. P is a point on the angle bisector AD of angle A, and <PERSON> is perpendicular to AD. M is the midpoint of BC. Hmm, let's start by visualizing the problem. \\n\\nFirst, let me sketch triangle ABC. AB is 14, BC is 16, and AC is 26. Wait, those side lengths seem a bit off. If AB=14, BC=16, and AC=26, then triangle inequality might not hold. Let me check. \\n\\nAB + BC = 14 + 16 = 30, which is greater than AC=26. Then AB + AC = 14 + 26 = 40, which is greater than BC=16. BC + AC = 16 + 26 = 42, which is greater than AB=14. So yes, triangle inequalities are satisfied. Alright.\\n\\nNow, point D is on BC such that AD is the angle bisector of angle A. There's a theorem about angle bisectors: the ratio of the two segments created on the opposite side is equal to the ratio of the adjacent sides. So BD\\/DC = AB\\/AC. Let me calculate BD and DC.\\n\\nGiven AB=14, AC=26, so BD\\/DC = 14\\/26 = 7\\/13. Since BD + DC = BC=16, let me set BD = 7k and DC =13k. Then 7k +13k=20k=16, so k=16\\/20=4\\/5. Therefore, BD=7*(4\\/5)=28\\/5=5.6 and DC=13*(4\\/5)=52\\/5=10.4. So D divides BC into BD=28\\/5 and DC=52\\/5.\\n\\nAD is the angle bisector. Now P is a point on AD such that BP is perpendicular to AD. I need to find PM, where M is the midpoint of BC. Since M is the midpoint, BM=MC=8, since BC=16.\\n\\nHmm, okay. Let me think about coordinates. Maybe coordinate geometry can help here. Let me place point B at the origin to make calculations easier. Let me set coordinate system with point B at (0,0). Let me denote coordinates:\\n\\nLet me let point B be at (0,0). Then, since BC is 16 units, and if I place BC along the x-axis, then point C would be at (16,0). Then M, the midpoint of BC, would be at (8,0).\\n\\nBut wait, AB is 14, AC is 26. So point A must be somewhere such that the distance from A to B is 14, and from A to C is 26. Let me find coordinates for point A.\\n\\nLet me set coordinate system with B at (0,0), C at (16,0). Let coordinates of A be (x,y). Then distance AB=14, so sqrt(x^2 + y^2)=14, and AC=26, so sqrt((x-16)^2 + y^2)=26. Let's square both equations:\\n\\nx² + y² = 196 ...(1)\\n\\n(x -16)² + y² = 676 ...(2)\\n\\nSubtract equation (1) from (2):\\n\\n(x -16)² + y² - x² - y² = 676 - 196\\n\\nExpand (x-16)^2: x² -32x +256 -x² = 480\\n\\nSimplify: -32x +256 = 480\\n\\nThen -32x = 480 -256 = 224\\n\\nTherefore x = -224 \\/32 = -7.\\n\\nSo x= -7. Then from equation (1), x² + y²=196 => (-7)^2 + y²=196 =>49 + y²=196 => y²=147 => y= sqrt(147)=7*sqrt(3). So coordinates of A are (-7,7√3).\\n\\nAlright, so coordinates:\\n\\nB: (0,0)\\n\\nC: (16,0)\\n\\nA: (-7,7√3)\\n\\nM is midpoint of BC: (8,0)\\n\\nAD is the angle bisector. We already found D divides BC into BD=28\\/5=5.6 and DC=10.4. So coordinate of D: Since BD=28\\/5=5.6, starting from B(0,0) towards C(16,0), D is at (5.6,0). 5.6 is 28\\/5, so as a coordinate, D is (28\\/5,0).\\n\\nSo angle bisector AD connects A(-7,7√3) to D(28\\/5,0). Now, P is a point on AD such that BP is perpendicular to AD. So we need to find point P on AD where BP is perpendicular to AD. Then, once we have coordinates of P, we can compute PM, the distance from P to M(8,0).\\n\\nSo the plan is:\\n\\n1. Find parametric equations for AD.\\n\\n2. Find the equation of BP such that it's perpendicular to AD.\\n\\n3. Find point P as the intersection of BP and AD.\\n\\n4. Compute coordinates of P.\\n\\n5. Compute distance from P to M(8,0).\\n\\nAlternatively, since BP is perpendicular to AD, and P is on AD, we can parametrize AD and find the foot of the perpendicular from B to AD. That would be point P.\\n\\nYes, that's another way. Since BP is perpendicular to AD, P is the foot of the perpendicular from B to AD.\\n\\nSo, to find P, we can compute the foot of the perpendicular from B(0,0) to line AD.\\n\\nFirst, let's find the equation of line AD.\\n\\nCoordinates of A: (-7,7√3)\\n\\nCoordinates of D: (28\\/5,0)\\n\\nSlope of AD: (0 -7√3)\\/(28\\/5 - (-7)) = (-7√3)\\/(28\\/5 +35\\/5)= (-7√3)\\/(63\\/5)= (-7√3)*(5\\/63)= (-35√3)\\/63= (-5√3)\\/9.\\n\\nSo slope of AD is -5√3\\/9. Therefore, the equation of AD can be written as:\\n\\ny -7√3 = (-5√3\\/9)(x +7)\\n\\nBut maybe easier to parametrize AD. Let's parametrize AD with a parameter t.\\n\\nFrom point A(-7,7√3) to D(28\\/5,0). The vector from A to D is (28\\/5 - (-7), 0 -7√3) = (28\\/5 +35\\/5, -7√3)= (63\\/5, -7√3). So parametric equations:\\n\\nx = -7 + (63\\/5)t\\n\\ny =7√3 -7√3 t\\n\\nWhere t ranges from 0 to 1.\\n\\nAlternatively, we can parametrize it as:\\n\\nx = -7 + (63\\/5)t\\n\\ny =7√3(1 - t)\\n\\nt from 0 to1.\\n\\nSo any point P on AD can be written as (-7 + (63\\/5)t,7√3(1 - t)) for some t between 0 and1.\\n\\nNow, BP is perpendicular to AD. The vector BP is from B(0,0) to P(-7 + (63\\/5)t,7√3(1 - t)).\\n\\nSo the direction vector of BP is (-7 + (63\\/5)t,7√3(1 - t)).\\n\\nThe direction vector of AD is (63\\/5, -7√3). Therefore, the dot product of BP and AD should be zero since they are perpendicular.\\n\\nWait, no. Wait, BP is perpendicular to AD. So the direction vector of AD is (63\\/5, -7√3). The direction vector of BP is (x_p -0, y_p -0) = (x_p, y_p). So we need the vector BP (x_p, y_p) to be perpendicular to the direction vector of AD (63\\/5, -7√3). Therefore, their dot product is zero.\\n\\nSo:\\n\\nx_p*(63\\/5) + y_p*(-7√3) =0\\n\\nBut x_p and y_p are coordinates of P, which is on AD. So x_p = -7 + (63\\/5)t, y_p =7√3(1 - t). Substitute into the equation:\\n\\n[ -7 + (63\\/5)t ]*(63\\/5) + [7√3(1 - t)]*(-7√3) =0\\n\\nLet me compute each term:\\n\\nFirst term: [ -7 + (63\\/5)t ]*(63\\/5)\\n\\n= -7*(63\\/5) + (63\\/5)*(63\\/5) t\\n\\n= -441\\/5 + (3969\\/25) t\\n\\nSecond term: [7√3(1 - t)]*(-7√3)\\n\\n= -49*3*(1 -t )\\n\\n= -147*(1 -t )\\n\\nSo combining both terms:\\n\\n-441\\/5 + (3969\\/25) t -147 +147 t =0\\n\\nLet me convert all terms to 25 denominator:\\n\\n-441\\/5 = -2205\\/25\\n\\n-147 = -3675\\/25\\n\\n147 t = (3675\\/25) t\\n\\nSo equation becomes:\\n\\n-2205\\/25 + (3969\\/25) t -3675\\/25 + (3675\\/25) t =0\\n\\nCombine terms:\\n\\n[ -2205 -3675 ]\\/25 + (3969 +3675)\\/25 t =0\\n\\nCompute numerators:\\n\\n-2205 -3675 = -5880\\n\\n3969 +3675 = 7644\\n\\nSo:\\n\\n-5880\\/25 + 7644\\/25 t =0\\n\\nMultiply both sides by 25:\\n\\n-5880 +7644 t =0\\n\\n7644 t =5880\\n\\nt=5880\\/7644\\n\\nSimplify the fraction:\\n\\nDivide numerator and denominator by 12:\\n\\n5880 ÷12=490\\n\\n7644 ÷12=637\\n\\nWait, 5880 ÷12: 12*490=5880. 7644 ÷12=637. So t=490\\/637\\n\\nCheck if 490 and 637 have a common factor. 490=49*10=7*7*10. 637=7*91=7*13*7. Wait, 7*91=637, 91=13*7. So 637=7*7*13. So 490=7*7*10, 637=7*7*13. So common factors are 7*7=49. So divide numerator and denominator by 49:\\n\\n490 ÷49=10\\n\\n637 ÷49=13. 13*49=637. Yes. So t=10\\/13.\\n\\nTherefore, t=10\\/13.\\n\\nSo coordinates of P are:\\n\\nx_p= -7 + (63\\/5)*(10\\/13)= -7 + (63*10)\\/(5*13)= -7 + (630)\\/(65)= -7 + 126\\/13.\\n\\nConvert -7 to -91\\/13, so x_p= (-91\\/13 +126\\/13)=35\\/13.\\n\\nSimilarly, y_p=7√3(1 -10\\/13)=7√3*(3\\/13)=21√3\\/13.\\n\\nSo coordinates of P are (35\\/13,21√3\\/13).\\n\\nNow, coordinates of M are (8,0). So PM is the distance between (35\\/13,21√3\\/13) and (8,0).\\n\\nCompute the difference in x: 8 -35\\/13= (104\\/13 -35\\/13)=69\\/13.\\n\\nDifference in y:0 -21√3\\/13= -21√3\\/13.\\n\\nTherefore, PM= sqrt[(69\\/13)^2 + (-21√3\\/13)^2]\\n\\nCompute each term:\\n\\n(69\\/13)^2= (69^2)\\/(13^2)=4761\\/169\\n\\n(-21√3\\/13)^2= (441*3)\\/169=1323\\/169\\n\\nSum:4761 +1323=6084. 6084\\/169=6084 ÷169.\\n\\n169*36=6084, since 169*30=5070, 169*6=1014, 5070+1014=6084. So sqrt(6084\\/169)=sqrt(36*169)\\/13= (6*13)\\/13=6. Wait, wait:\\n\\nWait, sqrt(6084\\/169)=sqrt(36*169)\\/13= (6*13)\\/13=6. Wait, let me check:\\n\\nWait 6084\\/169=36. So sqrt(36)=6. Therefore, PM=6.\\n\\nWait, that's a nice integer. So PM=6.\\n\\nWait, let me confirm the steps again. So coordinates of P were found by parametrizing AD, then using the condition that BP is perpendicular to AD. Then calculated t=10\\/13, found coordinates of P as (35\\/13,21√3\\/13). Then distance from P to M(8,0) is sqrt[(69\\/13)^2 + (21√3\\/13)^2]. Then 69^2=4761, (21√3)^2=441*3=1323, sum 4761+1323=6084. 6084 divided by 169=36. sqrt(36)=6. So indeed PM=6. So the answer is 6. So boxed answer is \\\\boxed{6}\\n\\n**Final Answer**\\n\\\\boxed{6}\\n<\\/think>\\n\\nTo find the value of \\\\( PM \\\\) in triangle \\\\( ABC \\\\) with \\\\( AB = 14 \\\\), \\\\( BC = 16 \\\\), \\\\( AC = 26 \\\\), where \\\\( P \\\\) is a point on the angle bisector \\\\( AD \\\\) such that \\\\( BP \\\\perp AD \\\\), and \\\\( M \\\\) is the midpoint of \\\\( BC \\\\):\\n\\n1. **Determine coordinates of points**:\\n   - Place \\\\( B \\\\) at \\\\( (0, 0) \\\\) and \\\\( C \\\\) at \\\\( (16, 0) \\\\).\\n   - Find coordinates of \\\\( A \\\\) using the distances \\\\( AB = 14 \\\\) and \\\\( AC = 26 \\\\). Solving the system of equations, \\\\( A \\\\) is found to be \\\\( (-7, 7\\\\sqrt{3}) \\\\).\\n   - Midpoint \\\\( M \\\\) of \\\\( BC \\\\) is \\\\( (8, 0) \\\\).\\n\\n2. **Find coordinates of \\\\( D \\\\)**:\\n   - \\\\( D \\\\) divides \\\\( BC \\\\) in the ratio \\\\( AB:AC = 7:13 \\\\). Solving, \\\\( BD = \\\\frac{28}{5} \\\\) and \\\\( DC = \\\\frac{52}{5} \\\\). Thus, \\\\( D \\\\) is \\\\( \\\\left( \\\\frac{28}{5}, 0 \\\\right) \\\\).\\n\\n3. **Parametrize line \\\\( AD \\\\)**:\\n   - Parametric equations for \\\\( AD \\\\) are \\\\( x = -7 + \\\\frac{63}{5}t \\\\) and \\\\( y = 7\\\\sqrt{3}(1 - t) \\\\).\\n\\n4. **Find point \\\\( P \\\\)**:\\n   - \\\\( P \\\\) is the foot of the perpendicular from \\\\( B \\\\) to \\\\( AD \\\\). Using the condition that \\\\( BP \\\\perp AD \\\\), solve for parameter \\\\( t \\\\) which gives \\\\( t = \\\\frac{10}{13} \\\\).\\n   - Coordinates of \\\\( P \\\\) are \\\\( \\\\left( \\\\frac{35}{13}, \\\\frac{21\\\\sqrt{3}}{13} \\\\right) \\\\).\\n\\n5. **Calculate distance \\\\( PM \\\\)**:\\n   - Using the distance formula between \\\\( P \\\\left( \\\\frac{35}{13}, \\\\frac{21\\\\sqrt{3}}{13} \\\\right) \\\\) and \\\\( M (8, 0) \\\\):\\n     \\\\[\\n     PM = \\\\sqrt{\\\\left( 8 - \\\\frac{35}{13} \\\\right)^2 + \\\\left( 0 - \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} = \\\\sqrt{\\\\left( \\\\frac{69}{13} \\\\right)^2 + \\\\left( -\\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} = \\\\sqrt{\\\\frac{6084}{169}} = \\\\sqrt{36} = 6\\n     \\\\]\\n\\nThus, the value of \\\\( PM \\\\) is \\\\(\\\\boxed{6}\\\\).\"\n", "    ],\n", "    \"correctness_math_verify\":[\n", "        true,\n", "        true\n", "    ],\n", "    \"correctness_llama\":null,\n", "    \"finish_reasons\":null,\n", "    \"correctness_count\":2,\n", "    \"messages\":[\n", "        {\n", "            \"content\":\"3. As shown in the figure, the side lengths of $\\\\triangle A B C$ are $A B=14, B C$ $=16, A C=26, P$ is a point on the angle bisector $A D$ of $\\\\angle A$, and $B P \\\\perp A D, M$ is the midpoint of $B C$. Find the value of $P M$ $\\\\qquad$\",\n", "            \"role\":\"user\"\n", "        },\n", "        {\n", "            \"content\":\"<think>\\nA<PERSON>right, let's tackle this geometry problem. So, we have triangle ABC with sides AB = 14, BC = 16, and AC = 26. Point P is on the angle bisector AD of angle A, and BP is perpendicular to AD. M is the midpoint of BC. We need to find the length of PM. Hmm, okay, let's start by visualizing the triangle and the given points.\\n\\nFirst, I should recall some properties related to angle bisectors and midpoints. The angle bisector theorem states that the angle bisector divides the opposite side in the ratio of the adjacent sides. So, in triangle ABC, AD is the angle bisector of angle A, so it should divide BC into segments proportional to AB and AC. That is, BD\\/DC = AB\\/AC = 14\\/26 = 7\\/13. Since BC is 16, we can find BD and DC.\\n\\nLet me calculate BD and DC. Let BD = 7k and DC = 13k. Then BD + DC = 7k + 13k = 20k = 16. So, k = 16\\/20 = 4\\/5. Therefore, BD = 7*(4\\/5) = 28\\/5 = 5.6 and DC = 13*(4\\/5) = 52\\/5 = 10.4. So, BD = 5.6 and DC = 10.4. Got that.\\n\\nNow, since M is the midpoint of BC, BM = MC = 16\\/2 = 8. So, <PERSON> divides BC into two equal parts of 8 each. So, M is located 8 units from B and C.\\n\\nNow, we need to find PM. To do this, perhaps coordinate geometry might help. Let me set up a coordinate system. Let me place point A at the origin (0,0) for simplicity. Then, we can place point B somewhere in the plane, point C somewhere else, and compute coordinates accordingly. But since we have lengths, maybe using coordinates would allow us to compute everything algebraically.\\n\\nAlternatively, maybe using vectors or trigonometry. Let me see. But coordinate geometry seems feasible here.\\n\\nLet me proceed step by step. Let's set point A at (0,0). Let me place AD along the x-axis for simplicity since AD is an angle bisector. Wait, but angle bisector is not necessarily along the x-axis unless we position the triangle that way. Alternatively, maybe align AD along the x-axis. Let me try that.\\n\\nSo, let's set point A at (0,0), and let AD be along the positive x-axis. Then, point D is on BC, and AD is the angle bisector. Then, we can find coordinates of B and C such that AD is the x-axis.\\n\\nWait, but maybe that complicates things because BC is not along any axis. Alternatively, maybe place point B at (0,0), point C at (16,0), but then point A is somewhere above the x-axis. But given that AB = 14 and AC = 26, that might be manageable.\\n\\nWait, if I place B at (0,0), C at (16,0), then BC is along the x-axis. Then, point A is somewhere above the x-axis. Then, AB = 14, AC = 26. Let's find the coordinates of A. Let me call the coordinates of A as (x, y). Then, distance from A to B is sqrt(x^2 + y^2) = 14, and distance from A to C is sqrt((x - 16)^2 + y^2) = 26.\\n\\nSo, we have two equations:\\n\\n1) x² + y² = 14² = 196\\n\\n2) (x - 16)² + y² = 26² = 676\\n\\nSubtracting equation 1 from equation 2:\\n\\n(x - 16)² + y² - x² - y² = 676 - 196\\n\\nExpand (x - 16)^2: x² -32x +256\\n\\nSo, x² -32x +256 + y² -x² - y² = 480\\n\\nSimplify: -32x +256 = 480\\n\\nThen, -32x = 480 -256 = 224\\n\\nSo, x = -224 \\/32 = -7. So, x = -7. Therefore, the coordinates of A are (-7, y). Then, from equation 1, x² + y² = 196. So, (-7)^2 + y² = 196 => 49 + y² =196 => y²=147 => y= sqrt(147)=7*sqrt(3). So, coordinates of A are (-7, 7√3).\\n\\nOkay, so let's recap. Coordinates:\\n\\n- B is at (0,0)\\n\\n- C is at (16,0)\\n\\n- A is at (-7, 7√3)\\n\\nThen, AD is the angle bisector of angle A. We found earlier that BD = 5.6 and DC =10.4, so D is located 5.6 units from B along BC. Since BC is from (0,0) to (16,0), so D is at (5.6, 0). Because starting from B at (0,0), moving 5.6 units along the x-axis gives D at (5.6, 0).\\n\\nSo, AD is the line from A (-7,7√3) to D (5.6,0). Let me write 5.6 as 28\\/5 to keep it exact. 5.6 = 28\\/5. So, D is at (28\\/5, 0).\\n\\nWe need to find the equation of AD. Let's compute the slope of AD first. The slope m is (0 - 7√3)\\/(28\\/5 - (-7)) = (-7√3)\\/(28\\/5 +35\\/5) = (-7√3)\\/(63\\/5) = (-7√3)*(5\\/63) = (-35√3)\\/63 = (-5√3)\\/9.\\n\\nTherefore, the equation of AD is y - 7√3 = (-5√3)\\/9 (x +7). Let's confirm that. Starting at point A (-7,7√3), slope -5√3\\/9.\\n\\nAlternatively, parametric equations for AD. Since it's a line from A to D, we can parametrize it as t going from 0 to 1:\\n\\nx(t) = -7 + t*(28\\/5 +7) = -7 + t*(28\\/5 +35\\/5) = -7 + t*(63\\/5)\\n\\ny(t) = 7√3 + t*(0 -7√3) = 7√3 -7√3 t\\n\\nBut perhaps another approach. Since P is a point on AD such that BP is perpendicular to AD. So, BP ⊥ AD.\\n\\nGiven that, perhaps we can find the coordinates of P by finding where BP is perpendicular to AD.\\n\\nSo, if we can express BP as a line that is perpendicular to AD and passes through B, then the intersection of BP with AD is point P.\\n\\nWait, BP is perpendicular to AD, so BP is a line starting at B (0,0) and going in the direction perpendicular to AD. Since AD has slope -5√3\\/9, the perpendicular slope is the negative reciprocal, which is 9\\/(5√3) = 3√3\\/5.\\n\\nTherefore, the equation of BP is y = (3√3\\/5)x.\\n\\nNow, find the intersection of BP with AD. AD has equation y = (-5√3\\/9)(x +7) +7√3. Wait, let me rederive the equation of AD to be sure.\\n\\nPoint A is (-7,7√3), point D is (28\\/5,0). So, slope m = (0 -7√3)\\/(28\\/5 +7) = (-7√3)\\/(63\\/5) = -5√3\\/9 as before. So, equation of AD: y -7√3 = -5√3\\/9 (x +7). So, y = (-5√3\\/9)(x +7) +7√3.\\n\\nLet's compute that:\\n\\ny = (-5√3\\/9)x - (35√3\\/9) +7√3\\n\\nConvert 7√3 to 63√3\\/9:\\n\\ny = (-5√3\\/9)x -35√3\\/9 +63√3\\/9 = (-5√3\\/9)x +28√3\\/9\\n\\nSo, equation of AD: y = (-5√3\\/9)x +28√3\\/9\\n\\nEquation of BP: y = (3√3\\/5)x\\n\\nFind intersection point P between BP and AD. Set the two equations equal:\\n\\n(3√3\\/5)x = (-5√3\\/9)x +28√3\\/9\\n\\nMultiply both sides by 45 to eliminate denominators:\\n\\n45*(3√3\\/5)x = 45*(-5√3\\/9)x +45*(28√3\\/9)\\n\\nSimplify:\\n\\n9*3√3 x = -5*5√3 x + 5*28√3\\n\\n27√3 x = -25√3 x +140√3\\n\\nBring all terms to left:\\n\\n27√3 x +25√3 x -140√3 =0\\n\\n52√3 x =140√3\\n\\nDivide both sides by √3:\\n\\n52x =140\\n\\nx=140\\/52=35\\/13≈2.692\\n\\nThen, substitute x=35\\/13 into BP equation: y=(3√3\\/5)*(35\\/13)= (105√3)\\/65=21√3\\/13≈2.886\\n\\nSo, coordinates of P are (35\\/13,21√3\\/13)\\n\\nNow, coordinates of M, the midpoint of BC. Since B is at (0,0), C at (16,0), then M is at ((0+16)\\/2, (0+0)\\/2)=(8,0)\\n\\nSo, M is at (8,0). Now, need to find PM. Coordinates of P are (35\\/13,21√3\\/13), coordinates of M are (8,0). The distance PM is sqrt[(8 -35\\/13)^2 + (0 -21√3\\/13)^2]\\n\\nFirst, compute 8 -35\\/13: 8=104\\/13, so 104\\/13 -35\\/13=69\\/13\\n\\nThen, compute the distance squared: (69\\/13)^2 + (21√3\\/13)^2\\n\\nCalculate each term:\\n\\n(69\\/13)^2 = (69^2)\\/(13^2)=4761\\/169\\n\\n(21√3\\/13)^2= (21^2)*(3)\\/(13^2)=441*3\\/169=1323\\/169\\n\\nSum: 4761 +1323=6084\\n\\nThus, sqrt(6084\\/169)=sqrt(36*169)\\/13=6*13\\/13=6\\n\\nWait, wait. 6084 divided by 169. Let me compute that. 169*36=6084. Because 169*30=5070, 169*6=1014, 5070+1014=6084. So sqrt(6084\\/169)=sqrt(36)=6.\\n\\nSo, PM=6. So, the answer is 6. Therefore, PM=6.\\n\\nWait, that seems straightforward. Let me just verify the calculations step by step to make sure.\\n\\nFirst, coordinates of A, B, C:\\n\\nB (0,0), C (16,0), A (-7,7√3). Correct, since AB distance is sqrt((-7-0)^2 + (7√3 -0)^2)=sqrt(49 + 147)=sqrt(196)=14. Correct. AC distance is sqrt((16 +7)^2 + (0 -7√3)^2)=sqrt(23² + (7√3)^2)=sqrt(529 +147)=sqrt(676)=26. Correct.\\n\\nCoordinates of D: BD=28\\/5=5.6, so D is at (5.6,0)=(28\\/5,0). Correct.\\n\\nEquation of AD: slope calculated as -5√3\\/9, equation y = (-5√3\\/9)x +28√3\\/9. Correct.\\n\\nEquation of BP: perpendicular to AD, slope 3√3\\/5, passing through B (0,0), so y=(3√3\\/5)x. Correct.\\n\\nIntersection: solving (3√3\\/5)x = (-5√3\\/9)x +28√3\\/9. Multiplying through by 45, got 27√3 x = -25√3 x +140√3, leading to 52√3 x=140√3, so x=140\\/52=35\\/13. Then y=3√3\\/5*(35\\/13)=21√3\\/13. Correct coordinates of P.\\n\\nMidpoint M is (8,0). Distance PM: sqrt[(8 -35\\/13)^2 + (0 -21√3\\/13)^2]. Calculated 8 -35\\/13=69\\/13. Squared, (69\\/13)^2=4761\\/169. Then (21√3\\/13)^2=1323\\/169. Sum=6084\\/169=36. sqrt(36)=6. So PM=6. All steps check out. So, the answer is 6.\\n\\n**Final Answer**\\n\\\\boxed{6}\\n<\\/think>\\n\\nGiven triangle \\\\( \\\\triangle ABC \\\\) with side lengths \\\\( AB = 14 \\\\), \\\\( BC = 16 \\\\), and \\\\( AC = 26 \\\\). Point \\\\( P \\\\) is on the angle bisector \\\\( AD \\\\) of \\\\( \\\\angle A \\\\), and \\\\( BP \\\\perp AD \\\\). \\\\( M \\\\) is the midpoint of \\\\( BC \\\\). We need to find the value of \\\\( PM \\\\).\\n\\n1. **Using the Angle Bisector Theorem**:\\n   - The angle bisector \\\\( AD \\\\) divides \\\\( BC \\\\) in the ratio \\\\( AB : AC = 14 : 26 = 7 : 13 \\\\).\\n   - Therefore, \\\\( BD = \\\\frac{7}{20} \\\\times 16 = \\\\frac{28}{5} \\\\) and \\\\( DC = \\\\frac{13}{20} \\\\times 16 = \\\\frac{52}{5} \\\\).\\n\\n2. **Coordinates Setup**:\\n   - Place \\\\( B \\\\) at \\\\( (0, 0) \\\\), \\\\( C \\\\) at \\\\( (16, 0) \\\\), and find coordinates of \\\\( A \\\\).\\n   - Solving the system of equations for distances \\\\( AB = 14 \\\\) and \\\\( AC = 26 \\\\), we find \\\\( A \\\\) at \\\\( (-7, 7\\\\sqrt{3}) \\\\).\\n\\n3. **Coordinates of \\\\( D \\\\)**:\\n   - \\\\( D \\\\) is the point on \\\\( BC \\\\) such that \\\\( BD = \\\\frac{28}{5} \\\\), so \\\\( D \\\\) is at \\\\( \\\\left( \\\\frac{28}{5}, 0 \\\\right) \\\\).\\n\\n4. **Equations of Lines**:\\n   - Slope of \\\\( AD \\\\): \\\\( \\\\frac{0 - 7\\\\sqrt{3}}{\\\\frac{28}{5} + 7} = -\\\\frac{5\\\\sqrt{3}}{9} \\\\).\\n   - Equation of \\\\( AD \\\\): \\\\( y = -\\\\frac{5\\\\sqrt{3}}{9}x + \\\\frac{28\\\\sqrt{3}}{9} \\\\).\\n   - Slope of \\\\( BP \\\\) (perpendicular to \\\\( AD \\\\)): \\\\( \\\\frac{9}{5\\\\sqrt{3}} = \\\\frac{3\\\\sqrt{3}}{5} \\\\).\\n   - Equation of \\\\( BP \\\\): \\\\( y = \\\\frac{3\\\\sqrt{3}}{5}x \\\\).\\n\\n5. **Intersection of \\\\( BP \\\\) and \\\\( AD \\\\)**:\\n   - Solving \\\\( \\\\frac{3\\\\sqrt{3}}{5}x = -\\\\frac{5\\\\sqrt{3}}{9}x + \\\\frac{28\\\\sqrt{3}}{9} \\\\):\\n     - \\\\( x = \\\\frac{35}{13} \\\\), \\\\( y = \\\\frac{21\\\\sqrt{3}}{13} \\\\).\\n   - Coordinates of \\\\( P \\\\): \\\\( \\\\left( \\\\frac{35}{13}, \\\\frac{21\\\\sqrt{3}}{13} \\\\right) \\\\).\\n\\n6. **Midpoint \\\\( M \\\\)**:\\n   - \\\\( M \\\\) is the midpoint of \\\\( BC \\\\), so \\\\( M \\\\) is at \\\\( (8, 0) \\\\).\\n\\n7. **Distance \\\\( PM \\\\)**:\\n   - Using distance formula: \\\\( PM = \\\\sqrt{\\\\left( 8 - \\\\frac{35}{13} \\\\right)^2 + \\\\left( 0 - \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} \\\\).\\n   - Simplifying, \\\\( PM = \\\\sqrt{\\\\left( \\\\frac{69}{13} \\\\right)^2 + \\\\left( \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} = \\\\sqrt{\\\\frac{6084}{169}} = 6 \\\\).\\n\\nThus, the value of \\\\( PM \\\\) is \\\\(\\\\boxed{6}\\\\).\",\n", "            \"role\":\"assistant\"\n", "        }\n", "    ]\n", "}\n"]}], "source": ["import pandas as pd\n", "\n", "file_path = \"/root/autodl-tmp/minideepseek/v3/data/openr1/all/default-00002-of-00010.parquet\"\n", "\n", "try:\n", "    # 读取第一行数据\n", "    df = pd.read_parquet(file_path)\n", "    \n", "    if not df.empty:\n", "        first_row = df.iloc[0]  # 取第一行数据\n", "        print(\"第一行数据：\\n\", first_row.to_json(indent=4, force_ascii=False))\n", "    else:\n", "        print(\"❌ 读取失败，文件为空！\")\n", "\n", "except FileNotFoundError:\n", "    print(f\"❌ 文件未找到：{file_path}\")\n", "except Exception as e:\n", "    print(f\"❌ 发生错误：{e}\")"]}, {"cell_type": "markdown", "id": "c56a00f8-827e-464e-96e9-b4f2e5d971c8", "metadata": {}, "source": ["- **ape210K**"]}, {"cell_type": "code", "execution_count": 21, "id": "6846bc72-9a2d-4d41-a186-da62bc0be890", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>question</th>\n", "      <th>question_chinese</th>\n", "      <th>chain</th>\n", "      <th>result</th>\n", "      <th>result_float</th>\n", "      <th>equation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ape210k__01099539</td>\n", "      <td>The fifth grade students participated in the voluntary book donation activity. Class Five 1 donated 500 books, Class Five 2 donated 80% of Class Five 1, Class Five 3 donated 120% of Class Five 2, Class Five 1 and Class Five 3 Donate more books than who? (Please compare the two methods).</td>\n", "      <td>五年级同学参加义务捐书活动，五1班捐了500本，五2班捐的本数是五1班80%，五3班捐的本数是五2班120%，五1班和五3班比谁捐书多？(请用两种方法比较一下)．</td>\n", "      <td>&lt;result&gt;1&lt;/result&gt;</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>x=1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  id                                                                                                                                                                                                                                                                                         question                                                                   question_chinese               chain result  result_float equation\n", "0  ape210k__01099539  The fifth grade students participated in the voluntary book donation activity. Class Five 1 donated 500 books, Class Five 2 donated 80% of Class Five 1, Class Five 3 donated 120% of Class Five 2, Class Five 1 and Class Five 3 Donate more books than who? (Please compare the two methods).  五年级同学参加义务捐书活动，五1班捐了500本，五2班捐的本数是五1班80%，五3班捐的本数是五2班120%，五1班和五3班比谁捐书多？(请用两种方法比较一下)．  <result>1</result>      1           1.0      x=1"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 指定 Parquet 文件路径\n", "file_path = \"/root/autodl-tmp/minideepseek/v3/data/ape210k/data/train-00000-of-00001-b9f022a8492442e4.parquet\"\n", "\n", "# 读取 Parquet 文件\n", "df = pd.read_parquet(file_path, engine=\"pyarrow\")\n", "\n", "# 设置 Pandas 选项，确保完整显示内容\n", "pd.set_option(\"display.max_columns\", None)  # 显示所有列\n", "pd.set_option(\"display.max_colwidth\", None)  # 显示所有单元格内容\n", "pd.set_option(\"display.width\", 1000)  # 设置输出宽度，防止换行\n", "\n", "# 显示第一行\n", "df.head(1)"]}, {"cell_type": "markdown", "id": "405f2443-56fb-4065-98fd-068df03cf83d", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "f9fbc2d6-f43d-44f0-a0a5-826e37c5ec03", "metadata": {}, "source": ["## 4 多模态PDF规模化处理实战"]}, {"cell_type": "markdown", "id": "29a9bf58-77ef-47e4-a686-2dcc923b94a5", "metadata": {}, "source": ["### 4.1 大模型预训练数据处理的一般流程"]}, {"cell_type": "markdown", "id": "e5cf0110-36fb-46b3-a7f1-71a94df63534", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["大模型所使用的预训练数据预处理流程主要包括 **数据收集、格式转换、清洗、质量控制、分词、合并拼接** 等多个步骤。\n", "\n", "```\n", "【step 1】📂 raw data (PDF/HTML/TXT)\n", "            ↓  (转换)\n", "【step 2】📂 JSONL {\"text\": \"...\"}\n", "            ↓  (清洗)\n", "          📂 cleaned JSONL\n", "            ↓  (质量控制)\n", "【step 3】📂 filtered JSONL\n", "            ↓  (tokenizer)\n", "          📂 Tokenized JSONL {\"tokens\": [...]}\n", "            ↓  (拼接)\n", "          📂 Pretrain Data (bin/lmdb)\n", "```"]}, {"cell_type": "markdown", "id": "4ebd0cd9-50a1-4b3e-85fc-46f969b2fe75", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["原始数据大约是下面这些来源——\n", "\n", "- 📄 **公开文本数据**：CC (Common Crawl)、Wikipedia、BooksCorpus\n", "- 📜 **领域数据**：法律文档、医学论文、代码（如 GitHub）\n", "- 📊 **企业内部数据**：客服对话、文档、邮件\n", "- 📰 **网页爬取**：新闻网站、论坛（爬取后可能需要清理）\n", "- 📚 **PDF 文档**：论文、书籍（需 OCR + 结构解析）\n", "\n", "通常来说，如果我们转换格式并将文件进行存储——\n", "\n", "- **原始格式（PDF, HTML, TXT, CSV） → JSONL**\n", "  - PDF → JSONL: `pdfplumber` + `pymupdf` + `pytesseract`\n", "  - HTML → JSONL: `BeautifulSoup` + `html2text`\n", "  - CSV → JSONL: `pandas`\n", "  - 爬虫: `scrapy` / `requests`\n", "\n", "如果我们需要更深度的存储，则 ↓\n", "```\n", "📂 Raw data (PDF/HTML/TXT/CSV)\n", "    ↓  (转换)\n", "📂 JSONL {\"text\": \"...\"}\n", "    ↓  (压缩存储)\n", "📂 Parquet (列式存储)\n", "```"]}, {"cell_type": "markdown", "id": "89cb7a87-72f5-40ba-8eb3-7e58f1f7a3aa", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "71caaf5c-cb5c-4b0b-9988-7924b913c465", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["- **数据清洗 (`<PERSON><PERSON>uicer` 预处理)**\n", "    - 去除低质量数据（乱码、HTML 代码、重复数据）\n", "    - 正则化文本（统一编码、去除异常符号）\n", "    - 过滤敏感信息（PII/用户隐私、违禁词过滤）\n", "\n", "- 工具：\n", "    - **`DataJuicer`** 🧃：大规模数据清洗工具\n", "    - `langdetect` / `fasttext`：过滤非目标语言\n", "    - `ftfy`：修正乱码\n", "    - `textacy` / `regex`：去除特殊符号\n", "    - `deduplication`：去重（MinHash/SimHash）"]}, {"cell_type": "markdown", "id": "03999cc9-340d-43c4-90a8-a7e1bfd5a9e0", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["- **质量控制 (`filtered data`)**\n", "数据清洗完成后，还需要 **质量控制** 以确保高质量数据：\n", "    - 关键词过滤（避免不相关或不健康内容）\n", "    - 长度筛选（过短的文本无价值）\n", "    - 可读性检测（自动计算 Flesch-Kincaid 指数）\n", "    - 标注与审查（人工 or 规则标注）"]}, {"cell_type": "markdown", "id": "ba4098cc-c79c-4b37-b64c-316e8960ed8b", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "cd97bc4b-5c4b-49fe-a12b-488adb018f49", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["- **Tokenizer 预处理**\n", "    - JSONL → Tokenized Data\n", "    - 拼接/填充/截断：形成长序列，减少 `padding`、添加 `bos` / `eos` token**\n", "    - 生成 `train.bin` / `train.json` / `h5py` / `lmdb` 格式、或者生成"]}, {"cell_type": "markdown", "id": "ea95db41-3185-46eb-b162-e41438e110db", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["- **Dataloder或者PackedDataset预训练数据加载**"]}, {"cell_type": "markdown", "id": "8954487d-481c-4ebe-b06a-50293b8d0b09", "metadata": {}, "source": ["### 4.2 多模态Rawdata到JSONL标准化文件"]}, {"cell_type": "markdown", "id": "026c8e6d-6541-4edc-875d-d9b774ede124", "metadata": {}, "source": ["数据分析1000+行业报告.zip ↓ <br>\n", "链接: https://pan.baidu.com/s/1EPWpAbCVjaoUenEikPTbng?pwd=5x4c <br>\n", "提取码: 5x4c <br>"]}, {"cell_type": "markdown", "id": "56252d4c-da43-4613-974a-f7b5170b3140", "metadata": {}, "source": ["\n", "| 组件 | 作用 | 安装命令 |\n", "|------|------|---------|\n", "| pdfplumber | 解析 PDF 文本 & 表格 | `pip install pdfplumber` |\n", "| PyMuPDF (fitz) | 解析 PDF & 提取图片 | `pip install pymupdf` |\n", "| Tesseract OCR | 识别图片文字 | `sudo apt install tesseract-ocr` |\n", "| pytesseract | Tesseract 的 Python 绑定 | `pip install pytesseract` |\n", "| 中文 OCR | 处理中文图片文字 | `sudo apt install tesseract-ocr-chi-sim` |"]}, {"cell_type": "markdown", "id": "27763d4c-5983-49c2-81be-826bd526e270", "metadata": {}, "source": ["```shell\n", "\n", "# 必须转向有python10的环境\n", "# 如果你不希望重建环境、那可以使用mnds_data_clean_step2_\n", "# 先退出原本的mnds_training环境\n", "# 回到~/autodl-tmp/minideepseek/v3\n", "deactivate\n", "cd ~/autodl-tmp/minideepseek/v3\n", "source mnds_data_clean_step2_/bin/activate\n", "\n", "# 更新pip\n", "pip install --upgrade pip\n", "pip install --upgrade pip setuptools wheel\n", "sudo apt-get install -y build-essential cmake\n", "\n", "# 安装你处理pdf所需的所有库\n", "\n", "pip install pdfplumber\n", "pip install pymupdf\n", "sudo apt install -y tesseract-ocr\n", "pip install pytesseract\n", "sudo apt install -y tesseract-ocr-chi-sim\n", "\n", "# 验证工具是否安装成功\n", "python\n", "\n", "import pdfplumber\n", "import fitz\n", "import pytesseract\n", "\n", "print(\"pdfplumber version:\", pdfplumber.__version__)\n", "print(\"PyMuPDF version:\", fitz.__doc__)\n", "print(\"Tesseract OCR version:\", pytesseract.get_tesseract_version())\n", "\n", "exit()\n", "\n", "# 如果安装依赖出现问题、你可以手动安装下面的依赖\n", "#pip install pillow\n", "#pip install pdfminer.six\n", "\n", "# 建立存储数据新目录\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/finPDF\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/finPDF/output_jsonl\n", "\n", "# 将1000+行业报告直接上传至data目录"]}, {"cell_type": "markdown", "id": "4bf0c2af-c179-4030-b661-c885173982bc", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/132.png)"]}, {"cell_type": "markdown", "id": "ee3cfd19-f671-47aa-acde-c033d84359e8", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["```shell\n", "# 将你的pdf压缩文件上传至/root/autodl-tmp/minideepseek/v3/data\n", "# 先进行解压，我所提供的pdf文件为mac压缩的zip文件，编码模式为mac的GBK\n", "unzip /root/autodl-tmp/minideepseek/v3/data/1000+行业报告.zip -d /root/autodl-tmp/minideepseek/v3/data/reports\n", "\n", "# 删除由于mac解压带来的无用文件\n", "# __MACOSX文件夹下的文件必须删除、否则会报错\n", "find /root/autodl-tmp/minideepseek/v3/data/reports -name \"__MACOSX\" -exec rm -rf {} +\n", "find /root/autodl-tmp/minideepseek/v3/data/reports -name \"._*\" -exec rm -rf {} +\n", "```"]}, {"cell_type": "markdown", "id": "da659101-9a8b-41f4-b617-0b6b70788724", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/133.png)"]}, {"cell_type": "markdown", "id": "2dba4076-ff5e-49ce-8f0e-d2e0aa4a0863", "metadata": {}, "source": ["```shell\n", "# 执行下面的代码，注意其中的done也是需要执行的\n", "find /root/autodl-tmp/minideepseek/v3/data/reports/行业报告 -type f -name \"*.zip\" | while read zipfile; do\n", "    unzip -o \"$zipfile\" -d /root/autodl-tmp/minideepseek/v3/data/finPDF\n", "done\n", "```"]}, {"cell_type": "markdown", "id": "9f97c319-da8b-42b1-90c1-9304f9fe71bd", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/134.png)"]}, {"cell_type": "markdown", "id": "29ea6c36-a384-450e-a0a1-f30a69056166", "metadata": {}, "source": ["最终得到的文件是 ↓"]}, {"cell_type": "markdown", "id": "e50ef9e6-bbec-4afe-8eed-231d3c32e0a9", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/135.png)"]}, {"cell_type": "markdown", "id": "515c54a1-e5a6-4225-9ea8-ec0072dd47fa", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/136.png)"]}, {"cell_type": "markdown", "id": "3676dab3-e9d8-463f-b88e-e8355234b28d", "metadata": {}, "source": ["**接下来可以开始运行pdfprocess.py了** ↓"]}, {"cell_type": "markdown", "id": "25eee41e-93f4-48d3-b6b4-acd1ed3a4f04", "metadata": {}, "source": ["```shell\n", "cd /root/autodl-tmp/minideepseek/v3/data\n", "\n", "# 上传pdfprocess.py文件到data文件夹下\n", "# 开始执行，我设置的目录是/root/autodl-tmp/minideepseek/v3/data/finPDF/\n", "# 你可以尝试设置到其中一个子目录来检测代码\n", "# 【TIME WARNING：32线程并行、2小时】\n", "touch pdfprocess.py\n", "python3 pdfprocess.py\n", "```"]}, {"cell_type": "markdown", "id": "f315c580-a8f2-4f5d-860e-74f19d085095", "metadata": {}, "source": ["开始运行后你会看到 ↓"]}, {"cell_type": "markdown", "id": "1fda0592-160d-43c6-971d-5145a4a675c3", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/144.png)"]}, {"cell_type": "markdown", "id": "5bd31617-2ec3-4a64-92eb-8f9ba04d04d1", "metadata": {}, "source": ["多线程处理脚本解读——"]}, {"cell_type": "markdown", "id": "861909c6-787b-4c84-9e02-0293863ab856", "metadata": {}, "source": ["```python\n", "import os\n", "import json\n", "import pdfplumber\n", "import fitz  # PyMuPDF\n", "import pytesseract\n", "from multiprocessing import Pool\n", "from PIL import Image\n", "from io import BytesIO\n", "from tqdm import tqdm  # 进度条库\n", "import pdfminer.pdfparser\n", "\n", "# 统一的 JSONL 输出文件夹\n", "JSONL_OUTPUT_DIR = \"/root/autodl-tmp/minideepseek/v3/data/finPDF/output_jsonl\"\n", "os.makedirs(JSONL_OUTPUT_DIR, exist_ok=True)  # 确保目录存在\n", "\n", "def extract_text_from_pdf(pdf_path):\n", "    \"\"\"提取 PDF 里的文本、表格、OCR 解析的图像文本\"\"\"\n", "    extracted_data = {\"file\": pdf_path, \"text\": \"\"}\n", "\n", "    try:\n", "        # 用 pdfplumber 解析文本和表格\n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            # 提取文本\n", "            text_content = \"\\n\".join(page.extract_text() for page in pdf.pages if page.extract_text())\n", "\n", "            # 提取表格\n", "            table_text = []\n", "            for page in pdf.pages:\n", "                tables = page.extract_tables()\n", "                for table in tables:\n", "                    if table:\n", "                        # 处理表格中的 None 值\n", "                        table_text.append(\"\\n\".join(\n", "                            [\"\\t\".join(str(cell) if cell is not None else \"\" for cell in row) for row in table]\n", "                        ))\n", "\n", "            # 合并文本\n", "            extracted_data[\"text\"] = text_content + \"\\n\\n\" + \"\\n\".join(table_text)\n", "\n", "        # 用 PyMuPDF (fitz) 解析图片\n", "        doc = fitz.open(pdf_path)\n", "        image_texts = []\n", "        for page_index in range(len(doc)):\n", "            images = doc[page_index].get_images(full=True)  # 获取当前页的所有图片\n", "            for img_index, img in enumerate(images):\n", "                xref = img[0]\n", "                base_image = doc.extract_image(xref)\n", "                image_bytes = base_image[\"image\"]\n", "                image = Image.open(BytesIO(image_bytes))\n", "\n", "                # 用 OCR 识别图像中的文本\n", "                ocr_text = pytesseract.image_to_string(image)\n", "                if ocr_text.strip():\n", "                    image_texts.append(f\"Page {page_index + 1} Image {img_index + 1}: {ocr_text}\")\n", "\n", "        # 合并 OCR 文本\n", "        extracted_data[\"text\"] += \"\\n\\n\" + \"\\n\".join(image_texts)\n", "\n", "    except (pdfminer.pdfparser.PDFSyntaxError, ValueError, TypeError, Exception) as e:\n", "        extracted_data[\"text\"] = f\"ERROR: Unable to read PDF ({str(e)})\"\n", "        print(f\"⚠️ 无法解析 PDF: {pdf_path}，跳过。错误信息: {e}\")\n", "\n", "    return extracted_data\n", "\n", "def process_folder(pdf_folder):\n", "    \"\"\"处理单个文件夹中的所有 PDF 并保存为 JSONL\"\"\"\n", "    pdf_files = [os.path.join(pdf_folder, f) for f in os.listdir(pdf_folder) if f.endswith(\".pdf\")]\n", "    if not pdf_files:\n", "        return  # 如果当前文件夹没有 PDF，跳过处理\n", "\n", "    folder_name = os.path.basename(pdf_folder)  # 获取文件夹名称\n", "    output_file = os.path.join(JSONL_OUTPUT_DIR, f\"{folder_name}.jsonl\")  # JSONL 统一存放到 JSONL_OUTPUT_DIR\n", "\n", "    print(f\"📂 正在处理文件夹: {pdf_folder}，发现 {len(pdf_files)} 个 PDF 文件...\")\n", "\n", "    with Pool(processes=32) as pool:  # 32 个进程并行处理 PDF\n", "        results = list(tqdm(pool.imap(extract_text_from_pdf, pdf_files), total=len(pdf_files), desc=f\"🔄 处理中: {folder_name}\"))\n", "\n", "    # 统计失败的 PDF 文件\n", "    failed_pdfs = [r[\"file\"] for r in results if \"ERROR\" in r[\"text\"]]\n", "\n", "    # 保存 JSONL 格式文件\n", "    with open(output_file, \"w\", encoding=\"utf-8\") as f:\n", "        for item in results:\n", "            f.write(json.dumps(item, ensure_ascii=False) + \"\\n\")  # 确保支持中文\n", "\n", "    print(f\"✅ 文件夹 {folder_name} 处理完成，结果已保存至 {output_file}\")\n", "\n", "    # 记录失败的 PDF\n", "    if failed_pdfs:\n", "        error_log = os.path.join(JSONL_OUTPUT_DIR, f\"{folder_name}_failed_pdfs.txt\")\n", "        with open(error_log, \"w\", encoding=\"utf-8\") as f:\n", "            for pdf in failed_pdfs:\n", "                f.write(pdf + \"\\n\")\n", "        print(f\"⚠️ 部分 PDF 解析失败，失败文件列表已保存到: {error_log}\")\n", "\n", "def main():\n", "    root_folder = \"/root/autodl-tmp/minideepseek/v3/data/finPDF/\"\n", "    \n", "    # 遍历根目录下的所有子文件夹\n", "    for subdir, _, _ in os.walk(root_folder):\n", "        if subdir == root_folder:\n", "            continue  # 跳过根目录自身，只处理子目录\n", "        process_folder(subdir)  # 处理每个子文件夹\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "```"]}, {"cell_type": "markdown", "id": "98b34e84-8c57-45c5-a456-b2e6af05464a", "metadata": {}, "source": ["最终运行的结果如下 ↓"]}, {"cell_type": "markdown", "id": "a11ba8c4-1842-4591-83d9-0e38cca8d41f", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/146.png)"]}, {"cell_type": "markdown", "id": "5895f12f-277b-4104-976a-8659c12d4499", "metadata": {}, "source": ["点开其中一份jsonl文件如下 ↓"]}, {"cell_type": "markdown", "id": "5f69cbc8-5e74-455b-81f5-49ca1ba44392", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/143.png)"]}, {"cell_type": "markdown", "id": "fc83d7dc-982c-4ea5-9a15-f0b2c1df93eb", "metadata": {}, "source": ["## 5 数据处理第一步：巨量数据并行jsonl化实战"]}, {"cell_type": "markdown", "id": "d016b91a-27eb-4a94-8820-fab8b8db8f3e", "metadata": {}, "source": ["大模型所使用的预训练数据预处理流程主要包括 **数据收集、格式转换、清洗、质量控制、分词、合并拼接** 等多个步骤。\n", "\n", "```\n", "【step 1】📂 raw data (PDF/HTML/TXT)\n", "            ↓  (转换)\n", "【step 2】📂 JSONL {\"text\": \"...\"}\n", "            ↓  (清洗)\n", "          📂 cleaned JSONL\n", "            ↓  (质量控制)\n", "【step 3】📂 filtered JSONL\n", "            ↓  (tokenizer)\n", "          📂 Tokenized JSONL {\"tokens\": [...]}\n", "            ↓  (拼接)\n", "          📂 Pretrain Data (bin/lmdb)\n", "```"]}, {"cell_type": "markdown", "id": "652e5815-0b62-4593-a22a-057e5318bd18", "metadata": {}, "source": ["- **数据预处理阶段所需全部文件**\n", "（不包括pretrain脚本与SFT脚本，仅限数据预处理）"]}, {"cell_type": "markdown", "id": "bce56048-5a9d-447a-bb61-2c8aaa7a937a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/119.png)"]}, {"attachments": {}, "cell_type": "markdown", "id": "b3d6f265-5429-42c6-a250-cb5217c448fd", "metadata": {}, "source": ["文件结构如下 ↓\n", "```python\n", "minideepseek/\n", "├── requirements_minids.txt      # 预训练所需的环境\n", "├── test_v3model.py              # 测试deepseev3_mtp_model.py能否跑通\n", "│\n", "├── model/                       # model文件夹、存放模型文件\n", "│   ├── config.py\n", "│   ├── convert.py\n", "│   ├── deepseev3_mtp_model.py   # 要训练的模型脚本，加上了mtp\n", "│   ├── fp8_cast_bf16.py\n", "│   ├── kernel.py\n", "│\n", "├── data/                        # data文件夹\n", "│   ├── analyze_jsonl.py         # 查询各jsonl文件统计指标的脚本\n", "│   ├── data-juicer-main.zip     # data-juicer库安装所需文件\n", "│   ├── delete_files.sh          # 删除数据、降低数据大小的脚本\n", "│   ├── hfd_revised.sh           # 分布式并行数据下载脚本（防429报错版）\n", "│   ├── slimpajama_preclean.py   # slimpajama清理脚本\n", "│   ├── pdfprocess.py            # pdf批量处理流程\n", "│   ├── print_data.ipy           # 查看数据状态的ipy\n", "│   ├── step1_pretrain_basic_dataprocess.py   # 数据处理第一步：JSONL化脚本\n", "│   ├── step2/                                # 数据处理第二步：数据清洗脚本\n", "│   │   ├── minidsv3_starcoder.yaml           # 各个数据自身的yaml文件\n", "│   │   ├── minidsv3_text_ape.yaml\n", "│   │   ├── minidsv3_text_openr1.yaml\n", "│   │   ├── minidsv3_text_skypile.yaml\n", "│   │   ├── minidsv3_text_slimpajama.yaml\n", "│   │   ├── requirements_step2.txt           # 第二步运行所需的环境\n", "│   │   ├── run_step2_ape.sh                 # 各个数据上运行DJ的sh脚本\n", "│   │   ├── run_step2_openr1.sh\n", "│   │   ├── run_step2_skypile.sh\n", "│   │   ├── run_step2_slimpajama.sh\n", "│   │   ├── run_step2_starcoder.sh\n", "│   ├── step3_prepare_data_for_pretrain.py   # 数据处理第三步：Tokenizer脚本\n", "│\n", "├── tokenizer/                  # tokenizer文件夹\n", "│   ├── test_tokenizer.py       # 测试tokenizer是否正常运行的代码\n", "│   ├── tokenizer.json          # deepseek开源的tokenizer架构本身\n", "│   ├── tokenizer_config.json   # deepseek开源的tokenizer配置文件\n", "│\n", "```"]}, {"cell_type": "markdown", "id": "f8b4f283-fc3e-49b1-a5ec-12ce06f790c9", "metadata": {}, "source": ["### 5.1 数据预处理第一阶段执行脚本"]}, {"cell_type": "markdown", "id": "a507e912-fa45-4e09-8f3a-780621eb71a9", "metadata": {}, "source": ["```shell\n", "# 依然使用训练环境\n", "# 回到建立训练环境的目录、激活训练环境\n", "cd ~/autodl-tmp/minideepseek/v3\n", "source mnds_training/bin/activate\n", "\n", "# 环境补完\n", "pip install zstandard\n", "pip <PERSON> pyarrow\n", "\n", "# 建立存储数据的新目录\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/basic_clean\n", "\n", "cd ~/autodl-tmp/minideepseek/v3/data\n", "\n", "# 测试模式，max_line = 5\n", "# 【TIME WARNING：15分钟】\n", "python step1_pretrain_basic_dataprocess.py     \n", "\n", "# 完整模式\n", "#【TIME WARNING: 6小时】\n", "touch step1_pretrain_basic_dataprocess.py\n", "python step1_pretrain_basic_dataprocess.py main\n", "\n", "# 针对过于零碎的slim_pajama做预清洗\n", "# 【TIME WARNING】\n", "# 8进程并行、64G内存支持下约25分钟\n", "touch slimpajama_preclean.py\n", "python slimpajama_preclean.py\n", "\n", "# 建立data层的缓存目录temp\n", "mkdir /root/autodl-tmp/minideepseek/v3/data/temp\n", "\n", "# 把原本的slimpajama文件\n", "mv /root/autodl-tmp/minideepseek/v3/data/basic_clean/processed_slimpajama.jsonl /root/autodl-tmp/minideepseek/v3/data/temp\n", "```"]}, {"cell_type": "markdown", "id": "4a120da4-fb49-4f52-a225-92b88ba4cb3d", "metadata": {}, "source": ["在这一阶段，我们的目标是将所有的数据都转化为标准JSONL格式 ↓ 即每个样本中只有一个键值对、键的内容为\"text\"、值的内容为当前样本所包含的所有文字——\n", "```python\n", "{\"text\":\"xxxx\"}\n", "```"]}, {"cell_type": "markdown", "id": "eac6b12e-ca50-4fec-a967-efdde417d679", "metadata": {}, "source": ["有的数据可能天然就遵循这样的格式，例如skypile ↓"]}, {"cell_type": "markdown", "id": "d13f5d3e-c44f-4e4d-b716-e0e28149f80a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/137.png)"]}, {"cell_type": "markdown", "id": "530bb644-6d23-4de6-b92b-c6e885c67d21", "metadata": {}, "source": ["**但是大部分数据都不可能遵循这样的格式、相反，大部分数据都有自己特殊的结构** ↓"]}, {"cell_type": "markdown", "id": "5636cac7-49f6-4d5d-949c-9593c53bd439", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/138.png)\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/139.png)"]}, {"cell_type": "markdown", "id": "3b015488-dda0-48b8-a90d-c1565e83be1a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/140.png)\n"]}, {"cell_type": "markdown", "id": "6bf9ca47-96b6-4cf1-a3af-9ff432373419", "metadata": {}, "source": ["对于OpenR1-Math，在huggingface页面上也可看到相应的结构 → https://huggingface.co/datasets/open-r1/OpenR1-Math-220k"]}, {"cell_type": "markdown", "id": "11412b4a-ce6a-415f-859d-01d48d18d4cf", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/141.png)\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/142.png)"]}, {"cell_type": "markdown", "id": "64efbc9c-db0e-45cc-87ed-9375293e67ff", "metadata": {}, "source": ["因此我采取的方案是 ↓"]}, {"cell_type": "markdown", "id": "2da7d038-9086-4e71-b46d-c563c0b79ada", "metadata": {}, "source": ["| **数据集**       | **输入格式**            | **处理步骤** | **基础去重** | **基础质量检查** | **输出格式** |\n", "|----------------|---------------------|-----------|-------|--------|--------|\n", "| **Skypile**    | `.jsonl`            | 解析 JSON，提取 `\"text\"` 字段 | ✅ | ✅ | `.jsonl` |\n", "| **Slimpajama** | `.jsonl.zst`        | 解压 Zstd，解析 JSON，提取 `\"text\"` | ✅ | ✅ | `.jsonl` |\n", "| **OpenR1**     | `.parquet`          | 提取 `problem_type`、`problem`、`solution`、`answer`、`generations` | ✅ | ✅ | `.jsonl` |\n", "| **APE210K**    | `.parquet`          | 提取 `question_chinese`、`chain`、`equation`、`result` | ✅ | ✅ | `.jsonl` |\n", "| **Starcoder**  | `.parquet`          | 提取 `content`，筛选 `max_stars_count` | ✅ | ✅ | `.jsonl` |"]}, {"cell_type": "markdown", "id": "528b41c2-8fe6-4980-a11f-500b0aba4aac", "metadata": {}, "source": ["### 5.2 数据预处理第一阶段脚本解读"]}, {"cell_type": "markdown", "id": "dfe3a952-f20f-4753-b6c5-8a30cf703dd7", "metadata": {}, "source": ["- **`FormatHandler`（基础格式处理类）**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `__init__()` | 初始化 `FormatHandler`，定义输入路径、输出路径、数据集名称，并创建一个去重的 `set`。 |\n", "| `get_file_list()` | 递归获取 `input_path` 目录下的 `.jsonl` 和 `.parquet` 文件列表。 |\n", "| `process_one_line(line, fout)` | 处理单行数据（抽象方法，需要子类实现）。 |\n", "| `process_one_file(file_path, max_lines=None)` | 处理单个文件，逐行读取并调用 `process_one_line()`，去重并统计处理的行数。 |\n", "| `process_all(max_lines=None)` | 处理 `input_path` 目录下的所有文件，调用 `process_one_file()` 并统计总处理时间。 |\n", "| `quality_assurance(line)` | 进行文本质量检查，过滤短文本和换行符过多的文本。 |\n", "| `quality_assurance_math(line)` | 数学数据的质量检查，确保文本长度大于 10。 |\n", "| `zh_process(line)` | 处理中文文本，进行 Unicode 规范化、移除控制字符、格式化换行符等操作。 |\n", "\n", "---\n", "\n", "- **`SkypileFormatHandler`（Skypile 数据集处理类，继承 `FormatHandler`）**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `__init__()` | 初始化 `SkypileFormatHandler`，继承 `FormatHandler` 并初始化去重 `set`。 |\n", "| `get_file_list()` | 递归获取 `input_path` 目录下的 `.jsonl` 文件列表。 |\n", "| `process_one_line(line, fout)` | 解析 `.jsonl` 数据，提取 `\"text\"` 字段，去重后写入 `fout`。 |\n", "| `process_one_file(file_path, max_lines=None)` | 逐行读取 `.jsonl` 文件，调用 `process_one_line()` 处理文本，统计有效行和无效行。 |\n", "| `process_all(max_lines=None)` | 处理 `input_path` 目录下所有 `.jsonl` 文件，并统计总处理时间。 |\n", "\n", "---\n", "\n", "- **`SlimpajamaFormatHandler`（Slimpajama 数据集处理类）**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `__init__()` | 初始化 `SlimpajamaFormatHandler`，继承 `FormatHandler`。 |\n", "| `get_file_list()` | 递归获取 `input_path` 目录下的 `.jsonl.zst` 文件列表。 |\n", "| `process_one_file(file_path, max_lines=None)` | 读取 `.jsonl.zst`（Zstandard 压缩）文件，解压后逐行处理。 |\n", "| `process_one_line(line, fout)` | 解析 `.jsonl` 数据，提取 `\"text\"` 字段，质量检查后写入 `fout`。 |\n", "\n", "---\n", "\n", "- **`StarcoderFormatHandler`（Starcoder 代码数据集处理类）**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `__init__()` | 初始化 `StarcoderFormatHandler`，继承 `FormatHandler`，支持语言过滤。 |\n", "| `get_file_list()` | 获取 `.parquet` 文件列表，支持按编程语言分类筛选。 |\n", "| `process_one_file(file_path, max_lines=None)` | 读取 `.parquet` 文件，解析 `content` 字段，进行代码质量检查。 |\n", "\n", "---\n", "\n", "- **`OpenR1FormatHandler`（OpenR1 数据集处理类）**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `__init__()` | 初始化 `OpenR1FormatHandler`，继承 `FormatHandler`。 |\n", "| `get_file_list()` | 递归获取 `input_path` 目录下的 `.parquet` 文件列表。 |\n", "| `process_one_file(file_path, max_lines=None)` | 读取 `.parquet` 文件，提取 `problem`、`solution`、`answer` 等字段，进行质量检查和数据清洗。 |\n", "\n", "---\n", "\n", "- **`APE210KFormatHandler`（APE210K 数学数据集处理类）**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `__init__()` | 初始化 `APE210KFormatHandler`，继承 `FormatHandler`。 |\n", "| `process_one_file(file_path, max_lines=None)` | 读取 `.parquet` 文件，提取 `question_chinese`、`equation`、`result` 等字段，进行格式化处理。 |\n", "| `process_one_line(row, fout)` | 解析单行数据，将数学题目、解题步骤、最终答案合并为格式化文本并写入 `fout`。 |\n", "\n", "---\n", "\n", "- **`test_run()`（测试模式）**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `test_run()` | 在测试模式下运行多个数据集的 `process_all()`，并对每个数据集生成 `.jsonl` 文件。 |\n", "\n", "---\n", "\n", "- **`main_run()`（正式模式）**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `main_run()` | 在正式模式下运行 `process_all()` 处理 OpenR1 数据集，并生成 `.jsonl`。 |\n", "\n", "---\n", "\n", "- **`if __name__ == \"__main__\":`**\n", "| **方法名** | **功能解析** |\n", "|-----------|-------------|\n", "| `if __name__ == \"__main__\":` | 解析命令行参数，决定运行 `test_run()` 还是 `main_run()`，默认执行测试模式。 |\n", "\n", "---\n", "\n", "- `FormatHandler` 是所有格式处理器的 **基类**，提供通用功能（文件获取、去重、质量检查）。\n", "- 各子类（如 `SkypileFormatHandler`、`SlimpajamaFormatHandler`）继承 `FormatHandler`，并根据特定数据集的格式实现 `process_one_line()`。\n", "- **正式模式 `main_run()` 主要用于生产环境**，只处理 OpenR1 数据集。\n", "- **测试模式 `test_run()` 适用于调试**，可快速验证多个数据集的处理逻辑。"]}, {"cell_type": "markdown", "id": "ceec709b-c6e4-4dc0-ba3f-a40423aae3df", "metadata": {}, "source": ["## 6 数据处理第二步：data-jucier数据并行化清洗实战"]}, {"cell_type": "markdown", "id": "f8917559-8bfb-4f8e-a67c-ec4785eea0e5", "metadata": {}, "source": ["Data-Juicer 是一个 **数据处理与清洗** 的框架，专门用于 **大规模数据预处理**，特别适用于 **大语言模型（LLM）预训练** 数据管道。它提供了一整套 **高效、可配置** 的 **Mapper（映射）、Filter（过滤）、Deduplicator（去重）** 操作，使得数据清理变得 **模块化、自动化、高效**。"]}, {"cell_type": "markdown", "id": "f1c0c79f-d679-4d72-a43a-1916007f0d4c", "metadata": {}, "source": ["### 6.1 Datajucier的执行脚本"]}, {"cell_type": "markdown", "id": "801adaac-df00-49d3-acf9-497da6a22f45", "metadata": {}, "source": ["首次进入第二阶段 ↓ 此时你应该已经部署好了Datajucier所需要的一切环境，因此可以开始直接执行step2的代码了 ↓"]}, {"cell_type": "markdown", "id": "52fc4feb-4956-4a0f-9b8e-7fe66a9e3e6b", "metadata": {}, "source": ["```shell\n", "# 先退出原本的mnds_training环境\n", "# 回到~/autodl-tmp/minideepseek/v3\n", "deactivate\n", "cd ~/autodl-tmp/minideepseek/v3\n", "source mnds_data_clean_step2_/bin/activate\n", "\n", "# 建立存储数据新目录\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/deep_clean\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/deep_clean/djed_ape210k\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/deep_clean/djed_openr1\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/deep_clean/djed_skypile\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/deep_clean/djed_slimpajama\n", "mkdir -p ~/autodl-tmp/minideepseek/v3/data/deep_clean/djed_starcoder\n", "\n", "# 建立一个单独的目录来存放原来的processed_slimpajama.jsonl\n", "mkdir /root/autodl-tmp/minideepseek/v3/data/temp\n", "mv /root/autodl-tmp/minideepseek/v3/data/basic_clean/processed_slimpajama.jsonl /root/autodl-tmp/minideepseek/v3/data/temp\n", "\n", "# 进入data-jucier的脚本所在的目录\n", "cd /root/autodl-tmp/minideepseek/v3/data/step2\n", "\n", "# 运行下列代码、将jsonl分割成100~500MB的小文件\n", "# 更小的文件类似于PackedDataset的方法\n", "# 有助于提升data_jucier的并行化速度\n", "python split_jsonl.py\n", "\n", "###################################################\n", "####   4 x 64 vCPU Intel(R) Xeon(R) Gold 6430  ####\n", "####   RAM 4 x 120G，4 x RTX4090 24G           ####\n", "###################################################\n", "\n", "# 【TIME WARNING：120线程并行，约4小时】\n", "# python analyze_jsonl.py /root/autodl-tmp/minideepseek/v3/data/basic_clean\n", "\n", "# 检验data-jucier能否顺利运行\n", "# 【TIME WARNING：8进程并行，约5分钟】\n", "cd ~/autodl-tmp/minideepseek/v3/data/step2\n", "chmod +x run_step2_ape.sh\n", "bash run_step2_ape.sh\n", "\n", "# 【TIME WARNING：8进程并行，约15分钟】\n", "chmod +x run_step2_openr1.sh\n", "bash run_step2_openr1.sh\n", "\n", "# 【TIME WARNING：32进程并行、约3小时】\n", "# 中间还需额外下载一些筛选用模型、务必设置hf镜像站的环境变量\n", "chmod +x run_step2_skypile.sh\n", "bash run_step2_skypile.sh\n", "\n", "# 【TIME WARNING：32进程并行、约3小时】\n", "# 中间还需额外下载一些筛选用模型、务必设置hf镜像站的环境变量\n", "chmod +x run_step2_slimpajama.sh\n", "bash run_step2_slimpajama.sh\n", "\n", "# 【TIME WARNING：32进程并行、约3小时】\n", "chmod +x run_step2_starcoder.sh\n", "bash run_step2_starcoder.sh\n", "\n", "# 执行完成后、可以通过ctrl+C返回CLI命令行\n", "```"]}, {"cell_type": "markdown", "id": "337cdf64-b901-4d22-b487-ae5379d46188", "metadata": {}, "source": ["在执行时会看到下面的打印结果 ↓"]}, {"cell_type": "markdown", "id": "63fc46fe-baed-422f-9f67-80e26fe5e927", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/145.png)"]}, {"cell_type": "markdown", "id": "53e50a48-11bf-4c06-ab37-a2b5a79c4a23", "metadata": {}, "source": ["中断执行时，需要走下面的流程才能彻底释放显存 ↓ 否则显存会持续被占用。\n", "\n", "```shell\n", "source ~/.bashrc\n", "\n", "pkill -9 python\n", "\n", "rm -rf /root/autodl-tmp/minideepseek/v3/data/data_jucier_cache\n", "rm -rf /root/autodl-tmp/minideepseek/v3/data/data_jucier_cache/temp\n", "```"]}, {"cell_type": "markdown", "id": "b693e028-a272-40ba-924f-102c90ab9f87", "metadata": {}, "source": ["执行完毕后、你看到的输出结果为 ↓"]}, {"cell_type": "markdown", "id": "673fa91a-e137-4463-a87c-7c436c633a48", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/148.png)"]}, {"cell_type": "markdown", "id": "bb8881fa-26b9-4e2d-bd99-40c730bdb37e", "metadata": {}, "source": ["点进文件夹后、会看到大约按照300M和100M分配的数据 ↓ **在每个文件夹中，你需要删除排在第一位的stats.jsonl文件夹，为后续的处理做准备**。"]}, {"cell_type": "markdown", "id": "b3a7d2cc-9608-4bd8-94b7-87d46cbf02a4", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/150.png)"]}, {"cell_type": "markdown", "id": "bd93a06a-024e-40f7-8f13-ae9bfb8f8494", "metadata": {}, "source": ["### 6.2 data-jucier可完成的预处理"]}, {"cell_type": "markdown", "id": "12e1084c-aab7-42de-b6ed-73176d99b9db", "metadata": {}, "source": ["| 算子类型       | 算子名称        | 功能描述 |\n", "|--------------|--------------|---------|\n", "| **Mapper**   | `Mapper`     | 主要用于数据编辑和转换，对输入样本进行修改或增强 |\n", "| **Filter**   | `Filter`     | 主要用于数据过滤，根据统计信息或规则删除不符合条件的数据 |\n", "| **Deduplicator** | `Deduplicator` | 主要用于数据去重，利用哈希等方法消除重复样本 |\n", "| **Selector** | `Selector`   | 主要用于数据选择，从数据集中筛选符合特定条件的子集 |\n", "| **Grouper**  | `Grouper`    | 主要用于数据分组，将相关样本组合在一起 |\n", "| **Aggregator** | `Aggregator` | 主要用于数据聚合，对分组后的数据进行合并或统计 |"]}, {"cell_type": "markdown", "id": "922736ae-57f9-495c-a54d-708cb4ad609c", "metadata": {}, "source": ["更多预处理流程见附录。"]}, {"cell_type": "markdown", "id": "45c294c7-cf28-4d31-9a75-e86fe23b83f9", "metadata": {}, "source": ["**YAML 配置文件** 主要用于 Data-Juicer 框架，它 **定义了数据处理的全流程**，包括：\n", "1. **数据源路径**（`dataset_path`）\n", "2. **数据输出路径**（`export_path`）\n", "3. **数据清洗步骤（`process` 下的所有操作）**：\n", "   - **文本格式清理**\n", "   - **字符清理**\n", "   - **去除重复句子**\n", "   - **长度过滤**\n", "   - **相似度去重**\n", "   - **句子结构修正**\n", "   - **文本分词 & 统计**\n", "4. **并行处理参数**（`np`）\n", "\n", "---\n", "\n", "我们设置了2种不同**初始算子组合**、并在数据筛选流程中不断改写其中的细节，我们的初始算子分别是：\n", "\n", "> - **文本数据清洗算子**<br><br>\n", "和官方推荐的文本清洗算子相比、我们删除了`fix_unicode_mapper`（修复 Unicode 编码错误）以及`flagged_words_filter`（过滤包含特定敏感词的文本），对于已经过整理和清洗的数据而言、无需再使用前两个修复错误的算子。另外、针对中文数据、我们删除了`remove_repeat_sentences_mapper`，这个算子大多用于英文、并且对于已经清洗好的中文数据我们也不必再使用。针对英文数据，我们还删除了`chinese_convert_mapper`（简繁体转换，专用于中文）这一专用于中文字的算子。<br><br>\n", "| 算子名称                          | 功能描述                           | 可配置参数 |\n", "|--------------------------------|--------------------------------|---------|\n", "| `chinese_convert_mapper`      | 转换简繁体和日文汉字、**仅限中文数据**              | `mode`: 选择转换模式（如 `t2s`: 繁体转简体） |\n", "| `clean_email_mapper`         | 删除邮件地址                      | 无 |\n", "| `clean_html_mapper`          | 删除 HTML 代码                   | 无 |\n", "| `clean_ip_mapper`            | 删除 IP 地址                      | 无 |\n", "| `clean_links_mapper`         | 删除网页链接                      | 无 |\n", "| `clean_copyright_mapper`     | 删除版权声明                      | 无 |\n", "| `expand_macro_mapper`        | 展开 LaTeX 宏定义                 | 无 |\n", "| `punctuation_normalization_mapper` | 标准化标点符号                | 无 |\n", "| `remove_repeat_sentences_mapper` | 去除重复句子、**仅限英文数据**                  | `lowercase`: 是否忽略大小写, `ignore_special_character`: 是否忽略特殊字符, `min_repeat_sentence_length`: 最小重复句子长度 |\n", "| `remove_specific_chars_mapper` | 移除特定字符                   | `chars_to_remove`: 指定要删除的字符列表 |\n", "| `whitespace_normalization_mapper` | 标准化空白字符                | 无 |\n", "| `alphanumeric_filter`        | 按字母/数字比例过滤文本            | `min_ratio`: 最小比例, `max_ratio`: 最大比例, `tokenization`: 是否基于 token 计算 |\n", "| `average_line_length_filter` | 按平均行长度过滤文本              | `min_len`: 最小长度, `max_len`: 最大长度 |\n", "| `character_repetition_filter` | 按字符重复率过滤文本              | `min_ratio`: 最小比例, `max_ratio`: 最大比例 |\n", "| `maximum_line_length_filter` | 按最长行长度过滤文本              | `max_len`: 最大长度 |\n", "| `perplexity_filter`          | 按困惑度（Perplexity）过滤文本     | `lang`: 语言, `max_ppl`: 最大困惑度 |\n", "| `special_characters_filter`  | 按特殊字符比例过滤文本            | `min_ratio`: 最小比例, `max_ratio`: 最大比例 |\n", "| `text_length_filter`         | 按文本长度过滤                   | `min_len`: 最小长度, `max_len`: 最大长度 |\n", "| `words_num_filter`           | 按单词数量过滤文本                | `lang`: 语言, `min_num`: 最小数量, `max_num`: 最大数量, `tokenization`: 是否使用 token |\n", "| `word_repetition_filter`     | 按单词重复率过滤文本              | `lang`: 语言, `min_ratio`: 最小比例, `max_ratio`: 最大比例, `tokenization`: 是否使用 token |\n", "| `document_simhash_deduplicator` | 基于 SimHash-LSH 进行去重 | `tokenization`: 分词方式（space, punctuation, character）, `window_size`: 窗口大小, `num_blocks`: 哈希块数, `hamming_distance`: 最大汉明距离, `lowercase`: 是否转换为小写 |\n", "\n", "> - **代码/数学数据清洗算子**<br><br>\n", "代码算子会更偏向于结构化、规则化匹配，尽量创造格式化数据的结构、完成字符、字词级别过滤等等。<br><br>\n", "| 算子名称                            | 功能描述 |\n", "|----------------------------------|-------------------------------------------|\n", "| `clean_copyright_mapper`        | 删除代码文件开头的版权声明（必须包含单词 `copyright`） |\n", "| `clean_email_mapper`            | 删除邮箱信息 |\n", "| `clean_links_mapper`            | 删除链接，例如以 `http` 或 `ftp` 开头的链接 |\n", "| `fix_unicode_mapper`            | 修复损坏的 Unicode（借助 `ftfy` 库） |\n", "| `punctuation_normalization_mapper` | 将各种 Unicode 标点符号标准化为其 ASCII 等效项 |\n", "| `alphanumeric_filter`           | 仅保留字母数字比例在指定范围内的样本 |\n", "| `average_line_length_filter`    | 仅保留平均行长度在指定范围内的样本 |\n", "| `character_repetition_filter`   | 仅保留字符重复率（char-level n-gram）在指定范围内的样本 |\n", "| `maximum_line_length_filter`    | 仅保留最大行长度在指定范围内的样本 |\n", "| `text_length_filter`            | 仅保留文本长度在指定范围内的样本 |\n", "| `word_num_filter`               | 仅保留字数在指定范围内的样本 |\n", "| `word_repetition_filter`        | 仅保留词重复率（word-level n-gram）在指定范围内的样本 |\n", "| `document_simhash_deduplicator` | 使用 `SimHash` 在文档级别对样本去重 |"]}, {"cell_type": "markdown", "id": "b5f05573-72d4-48b1-abba-be842278c780", "metadata": {}, "source": ["### 6.3 data-jucier所需参数设置"]}, {"cell_type": "markdown", "id": "6cafcac3-939f-435c-8970-b3433a107a04", "metadata": {}, "source": ["- **processed_ape210k.jsonl**\n", "  \n", "| 统计项 (过滤器)                          | 平均值  | 中位数  | 最小值 | 最大值 | 95% 分位数 |\n", "|--------------------------------------|------|------|----|------|------|\n", "| **单样本文本字符数 (text_length_filter)**    | 344.1  | 309.0  | 95  | 2765  | 638.0  |\n", "| **单样本所含单词数 (words_num_filter)**         | 52.46  | 46.0  | 13  | 568  | 103.0  |\n", "| **平均行长度 (average_line_length_filter)**  | 33.89  | 33.62  | 22.25  | 123.5  | 40.3  |\n", "| **最长行长度 (maximum_line_length_filter)**  | 65.57  | 60.0  | 36  | 1366  | 94.0  |\n", "| **字符重复率 (character_repetition_filter)** | 0.07  | 0.07  | 0.02  | 0.35  | 0.09  |\n", "| **单词重复率 (word_repetition_filter)**  | 0.13  | 0.12  | 0.04  | 0.76  | 0.19  |\n", "| **特殊字符比例 (special_characters_filter)**  | 0.22  | 0.22  | 0.1  | 0.49  | 0.25  |"]}, {"cell_type": "markdown", "id": "ac014873-09c1-4323-b1ef-e586a9b0923f", "metadata": {}, "source": ["考虑到ape本身数据量较小、最后决定的参数十分宽松。一般来说数学流程的单词重复率和字符重复率都很高，在很小的范围内就容易出现重复，因此要小心筛选。同时、对数学而言困惑度无意义、不进行困惑度筛选。\n", "\n", "```python\n", "    # 单样本文本字符数\n", "  - text_length_filter:\n", "      max_len: 2700 # 接近最大值\n", "\n", "    # 单样本所含单词数\n", "  - words_num_filter:\n", "      min_num: 20 # 只筛下限不筛上限\n", "      max_num: 99999 # 和openr1相比都很短\n", "\n", "    # 平均行长度\n", "  - average_line_length_filter:\n", "      min_len: 20  # 比最小值更小\n", "      max_len: 150  # 比最大值更大\n", "\n", "    # 最长行长度\n", "  - maximum_line_length_filter:\n", "      max_len: 1000  # 接近最大值\n", "\n", "    # 字符重复率\n", "  - character_repetition_filter:\n", "      max_ratio: 0.5  # 数学、重复率允许较高\n", "\n", "    # 单词重复率\n", "  - word_repetition_filter:\n", "      rep_len: 10\n", "      max_ratio: 0.75 # 数学、重复率允许较高\n", "      \n", "    # 特殊字符比例\n", "  - alphanumeric_filter:\n", "      tokenization: false\n", "      min_ratio: 0.0  # 不设下限\n", "      max_ratio: 0.5  # 数学、上限允许高一些\n", "\n", "  - document_simhash_deduplicator:\n", "      tokenization: space\n", "      window_size: 6 # 十分宽松的去重\n", "      lowercase: true\n", "      ignore_pattern: '\\p{P}'\n", "      num_blocks: 6\n", "      hamming_distance: 4\n", "```"]}, {"cell_type": "markdown", "id": "28aeb4db-973f-4024-baaa-14c174ffa547", "metadata": {}, "source": ["- **processed_openr1.jsonl**\n", "  \n", "| 统计项 (过滤器)                          | 平均值  | 中位数  | 最小值 | 最大值 | 95% 分位数 |\n", "|--------------------------------------|------|------|----|------|------|\n", "| **单样本文本字符数 (text_length_filter)**    | 29699.23  | 22220.0  | 756  | 196439  | 77882.0  |\n", "| **单样本所含单词数 (words_num_filter)**         | 6035.55  | 4499.0  | 119  | 41181  | 16013.0  |\n", "| **平均行长度 (average_line_length_filter)**  | 1162.5  | 834.43  | 17.77  | 9461.0  | 3186.56  |\n", "| **最长行长度 (maximum_line_length_filter)**  | 15889.56  | 11683.0  | 251  | 79845  | 43239.0  |\n", "| **字符重复率 (character_repetition_filter)** | 0.18  | 0.18  | 0.04  | 0.81  | 0.2  |\n", "| **单词重复率 (word_repetition_filter)**  | 0.08  | 0.07  | 0.02  | 0.9  | 0.12  |\n", "| **特殊字符比例 (special_characters_filter)**  | 0.15  | 0.15  | 0.03  | 0.46  | 0.23  |"]}, {"cell_type": "markdown", "id": "59ed63ae-39a4-4282-ac7c-358700c939f2", "metadata": {}, "source": ["推理文本可长可短、但是还是倾向于使用比较长的推理文本，因此上限基本都给得比较宽松、下限反而给得相对严格一些，但是这可能会筛掉很多简单的数学推理题目、让模型最终反而无法回答简单的问题。同时，同样作为数学题目、openr1对于重复词筛选也会非常敏感，因此也需要谨慎处理。\n", "\n", "```python\n", "    # 单样本文本字符数\n", "  - text_length_filter:\n", "      max_len: 80000 # 略超过95%中位数\n", "\n", "    # 单样本所含单词数\n", "  - words_num_filter:\n", "      min_num: 150 # 保证推理文本不会太短\n", "      max_num: 17000 # 略超过95%中位数\n", "\n", "    # 平均行长度\n", "  - average_line_length_filter:\n", "      min_len: 30 # 保证推理文本不太短\n", "      max_len: 8000  # 给得也比较宽松\n", "\n", "    # 最长行长度\n", "  - maximum_line_length_filter:\n", "      max_len: 45000  # 略超过95%中位数\n", "\n", "    # 字符重复率\n", "  - character_repetition_filter:\n", "      max_ratio: 0.35  # 极值太大、设置超过95%中位数\n", "\n", "    # 单词重复率\n", "  - word_repetition_filter:\n", "      rep_len: 10\n", "      max_ratio: 0.35 # 极值太大、设置超过95%中位数\n", "\n", "    # 特殊字符比例\n", "  - alphanumeric_filter:\n", "      tokenization: false\n", "      min_ratio: 0.0  # 不设下限\n", "      max_ratio: 0.8  # 上限允许0.5\n", "\n", "  - document_simhash_deduplicator:\n", "      tokenization: space\n", "      window_size: 6 # 十分宽松的去重\n", "      lowercase: true\n", "      ignore_pattern: '\\p{P}'\n", "      num_blocks: 6\n", "      hamming_distance: 4\n", "\n", "```"]}, {"cell_type": "markdown", "id": "daa2a9fc-4606-40bc-9d85-2b91bb1350e4", "metadata": {}, "source": ["- **processed_skypile.jsonl**\n", "  \n", "| 过滤器 | 统计项 | 平均值 | 中位数 | 最小值 | 最大值 | 95% 分位数 |\n", "|--------|--------|--------|--------|--------|--------|------------|\n", "| **单样本文本字符数** | `text_length_filter` | 1028.12 | 741.0 | 5 | 203536 | 2681.0 |\n", "| **单样本所含单词数** | `words_num_filter` | 97.08 | 69.0 | 0 | 21526 | 255.0 |\n", "| **平均行长度** | `average_line_length_filter` | 134.06 | 109.0 | 5.0 | 175508.0 | 274.0 |\n", "| **最长行长度** | `maximum_line_length_filter` | 233.84 | 195.0 | 5 | 194936 | 471.0 |\n", "| **字符重复率** | `character_repetition_filter` | 0.05 | 0.05 | 0.01 | 1.0 | 0.07 |\n", "| **单词重复率** | `word_repetition_filter` | 0.04 | 0.03 | 0.0 | 1.0 | 0.09 |\n", "| **特殊字符比例** | `special_characters_filter` | 0.1 | 0.09 | 0.0 | 1.0 | 0.14 |"]}, {"cell_type": "markdown", "id": "6bb7a719-58e8-4283-acaa-00b6e60b187f", "metadata": {}, "source": ["文本极值很大很多、在之后可能会重整文本分割方式、因此在设置时没有使用大力度进行筛选。但是对文本来说，过长的段落可能意味着文本质量不高，因此既希望保留尽量多的数据、同时也希望有一定的筛选力度，在无法抉择时就使用1/4 * 最大值的方式进行压制。\n", "\n", "```python\n", "  # 困惑度筛选，考虑在困惑度上进行宽松的筛选、给了比较大的困惑度\n", "  - perplexity_filter: \n", "      lang: zh                                                \n", "      max_ppl: 1500              \n", "      \n", "    # 单样本文本字符数\n", "  - text_length_filter:\n", "      max_len: 50000 # 极值与95%分位数差异太大、对文本来说，取极值的1/4\n", "\n", "    # 单样本所含单词数\n", "  - words_num_filter:\n", "      min_num: 10 # 至少是一句话  \n", "      max_num: 20000 # 接近最大值\n", "\n", "    # 平均行长度\n", "  - average_line_length_filter:\n", "      min_len: 10 # 最小值的两倍\n", "      max_len: 50000 # 极值与95%分位数差异太大、取极值的1/4\n", "\n", "    # 最长行长度\n", "  - maximum_line_length_filter:\n", "      max_len: 50000  # 取极值的1/4\n", "\n", "    # 字符重复率\n", "  - character_repetition_filter:\n", "      max_ratio: 0.35  # 极值与95%分位数差异太大、取极值的1/3\n", "\n", "    # 单词重复率\n", "  - word_repetition_filter:\n", "   #   rep_len: 10   只对word-level有效、对中文无效\n", "      max_ratio: 0.35 # 极值太大、取极值的1/3\n", "\n", "    # 特殊字符比例\n", "  - alphanumeric_filter:\n", "      tokenization: false\n", "      min_ratio: 0.0  # 不设下限\n", "      max_ratio: 0.35  # 极值太大、取极值的1/3\n", "\n", "```"]}, {"cell_type": "markdown", "id": "04f020e8-0efb-42e7-9ca7-7494bd626b6b", "metadata": {}, "source": ["合理的困惑度（PPL）值取决于数据类型、语言模型的复杂度、文本质量等多个因素。一般来说，**困惑度越低，文本的质量越好**，但**过低可能意味着文本太简单或有问题**。\n", "\n", "| **数据类型**      | **理想困惑度范围 (PPL)** | **解释** |\n", "|----------------|----------------|---------------------------|\n", "| **高质量文本**（新闻、百科） | **10 - 500**  | 低困惑度，流畅且符合语法 |\n", "| **学术论文** | **20 - 1000** | 句子结构复杂，困惑度较高 |\n", "| **代码（编程语言）** | **50 - 300** | 代码结构稳定，但有关键字限制 |\n", "| **社交媒体（短文本）** | **100 - 2000** | 口语化表达，变化较大 |\n", "| **爬取的原始网页数据** | **500 - 5000+** | 噪音较多，可能包含垃圾文本 |\n", "\n", "💡 **一般筛选建议**：\n", "- **困惑度 > 1500** → **可能是低质量文本**（噪声、乱码、拼写错误）。\n", "- **困惑度 < 50** → **可能是格式化数据**（如模板化内容，重复性高）。\n", "- **困惑度 100-1000** → 适合**大多数高质量自然文本**。"]}, {"cell_type": "markdown", "id": "7a10a21e-30b1-4fac-af29-8043385005c1", "metadata": {}, "source": ["- **processed_starcoder.jsonl**\n", "\n", "| 过滤器 | 统计项 | 平均值 | 中位数 | 最小值 | 最大值 | 95% 分位数 |\n", "|--------|--------|--------|--------|--------|--------|------------|\n", "| **文本字符数** | `text_length_filter` | 3904.01 | 1298.0 | 2 | 2374708 | 13131.0 |\n", "| **单词数** | `words_num_filter` | 444.53 | 146.0 | 1 | 524313 | 1477.0 |\n", "| **平均行长度** | `average_line_length_filter` | 75.69 | 35.22 | 1.0 | 320654.5 | 125.52 |\n", "| **最长行长度** | `maximum_line_length_filter` | 214.18 | 96.0 | 1 | 1025819 | 614.0 |\n", "| **字符重复率** | `character_repetition_filter` | 0.16 | 0.14 | 0.0 | 1.0 | 0.32 |\n", "| **单词重复率** | `word_repetition_filter` | 0.09 | 0.07 | 0.0 | 1.0 | 0.2 |\n", "| **特殊字符比例** | `special_characters_filter` | 0.14 | 0.13 | 0.0 | 0.77 | 0.24 |\n"]}, {"cell_type": "markdown", "id": "a58c6e08-a77a-47c9-abdc-c0d8fe2cef8f", "metadata": {}, "source": ["> **📌 代码数据 vs 文字数据的筛选差异**\n", "| 方面  | 代码数据 | 文字数据 |\n", "|------------|--------------------------------------|--------------------------------------------------|\n", "| **结构** | 结构化、层次分明，有语法约束 | 非结构化，句法和段落灵活 |\n", "| **完整性** | 代码片段通常需要完整才能执行 | 文字可以被切分、重组 |\n", "| **换行/格式** | 代码换行是语义的一部分（如函数、类定义） | 文字换行主要是排版作用，不影响理解 |\n", "| **冗余性** | 重复代码可能是必要的（如变量声明、导入库） | 过多重复文本通常是低质量内容 |\n", "| **长度问题** | 代码短小精悍为佳，过长可能是自动生成的垃圾数据 | 文字数据往往有极大极小值，需要合理归一化 |\n", "| **特殊字符** | 特殊符号（`{}`、`[]`、`<>`、`=`）在代码中不可缺 | 文字中过多特殊符号可能表示乱码或低质量文本 |"]}, {"cell_type": "markdown", "id": "4ee51661-c21e-43f9-90a2-f9fd79472d51", "metadata": {}, "source": ["考虑到starcoder其实是经过严格清洗的数据、我们给的条件相对宽松。考虑到代码对完整性的要求较高，较长的代码可能具有较复杂的逻辑供模型学习，我们的筛选条件相对保守——\n", "\n", "```python\n", "    # 单样本文本字符数\n", "  - text_length_filter:\n", "      max_len: 1000000 # 长代码虽然也有冗余嫌疑、但大部分情况下具有意义，取极值的一半\n", "\n", "    # 单样本所含单词数\n", "  - words_num_filter:\n", "      min_num: 20 # 不能太低，过短的代码可能是无意义的单行代码片段 \n", "      max_num: 250000 # 取极值的一半\n", "\n", "    # 平均行长度\n", "  - average_line_length_filter:\n", "      min_len: 20 # 同样不能太低\n", "      max_len: 150000 # 极值与95%分位数差异太大、取极值的一半\n", "\n", "    # 最长行长度\n", "  - maximum_line_length_filter:\n", "      max_len: 500000  # 取极值的一半\n", "\n", "    # 字符重复率\n", "  - character_repetition_filter:\n", "      max_ratio: 0.6  # 在代码中字符会有较高的重复率、属于正常、取极值2/3\n", "\n", "    # 单词重复率\n", "  - word_repetition_filter:\n", "      rep_len: 10\n", "      max_ratio: 0.6 # 在代码中单词也会有较高的重复率，取极值2/3\n", "\n", "    # 特殊字符比例\n", "  - alphanumeric_filter:\n", "      tokenization: false\n", "      min_ratio: 0.0  # 不设下限\n", "      max_ratio: 0.6  # 代码中需要特殊字符，所以不能严格限制\n", "\n", "  - document_simhash_deduplicator:\n", "      tokenization: space\n", "      window_size: 6 #使用较大的window_size、使去重的颗粒度变粗\n", "      lowercase: true\n", "      ignore_pattern: '\\p{P}'\n", "      num_blocks: 6\n", "      hamming_distance: 4\n", "```"]}, {"cell_type": "markdown", "id": "1400356c-7d4b-45e3-93d5-9807011fcf0a", "metadata": {}, "source": ["- **processed_slimpajama.jsonl**\n", "  \n", "| 过滤项                        | 平均值    | 中位数   | 最小值  | 最大值   | 95% 分位数 |\n", "|-------------------------------|----------|---------|--------|---------|-----------|\n", "| **文本字符数** (text_length_filter) | 3823.63  | 1981.0  | 20     | 100000  | 12422.0   |\n", "| **单词数** (words_num_filter) | 634.15   | 327.0   | 1      | 28850   | 2061.0    |\n", "| **平均行长度** (average_line_length_filter) | 3823.63  | 1981.0  | 20.0   | 100000.0 | 12422.0   |\n", "| **最长行长度** (maximum_line_length_filter) | 3823.63  | 1981.0  | 20     | 100000  | 12422.0   |\n", "| **字符重复率** (character_repetition_filter) | 0.16     | 0.16    | 0.02   | 1.0     | 0.19      |\n", "| **单词重复率** (word_repetition_filter) | 0.06     | 0.05    | 0.0    | 1.0     | 0.1       |\n", "| **特殊字符比例** (special_characters_filter) | 0.04     | 0.03    | 0.0    | 0.97    | 0.1       |"]}, {"cell_type": "markdown", "id": "775f5793-b292-49a2-abeb-ffc79fe916d9", "metadata": {}, "source": ["英文文本、处理逻辑基本与中文文本接近，既希望保留尽量多的数据、同时也希望有一定的筛选力度，在无法抉择时就使用1/4 * 最大值的方式进行压制。"]}, {"cell_type": "markdown", "id": "ed8e4b8c-70c2-41f2-8be5-0174b19cfd0d", "metadata": {}, "source": ["```python\n", "    # 单样本文本字符数\n", "  - text_length_filter:\n", "      max_len: 25000 # 极值与95%分位数差异太大、对文本来说，取极值的1/4\n", "\n", "    # 单样本所含单词数\n", "  - words_num_filter:\n", "      min_num: 10 # 至少是一句话  \n", "      max_num: 25000 # 接近最大值\n", "\n", "    # 平均行长度\n", "  - average_line_length_filter:\n", "      min_len: 20 # 按最小值走\n", "      max_len: 25000 # 极值与95%分位数差异太大、取极值的1/4\n", "\n", "    # 最长行长度\n", "  - maximum_line_length_filter:\n", "      max_len: 25000  # 取极值的1/4\n", "\n", "    # 字符重复率\n", "  - character_repetition_filter:\n", "      max_ratio: 0.35  # 极值与95%分位数差异太大、取极值的1/3\n", "\n", "    # 单词重复率\n", "  - word_repetition_filter:\n", "      rep_len: 10   只对word-level有效、对中文无效\n", "      max_ratio: 0.35 # 极值太大、取极值的1/3\n", "\n", "    # 特殊字符比例\n", "  - alphanumeric_filter:\n", "      tokenization: false\n", "      min_ratio: 0.0  # 不设下限\n", "      max_ratio: 0.35  # 极值太大、取极值的1/3\n", "\n", "    # 困惑度筛选，使用了默认值\n", "  - perplexity_filter:\n", "      lang: en\n", "      max_ppl: 1500\n", "\n", "  - document_simhash_deduplicator:\n", "      tokenization: space\n", "      window_size: 6 #使用较大的window_size、使去重的颗粒度变粗\n", "      lowercase: true\n", "      ignore_pattern: '\\p{P}'\n", "      num_blocks: 6\n", "      hamming_distance: 4\n", "```"]}, {"cell_type": "markdown", "id": "a2fadc66-c851-41a8-94e0-ae8d29f1c3b7", "metadata": {}, "source": ["## 7 数据处理第三步：数据合并与Tokenizer"]}, {"cell_type": "markdown", "id": "965ee281-6772-4671-8943-17eb12e13150", "metadata": {}, "source": ["现在我们已经有了清洗好的、经过质量控制的数据，接下来我们则需要用一段代码将所有数据进行打包和分词、并最终将数据处理为二进制bin文件、供后续的预训练进行使用。\n", "\n", "```\n", "【step 1】📂 raw data (PDF/HTML/TXT)\n", "            ↓  (转换)\n", "【step 2】📂 JSONL {\"text\": \"...\"}\n", "            ↓  (清洗)\n", "          📂 cleaned JSONL\n", "            ↓  (质量控制)\n", "【step 3】📂 filtered JSONL\n", "            ↓  (tokenizer)\n", "          📂 Tokenized JSONL {\"tokens\": [...]}\n", "            ↓  (拼接)\n", "          📂 Pretrain Data (bin/lmdb)\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "4b1e8fd8-2372-4f59-a92b-baea8beffb37", "metadata": {}, "outputs": [], "source": ["zh(90G), en(90G), code(60G), math (8G), experts(2G)"]}, {"cell_type": "markdown", "id": "81776530-4ac0-43e3-846a-0754418cacab", "metadata": {}, "source": ["在进行打包时，我们所使用的代码是`step3_prepare_data_for_pretrain.py`，其中涉及到的流程有——\n", "\n", "1. **对数据进行混合及按比例采样**\n", "\n", "2. **对数据加上前缀或者后缀脚标**\n", "\n", "3. **使用multiprocessing启用多进程、同步对数据进行tokenizer**\n", "\n", "4. **使用PackedDatasetBuilder将数据拆分成小型二进制bin文件**\n", "\n", "最终我们为预训练准备的文件结构为 ↓"]}, {"cell_type": "markdown", "id": "93be8a24-7e5b-4bd7-ade9-69111e1afd2a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/153.png)"]}, {"cell_type": "markdown", "id": "c2f63aaf-b89f-4f5f-b9a2-b29d6dcaa773", "metadata": {}, "source": ["### 7.1 数据合并与tokenizer的执行脚本"]}, {"cell_type": "markdown", "id": "66b2e2e8-5d86-4049-b7d9-1d7cb73a51e6", "metadata": {}, "source": ["**在执行下面的脚本之前，请务必先删除data-jucier生成的数据中的stats那个jsonl** ↓"]}, {"cell_type": "markdown", "id": "8f176e12-6508-4265-97b0-444945a8db85", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/150.png)"]}, {"cell_type": "markdown", "id": "4eda2d64-41db-4d59-907a-cd429aa6ed64", "metadata": {}, "source": ["在下面的每个文件夹中都删除stats jsonl后，上传tokenizer文件夹至`/root/autodl-tmp/minideepseek/v3/`，你的文件夹里应该有如下的所有内容↓\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/151.png)\n", "\n", "确保你的设备上有至少500G左右的硬盘空间（运行到这一步，你可以考虑删除最初下载的原始数据、二只保留step1和step2留下的数据）、开始运行下面的代码 ↓"]}, {"cell_type": "markdown", "id": "2850d06f-23c2-4f05-a9c2-68109244dc32", "metadata": {}, "source": ["```shell\n", "# 使用训练的虚拟环境\n", "deactivate\n", "cd ~/autodl-tmp/minideepseek/v3\n", "source mnds_training/bin/activate\n", "\n", "# 建立新的数据存储目录\n", "mkdir /root/autodl-tmp/minideepseek/v3/data/final_data\n", "\n", "# 配置环境\n", "pip install lightning\n", "pip <PERSON> j<PERSON>arg<PERSON>e\n", "\n", "# 将step3代码上传至tokenizer\n", "# 进入代码执行目录\n", "cd /root/autodl-tmp/minideepseek/v3/tokenizer\n", "\n", "# 执行step3代码\n", "#【TIME WARNING：64进程并行、约4小时】\n", "python step3_prepare_data_for_pretrain.py\n", "\n", "python step3_prepare_data_for_pretrain_upsample.py\n", "```"]}, {"cell_type": "markdown", "id": "d3a0074c-68f6-4464-8127-d31ea77005ae", "metadata": {}, "source": ["当你顺利运行后，你会看到 ↓"]}, {"cell_type": "markdown", "id": "72aaf002-a4b4-40f8-a94a-4013d21ce20a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/154.png)"]}, {"cell_type": "markdown", "id": "8636460a-025d-4505-8b86-aae3d9bfd86b", "metadata": {}, "source": ["你可以用下面的代码来检查你的多进程是否顺利被启用 ↓"]}, {"cell_type": "markdown", "id": "014be94e-ed7f-475d-a2e6-48f71ae43a25", "metadata": {}, "source": ["```shell\n", "htop\n", "```"]}, {"cell_type": "markdown", "id": "685553b0-e1d1-4528-a967-ff0156a97ca9", "metadata": {}, "source": ["如果你看到有大量的进程在同步运行，其中CPU%的列下面有大量的100%或者接近100%的数字，则说明代码被顺利启用 ↓ 如果只有一个进程占比100%，则说明没有成功启用multiprocessing。"]}, {"cell_type": "markdown", "id": "b38546af-29b4-43d2-a83d-5dc4634d35a7", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/152.png)"]}, {"cell_type": "markdown", "id": "d26d9761-0a12-4738-8d50-fbd550c0fe7c", "metadata": {}, "source": ["### 7.2 数据合并脚本解读"]}, {"cell_type": "markdown", "id": "40432417-e6e5-4004-8864-b4b3f6aac9b6", "metadata": {}, "source": ["```python\n", "import glob\n", "import json\n", "import os\n", "import sys\n", "from pathlib import Path\n", "import multiprocessing as mp\n", "import queue\n", "import numpy as np\n", "from tqdm import tqdm\n", "\n", "# support running without installing as a package\n", "wd = Path(__file__).parent.parent.resolve()\n", "sys.path.append(str(wd))\n", "\n", "import random\n", "random.seed(666)\n", "import lit_gpt.packed_dataset as packed_dataset\n", "from lit_gpt import Config, Tokenizer\n", "from transformers import AutoTokenizer\n", "import time\n", "\n", "filenames_sample = []\n", "max_length = 131072  # 限制最大 token 长度，避免超长文本导致模型崩溃\n", "\n", "# key: name value: dir\n", "\n", "###################################\n", "###  处理单个文本，执行 Tokenizer ###\n", "###################################\n", "def process_line_text(text, tokenizer, max_length=131072):\n", "    \"\"\"\n", "    负责对文本进行 Tokenizer 处理，并把超长 Token 切分成多个小块进行存储。\n", "    其中max_length是tokenizer的config设置中规定的数字。\n", "    \"\"\"\n", "    text_ids = tokenizer.encode(text)\n", "\n", "    if len(text_ids) > max_length:\n", "        print(f\"⚠️ 超长文本 ({len(text_ids)} tokens) 被拆分！\")\n", "        return [item for chunk in [text_ids[i:i+max_length] for i in range(0, len(text_ids), max_length)] for item in chunk]\n", "    else:\n", "        return [text_ids]\n", "\n", "##################################################\n", "###  处理 JSONL 文件，将其 Tokenize 并存入数据集 ###\n", "#################################################\n", "def process_jsonl_file(set_name, file_dir, builder, tokenizer, cache_lines_num):\n", "    \"\"\"\n", "    读取 JSONL 文件，解析其中的 text 字段，并将其转换为 Token ID，存入 PackedDatasetBuilder 进行保存。\n", "    其中，我们的 JSONL 文件有固定格式，为 {\"text\": \"xxxx\"}。\n", "    \"\"\"\n", "    cache_text = \"\"\n", "    counter = 0\n", "    with open(file_dir, encoding=\"utf-8\") as f:\n", "        for row in tqdm(f):\n", "            counter += 1\n", "            try:\n", "                # 直接提取 \"text\" 字段\n", "                text = json.loads(row)[\"text\"]\n", "            except Exception as e:\n", "                print(f\"文件 {file_dir} 第 {counter} 行 读取错误: {e}\")\n", "                counter -= 1\n", "                continue\n", "\n", "            text += \"<|im_end|>\"  # 追加结束符\n", "            cache_text += text\n", "\n", "            # 每 cache_lines_num 行处理一次\n", "            if counter % cache_lines_num == 0:\n", "                text_ids = process_line_text(cache_text, tokenizer)\n", "                builder.add_array(np.array(text_ids, dtype=builder.dtype))\n", "                cache_text = \"\" \n", "\n", "        # 处理剩余缓存数据\n", "        if cache_text:\n", "            text_ids = process_line_text(cache_text, tokenizer)\n", "            builder.add_array(np.array(text_ids, dtype=builder.dtype))\n", "\n", "##############################\n", "###  数据集上采样权重设置    ###\n", "##############################\n", "upsample_dict = {\n", "    \"djed_ape210K\": 3,   # 该数据集增加 3 倍\n", "    \"djed_openr1\": 2,    # 该数据集增加 2 倍\n", "    \"djed_starcoder\": 1, # 该数据集正常采样\n", "    \"djed_skypile\": 1,   # 该数据集正常采样\n", "    \"djed_slimpajama\": 1 # 该数据集正常采样\n", "}\n", "\n", "########################################\n", "###  处理多个 JSONL 文件（多进程并行） ####\n", "########################################\n", "def multiprocess_data(set_name, file_dir_list, builder, tokenizer, cache_lines_num, process_idx=0, upsample_dict=None):\n", "    \"\"\"\n", "    多进程处理数据，负责调用 process_jsonl_file 处理多个 JSONL 文件。\n", "    并且根据 `upsample_dict` 进行数据集上采样。\n", "    \"\"\"\n", "    if upsample_dict is None:\n", "        upsample_dict = {}\n", "\n", "    try:\n", "        for file_dir in file_dir_list:\n", "            upsample_factor = upsample_dict.get(set_name, 1)  # 获取数据集的上采样倍数\n", "            for _ in range(int(upsample_factor)):  # 多次处理该数据集\n", "                t0 = time.time()\n", "                process_jsonl_file(set_name, file_dir, builder, tokenizer, cache_lines_num)\n", "                print(f\"Process {process_idx} 处理 {file_dir} ({upsample_factor} 倍) 耗时 {time.time()-t0:.2f}s\")\n", "\n", "        print(f\"{builder._prefix} 已处理 {builder._counter+1} 文件，共 {builder.all_tokens} tokens\")\n", "        builder.write_reminder()\n", "\n", "    except Exception as e:\n", "        print(f\"multiprocess_data 处理 {set_name} 发生错误: {str(e)}\")\n", "\n", "##############################\n", "###  遍历文件并创建进程队列 ###\n", "##############################\n", "def prepare_full(source_path: Path, checkpoint_dir: Path, destination_path: Path, : int, \n", "                 match: str = \"\", max_files=100000000000000, cache_lines_num=1000, process_num=64) -> None:\n", "    \"\"\"\n", "    遍历 filename_sets 字典，根据 glob 匹配路径，找到需要处理的所有 JSONL 文件。\n", "    并手动处理 multiprocessing 多进程操作，按进程数量 process_num 划分文件，每个进程处理一部分数据。\n", "    \"\"\"\n", "\n", "    destination_path.mkdir(parents=True, exist_ok=True)\n", "    tokenizer = AutoTokenizer.from_pretrained(checkpoint_dir)\n", "\n", "    for set_name, pattern in filename_sets.items():\n", "        if match and match not in set_name:\n", "            continue\n", "\n", "        t0 = time.time()\n", "        filenames = glob.glob(os.path.join(source_path, pattern), recursive=True)\n", "        filenames = sorted(filenames)\n", "        random.shuffle(filenames)\n", "        filenames = filenames[:max_files]\n", "\n", "        if not filenames:\n", "            print(f\"⚠️ No files found for {set_name}, skipping...\")\n", "            continue\n", "\n", "        # 计算动态 `process_num`\n", "        process_num = 1 if len(filenames) == 1 else min(64, len(filenames))\n", "\n", "        print(f\"📌 处理 {set_name}，文件数: {len(filenames)}, 启动进程数: {process_num}\")\n", "\n", "        builder_list = [\n", "            packed_dataset.PackedDatasetBuilder(\n", "                outdir=destination_path,\n", "                prefix=f\"{set_name}_process{i}\",\n", "                chunk_size = chunk_size,\n", "                sep_token=tokenizer.pad_token_id,\n", "                dtype=\"auto\",\n", "                vocab_size=len(tokenizer),\n", "            ) for i in range(process_num)\n", "        ]\n", "        \n", "        # 在文件数超过process_num的时候\n", "        # 拉起排队、确保每个文件都能被处理到\n", "        task_queue = queue.Queue()\n", "        for filename in filenames:\n", "            task_queue.put(filename)\n", "\n", "        def worker(process_idx):\n", "            while not task_queue.empty():\n", "                try:\n", "                    file_dir = task_queue.get_nowait()\n", "                except queue.Empty:\n", "                    break\n", "                print(f\"🛠️ Process {process_idx} handling {file_dir}\")\n", "                multiprocess_data(set_name, [file_dir], builder_list[process_idx % process_num],\n", "                                  tokenizer, cache_lines_num, process_idx)\n", "\n", "        process_list = []\n", "        for process_idx in range(process_num):\n", "            process = mp.Process(target=worker, args=(process_idx,))\n", "            process_list.append(process)\n", "            process.start()\n", "\n", "        for process in process_list:\n", "            process.join()\n", "\n", "        print(f\"✅ 处理完成 {set_name}，总耗时: {time.time()-t0:.2f}s\")\n", "\n", "##############################\n", "###  运行主程序             ###\n", "##############################\n", "if __name__ == \"__main__\":\n", "    \"\"\"允许命令行调用 prepare 进行数据预处理\"\"\"\n", "    import jsonargparse\n", "    from jsonargparse import CLI\n", "    CLI(prepare)\n", "\n", "```"]}, {"cell_type": "markdown", "id": "3d753f14-9f21-4812-b34e-8a9d163d1bde", "metadata": {}, "source": ["#### 7.2.1 PackedDataset的实现与使用"]}, {"cell_type": "markdown", "id": "e4bd994c-3967-447a-9dd8-2d238fbb79c4", "metadata": {}, "source": ["`PackedDatasetBuilder` 主要用于 **高效存储和管理 tokenized 数据，以便后续模型训练**。它的核心目标是：\n", "- **将多个 token 序列打包成连续的存储格式**，减少 I/O 读取成本，提高训练时的数据加载效率。\n", "- **使用固定大小的 chunk 进行存储**，以保证数据格式的统一性，同时优化 GPU 计算效率。\n", "- **支持不同的数据类型（dtype）**，确保 token 数据占用最少的存储空间。\n", "\n", " `prepare_full()` 函数里，我们创建了多个 `PackedDatasetBuilder` 实例：\n", "```python\n", "builder = packed_dataset.PackedDatasetBuilder(\n", "    outdir=destination_path,\n", "    prefix=set_name+\"_\"+f\"process{i}\",\n", "    chunk_size=chunk_size,\n", "    sep_token=tokenizer.pad_token_id,  # 这里是填充 token\n", "    dtype=\"auto\",\n", "    vocab_size=len(tokenizer),\n", ")\n", "```\n", "\n", "| 参数 | 作用 |\n", "|------|------|\n", "| `outdir` | 存储输出数据的目录 |\n", "| `prefix` | 输出文件的前缀，用于区分不同数据集 |\n", "| `chunk_size` | **一个数据块的大小**，影响存储效率和训练加载速度 |\n", "| `sep_token` | **填充 token**，用于填充序列以对齐 batch |\n", "| `dtype` | **数据类型**（如 `int32` 或 `uint16`），可减少存储占用 |\n", "| `vocab_size` | **词汇表大小**，用于确定 token 存储的 bit 位 "]}, {"cell_type": "markdown", "id": "a79abe9a-e6e8-4c54-90e3-3aa8550f9043", "metadata": {}, "source": ["#### 7.2.2 如何选择合适的并行方式？"]}, {"cell_type": "markdown", "id": "49f5353b-33d3-4744-bb40-a2118e64f601", "metadata": {}, "source": ["| **任务类型** | **文件类型** | **适合的并行方式** | **推荐的并行数（`np`）** | **优化建议** |\n", "|--------------|--------------|-----------------|-------------------|--------------|\n", "| **计算密集型任务**（如 Tokenization、模型推理、大量数学计算） | **单个大 JSONL 文件**（> 10GB） | **进程并行 (`multiprocessing`)** | **50% - 75% 的 CPU 物理核心数**（如 16 核 CPU，用 `np=8~12`） | **避免 `np` 过大导致 CPU 过载**，适当调整 batch 处理 |\n", "| **计算密集型任务** | **多个小 JSONL 文件**（每个 < 100MB） | **进程并行 (`multiprocessing`)** | **CPU 核心数（如 16 核 CPU，用 `np=16`）** | **分批处理 JSONL，动态分配进程以提高吞吐量** |\n", "| **I/O 密集型任务**（如 文件读取、日志分析、数据预处理） | **单个大 JSONL 文件**（> 10GB） | **线程并行 (`threading`)** | **1.5x - 2x 的 CPU 逻辑线程数（如 16 核 32 线程，可用 `np=48~64`）** | **使用 `asyncio` 或 `aiofiles` 进一步优化并发** |\n", "| **I/O 密集型任务** | **多个小 JSONL 文件**（每个 < 100MB） | **线程并行 (`threading`)** | **逻辑线程数（如 16 核 32 线程，可用 `np=32`）** | **确保磁盘 I/O 不成为瓶颈，观察 `iostat -x 1`** |\n", "| **混合任务（计算 + I/O）**（如 读取 JSONL + Tokenizer） | **单个大 JSONL 文件** | **进程并行 (`multiprocessing`) + I/O 预加载** | **CPU 物理核心数（如 16 核 CPU，用 `np=8~12`）** | **使用 `cache_lines_num` 控制 I/O 频率，避免磁盘过载** |\n", "| **混合任务（计算 + I/O）** | **多个小 JSONL 文件** | **进程并行 (`multiprocessing`)** | **CPU 物理核心数（如 `np=16`）** | **预分配文件给进程，减少进程间通信开销** |"]}, {"cell_type": "markdown", "id": "52e3ff53-63ec-4802-8c12-e617fe41a780", "metadata": {}, "source": ["- **什么是I/O限制**？"]}, {"cell_type": "markdown", "id": "8e6b6751-c4b5-4bb5-8687-43fe006b14d2", "metadata": {}, "source": ["I/O 限制（I/O Bottleneck）指的是 输入/输出（Input/Output）操作的速度成为系统的瓶颈，导致 CPU 或 GPU 计算资源无法充分利用。在数据处理任务（比如咱们在 step3 里转换 .jsonl 为 .bin）时，数据需要从磁盘读取、处理后再写入磁盘，如果磁盘 I/O 速度跟不上 CPU 计算速度，那么 I/O 就会成为整个任务的瓶颈。"]}, {"cell_type": "markdown", "id": "d5b64c45-1a1c-487a-9dcc-6ecefee325ac", "metadata": {}, "source": ["- **既然数据处理是I/O密集型任务，为什么无论是data-jucier还是tokenizer这些流程，都使用multiprocessing而没有使用threading**？"]}, {"cell_type": "markdown", "id": "8431ce5a-2223-4291-87eb-840d5a7375f2", "metadata": {}, "source": ["在运行python代码的时候、Python 的全局解释器锁（GIL）会限制`threading`的性能。Python 解释器 允许多个线程存在，但 同一时间只有一个线程在运行 Python 计算（即使你有 64 核 CPU），threading 适用于 I/O 任务（如网络请求、数据库查询），但 如果涉及到 CPU 计算（如 Tokenization），threading 可能会被 GIL 限制，导致并行效果很差！\n", "\n", "无论是datajucier还是tokenizer过程、我们都可能涉及到CPU上的运算（比如说字节对编码BPE、哈希值计算等等），这些都会受到GIL限制，如果我们使用的是线程并行、多线程会互相等待、无法真正并行运行Tokenization和各类计算。因此multiprocessing在实际大模型运行场景中会更适合作为数据清洗流程中的并行方式。"]}, {"cell_type": "markdown", "id": "456ece8f-6843-4be2-baca-35418ccca1e5", "metadata": {}, "source": ["#### 7.2.3 Prefix-Suffix-Middle数据掩码"]}, {"cell_type": "markdown", "id": "ba34e569-10cd-4790-950f-a3d0728be1ee", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/155.png)"]}, {"cell_type": "markdown", "id": "515ba3bd-920a-49cf-ab1f-8a43dce65366", "metadata": {}, "source": ["根据Deepseek的论文所描述，**DeepSeekCoder-V2** 使用了一种 **\"Fill-in-Middle (FIM)\"** 的训练方法，这是一种 **填充中间的预测策略**。这种方法不影响传统的 **“预测下一个 Token”（Next Token Prediction, NTP）** 任务，同时让模型能够更好地**理解上下文信息，并预测文本的中间部分**。\n", "\n", "| 训练方式 | 预测方式 | 适用任务 |\n", "|----------|------------|------------|\n", "| **Autoregressive (自回归模型)** | 仅预测下一个 token | 适用于 GPT 训练 |\n", "| **FIM (Fill-in-Middle)** | 预测文本的中间部分 | 适用于代码补全、上下文填充 |\n", "\n", "- **什么是 Prefix-Suffix-Middle (PSM) 结构？**\n", "  \n", "DeepSeekCoder-V2 采用了 **Prefix-Suffix-Middle (PSM)** 结构来组织数据。这种结构将一个完整的文本 **拆分成三部分**：\n", "\n", "1️⃣ **Prefix (前缀部分，`f_pre`)**  \n", "2️⃣ **Suffix (后缀部分，`f_suf`)**  \n", "3️⃣ **Middle (中间部分，`f_middle`)**（需要模型预测的部分）\n", "\n", "论文中表述的方式是 ↓\n", "```\n", "<|fim_begin|> f_pre <|fim_hole|> f_suf <|fim_end|> f_middle <|eos_token|>\n", "```\n", "- `<|fim_begin|>` → 表示 **填充任务的开始**\n", "- `f_pre` → **前缀**（上下文信息）\n", "- `<|fim_hole|>` → **填充位置**\n", "- `f_suf` → **后缀**（提供部分上下文）\n", "- `<|fim_end|>` → **结束填充**\n", "- `f_middle` → **模型需要预测的文本部分**\n", "- `<|eos_token|>` → **结束符**\n", "\n", "\n", "假设我们有一个完整的句子：\n", "> **\"今天的天气很好，我们一起去公园散步吧！\"**\n", "\n", "如果采用 **Prefix-Suffix-Middle (PSM) 结构**，可能会拆分为：\n", "- **前缀（Prefix, `f_pre`）：** `\"今天的天气很好，\"`\n", "- **后缀（Suffix, `f_suf`）：** `\"去公园散步吧！\"`\n", "- **中间部分（Middle, `f_middle`，模型需要预测）：** `\"我们一起\"`\n", "\n", "转换为 PSM 结构：\n", "```\n", "<|fim_begin|> 今天的天气很好， <|fim_hole|> 去公园散步吧！ <|fim_end|> 我们一起 <|eos_token|>\n", "```\n", "\n", "模型在训练时，需要根据 `f_pre`（前缀）和 `f_suf`（后缀）来预测 `f_middle`（中间部分），因此如果要执行PSM、则是需要对数据先进行各个部分的标注、再交换顺序、再使用掩码将`f_middle`部分遮盖起来、还要生成能够与`f_middle`相匹配的标签，实际执行难度远远高于论文中简短的几句话。但是、这一定是一个对代码训练非常有效的方式，如果你的模型需要说某种特定的低资源语言、或者是模型核心负责写代码、那这个方式你一定要想办法实现。"]}, {"cell_type": "markdown", "id": "5ebc4122-810d-4988-be03-8f7f2641de0c", "metadata": {}, "source": ["## 8 数据处理第四步：将数据按照神经网络需求标准化"]}, {"cell_type": "markdown", "id": "132947b2-7692-4469-b1d9-872888a343cb", "metadata": {}, "source": ["当我们有了完整的bin文件之后、整个数据处理的流程就只剩下最后一步：将数据通过PretrainDataset转变成能够与神经网络兼容的格式。这一步其实已经不算是数据预处理、而是常规的神经网络训练前的流程了。\n", "\n", "```\n", "【step 1】📂 raw data (PDF/HTML/TXT)\n", "            ↓  (转换)\n", "【step 2】📂 JSONL {\"text\": \"...\"}\n", "            ↓  (清洗)\n", "          📂 cleaned JSONL\n", "            ↓  (质量控制)\n", "【step 3】📂 filtered JSONL\n", "            ↓  (tokenizer)\n", "          📂 Tokenized JSONL {\"tokens\": [...]}\n", "            ↓  (拼接)\n", "【step 4】📂 Pretrain Data (bin/lmdb)\n", "            ↓  (匹配pytorch + deepspeed规则)\n", "          📂 能够索引的token\n", "            ↓  (匹配神经网络所需结构)\n", "          📂 分好批次的3d数据\n", "```\n", "\n", "这段代码位于`tokenizer`文件夹下的`step4_dataset.py`中、其核心功能包括：\n", "\n", "1. **实现数据跨类别打乱（shuffle）、解决数据分布不均的问题**，确保dataloader读取的数据顺序是随机的。\n", "\n", "2. **封装 \\_\\_getitem\\__，适配 PyTorch/DeepSpeed 训练**，PretrainDataset 继承 torch.utils.data.Dataset，可以 直接被 Dataloader 使用，支持索引访问，dataset[i] 可以直接返回 (X, Y)，让 PyTorch 训练框架能高效加载数据，支持 多进程数据加载，为分布式做准备。\n", "\n", "3. **使用memmap进行高效数据读取、节省内存**，通过np.memmap映射.bin文件到内存、避免一次性加载所有数据、还能提高I/O交换速度\n", "\n", "4. **构造符合神经网络需求的X,Y训练样本**，需要兼容 DeepSeek 采用的 Causal Language Modeling（CLM）+ Multi-Token Prediction 训练方式，如果有更复杂的预测流程则还需要兼容其他预测流程。"]}, {"cell_type": "markdown", "id": "91db0310-8bfb-4bdc-9500-3a36ffa61cb4", "metadata": {}, "source": ["这一步的代码将与预训练的`pretrain.py`一同使用、因此无需单独的shell脚本。具体的代码如下 ↓"]}, {"cell_type": "markdown", "id": "eb569840-f091-497e-a811-85ab41c5a2cc", "metadata": {}, "source": ["```python\n", "\n", "import os\n", "import numpy as np\n", "import torch\n", "from torch.utils.data import Dataset\n", "import random\n", "\n", "##############################\n", "###  预训练数据集类 (PretrainDataset) ###\n", "##############################\n", "class PretrainDataset(Dataset): \n", "    def __init__(self, data_path_lst, max_length=512, memmap=False, shuffle=True):\n", "        \"\"\"\n", "        负责加载预训练数据集，并支持 memory-mapped 文件读取方式。\n", "\n", "        Args:\n", "            data_path_lst (list): 存储所有 .bin 预处理数据文件路径的列表\n", "            max_length (int): 每个样本的最大长度（token 数量）\n", "            memmap (bool): 是否使用 memory-mapped 方式读取数据，节省内存\n", "            shuffle (bool): 是否对数据进行 shuffle 以增强训练效果\n", "        \"\"\"\n", "        super().__init__()\n", "        self.data_path_lst = data_path_lst\n", "        self.max_length = max_length\n", "        self.memmap = memmap\n", "        self.shuffle = shuffle\n", "        self.dtype = np.dtype('uint16')  # ✅ 设定数据类型\n", "\n", "        # 存储文件偏移信息\n", "        self.file_offsets = []\n", "        self.total_samples = 0\n", "\n", "        ####################################\n", "        ###  遍历所有 .bin 文件，计算样本数 ###\n", "        ####################################\n", "        for file_path in self.data_path_lst:\n", "            file_size = os.path.getsize(file_path)  # 获取文件大小（字节）\n", "            num_samples = (file_size // self.dtype.itemsize) // max_length  # 计算该文件中包含多少个样本\n", "            self.file_offsets.append((file_path, self.total_samples, num_samples))  # 记录文件偏移信息\n", "            self.total_samples += num_samples  # 更新数据集中总样本数\n", "\n", "        ####################################\n", "        ###  如果使用 memmap，打开所有文件 ###\n", "        ####################################\n", "        if self.memmap:\n", "            self.mmaps = [np.memmap(file, dtype=self.dtype, mode='r') for file, _, _ in self.file_offsets]\n", "        else:\n", "            self.mmaps = None  # 非 memmap 模式\n", "\n", "        ####################################\n", "        ###  生成样本索引，并进行 Shuffle ###\n", "        ####################################\n", "        self.indices = list(range(self.total_samples))  # 生成样本索引\n", "        if self.shuffle:\n", "            random.shuffle(self.indices)  # ✅ 确保数据是随机的，避免训练时的样本偏差\n", "\n", "        print(f\"Loaded {len(self.file_offsets)} files, total samples: {self.total_samples}\")\n", "\n", "    ###########################\n", "    ###  获取数据集的总样本数 ###\n", "    ###########################\n", "    def __len__(self):\n", "        return self.total_samples\n", "\n", "    ############################\n", "    ###  根据索引获取数据样本 ###\n", "    ###########################\n", "    def __getitem__(self, index):\n", "        \"\"\"\n", "        通过索引找到对应的 .bin 文件，并返回该索引对应的样本数据。\n", "\n", "        Args:\n", "            index (int): 样本索引\n", "\n", "        Returns:\n", "            X (torch.Tensor): 输入 token 序列\n", "            Y (torch.Tensor): 目标 token 序列（用于预测下一个 token）\n", "        \"\"\"\n", "        index = self.indices[index]  # ✅ 使用 Shuffle 过的索引，保证训练数据顺序是随机的\n", "\n", "        ################################\n", "        ###  找到索引所在的 .bin 文件 ###\n", "        ###############################\n", "        for file_idx, (file_path, start_idx, num_samples) in enumerate(self.file_offsets):\n", "            if index < start_idx + num_samples:\n", "                local_index = index - start_idx  # 计算该索引在该文件中的位置\n", "                break\n", "        else:\n", "            raise IndexError(\"Index out of range\")  # ✅ 确保索引不会超出数据范围\n", "\n", "        ##############################\n", "        ###  从文件中读取数据       ###\n", "        ##############################\n", "        if self.memmap:\n", "            # ✅ 使用 memory-mapped 方式读取数据，避免频繁 IO\n", "            sample = self.mmaps[file_idx][local_index * self.max_length : (local_index + 1) * self.max_length]\n", "        else:\n", "            # ✅ 直接从二进制文件读取数据（非 memmap 模式）\n", "            with open(file_path, 'rb') as f:\n", "                f.seek(local_index * self.max_length * self.dtype.itemsize)  # 定位到正确的样本位置\n", "                sample = np.frombuffer(f.read(self.max_length * self.dtype.itemsize), dtype=self.dtype)\n", "\n", "        ##############################\n", "        ###  生成 X 和 Y 作为训练输入/目标 ###\n", "        ##############################\n", "        X = np.array(sample[:-1], dtype=np.int64)  # 输入序列（去掉最后一个 token）\n", "        Y = np.array(sample[1:], dtype=np.int64)  # 目标序列（向右移动一位）\n", "\n", "        return torch.from_numpy(X), torch.from_numpy(Y) \n", "```"]}, {"cell_type": "markdown", "id": "4cebe28e-1838-46ae-92f7-8aa4e4bf0818", "metadata": {}, "source": ["## 9 DeepSeekv3的预训练"]}, {"cell_type": "markdown", "id": "b63b9b91-a5fa-4725-808b-790e3d99d2b7", "metadata": {}, "source": ["### 9.1 预训练执行脚本"]}, {"cell_type": "markdown", "id": "29b9a153-8c29-43ff-8822-530d33a60dab", "metadata": {}, "source": ["- **deepseekmini预训练数据集**\n", "  \n", "链接: https://pan.baidu.com/s/1-p4R2wGkgDbpwhTlwvLpVg?pwd=5v3z\n", "提取码: 5v3z "]}, {"cell_type": "markdown", "id": "0abb327e-d7cd-4e4d-b845-9b8e18d2b212", "metadata": {}, "source": ["- **全部文件结构如下**\n", "```python\n", "minideepseek/v3\n", "├── requirements_minids.txt      # 预训练所需的环境\n", "├── test_v3model.py              # 测试deepseev3_mtp_model.py能否跑通\n", "├── logger_utils.txt             # 进行logger控制和调试的代码\n", "├── pretrain.py                  # 预训练代码\n", "│\n", "├── model/                       # model文件夹、存放模型文件\n", "│   ├── config.py\n", "│   ├── convert.py\n", "│   ├── deepseev3_mtp_model.py   # 要训练的模型脚本，加上了mtp\n", "│   ├── fp8_cast_bf16.py\n", "│   ├── kernel.py\n", "│\n", "├── data/                        # data文件夹\n", "│   ├── print_datas.py           # 对下载好的数据进行状态打印的脚本\n", "│   ├── analyze_jsonl.py         # 查询各jsonl文件统计指标的脚本\n", "│   ├── data-juicer-main.zip     # data-juicer库安装所需文件\n", "│   ├── delete_files.sh          # 删除数据、降低数据大小的脚本\n", "│   ├── split_data.py            # 进行数据分割的python脚本 \n", "│   ├── hfd_revised.sh           # 分布式并行数据下载脚本（防429报错版）\n", "│   ├── slimpajama_preclean.py   # slimpajama清理脚本\n", "│   ├── pdfprocess.py            # pdf批量处理流程\n", "│   ├── step1_pretrain_basic_dataprocess.py   # 数据处理第一步：JSONL化脚本\n", "│   ├── data_jucier/             # 在step2做所使用的数据处理工具\n", "│   ├── final_data/              # 存放最终处理好的数据    \n", "│   ├── step2/                                # 数据处理第二步：数据清洗脚本\n", "│   │   ├── minidsv3_starcoder.yaml           # 各个数据自身的yaml文件\n", "│   │   ├── minidsv3_text_ape.yaml\n", "│   │   ├── minidsv3_text_openr1.yaml\n", "│   │   ├── minidsv3_text_skypile.yaml\n", "│   │   ├── minidsv3_text_slimpajama.yaml\n", "│   │   ├── requirements_step2.txt           # 第二步运行所需的环境\n", "│   │   ├── run_step2_ape.sh                 # 各个数据上运行DJ的sh脚本\n", "│   │   ├── run_step2_openr1.sh\n", "│   │   ├── run_step2_skypile.sh\n", "│   │   ├── run_step2_slimpajama.sh\n", "│   │   ├── run_step2_starcoder.sh\n", "│\n", "├── tokenizer/                  # tokenizer文件夹\n", "│   ├── lit_gpt/                # 辅助打包的库lit_gpt\n", "│   ├── test_tokenizer.py       # 测试tokenizer是否正常运行的代码\n", "│   ├── tokenizer.json          # deepseek开源的tokenizer架构本身\n", "│   ├── tokenizer_config.json   # deepseek开源的tokenizer配置文件\n", "│   ├── step3_prepare_data_for_pretrain.py   # 数据处理第三步：Tokenizer脚本\n", "│   ├── step3_prepare_data_for_pretrain_upsample.py   # 上采样脚本\n", "│   ├── step4_dataset.py        # 将数据抽样、打包成bin文件的脚本\n", "\n", "```"]}, {"cell_type": "markdown", "id": "8221c62f-6029-4af1-8327-b05e3d2d2ab8", "metadata": {}, "source": ["其中，数据的目录为`/root/autodl-tmp/minideepseek/v3/data/final_data`，里面是已经处理好的bin文件 ↓"]}, {"cell_type": "markdown", "id": "6c47b036-1034-494f-abcb-4c5845075b23", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/153.png)"]}, {"cell_type": "markdown", "id": "3d429977-f2cb-4c43-b867-05610aaf5314", "metadata": {}, "source": ["具体执行脚本为 ↓"]}, {"cell_type": "markdown", "id": "510d2da7-7697-4720-ae62-3e04be9e1492", "metadata": {}, "source": ["```shell\n", "# 退出当前环境\n", "deactivate\n", "\n", "# 激活mnds_training环境\n", "cd /root/autodl-tmp/minideepseek/v3\n", "source mnds_training/bin/activate\n", "\n", "###################################################\n", "####   4 x 64 vCPU Intel(R) Xeon(R) Gold 6430  ####\n", "####   RAM 4 x 120G，4 x RTX4090 24G           ####\n", "###################################################\n", "\n", "# 改变deepspeed版本以支持zero-1数据并行\n", "mkdir -p ~/.triton/autotune\n", "pip install deepspeed==0.16.1\n", "\n", "# 使用 DeepSpeed 分布式训练，4张GPU并行、单CPU上64线程并行、执行 10个 epoch\n", "# 【TIME WARNING：运行中后续补充】\n", "deepspeed --master_port 29500 --num_gpus=4 pretrain.py --epochs 10\n", "```"]}, {"cell_type": "markdown", "id": "9a1e55c7-1f33-4ed5-9eed-13b37691355e", "metadata": {}, "source": ["| **模型规模**         | **预估训练时间** |\n", "|----------------------|--------------------------------------|\n", "| **0.02B参数 + 30G数据 MateConvMini**    | 15个epoch，约18~22个小时，90分钟/epoch |\n", "| **0.15B参数 + 60G数据 MiniDeepSeek**   | 15个epoch，约4天时间，6~7小时/epoch |\n", "| **0.3B参数 + 100G数据 MiniDeepSeek**   | 10个epoch，约1周时间，14小时/epoch|\n", "| **0.3B参数 + 300G数据 MiniDeepSeek**   | 10个epoch，约2周时间，30小时/epoch|\n", "\n", "数据量直接关系到一个epoch中有多少需要处理的数据、因此数据量增加会让模型的训练时间线性增加。模型规模的增加就复杂得多，一般认为模型规模增加之后、训练时间不会线性增加、而会指数级 + 线性混合增加，因此模型变大之后所需的时间会爆炸性增长。"]}, {"cell_type": "markdown", "id": "2ec09820-06c6-4f16-a71c-1f121be66214", "metadata": {}, "source": ["### 9.2 预训练脚本解读"]}, {"cell_type": "markdown", "id": "74e9251e-6eaa-4f8e-ab0f-14a3aab1bb87", "metadata": {}, "source": ["DeepSeek预训练脚本是由MateConv预训练脚本修改而来、其核心板块与MateConv预训练脚本基本一致、包括下列功能：\n", "\n", " - `Logger()` → 负责日志记录\n", " - `get_lr()` → 计算动态学习率\n", " - `init_model()` → 初始化模型（支持断点续训）\n", " - `train_epoch()` → 训练单个 epoch\n", " - if \\_\\_main_\\_\n", " - 单独的Dataset.py和model.py用于支持模型和数据的处理\n", "\n", "单除此之外、DeepSeek预训练脚本还增加和删除了这些板块——\n", "\n", "1. **修改原本的DDP支持为DeepSpeed的ZeRO-1数据并行、并且将自定义并行结构全部纳入DeepSpeed框架进行考虑**，这是由于DeepSeek所使用的专家并行、Tensor并行、DualPipe等流程无法与传统的数据并行兼容、因此DeepSeek在论文中明确说明他们只使用了ZeRO-1阶段数据并行，故而在DeepSeek的预训练代码中、数据加载时也不再由pytorch的`Distributed Sampler`来控制数据读取、分布式初始化状态也不再由`torch.distributed`控制，而是交由DeepSpeed直接管理数据并行与分布式初始化。\n", "\n", "2. **补充DeepSeek本身要求的Multi-Token Prediction功能、为模型增加更多“远见”、以增加模型对长序列的预测能力**。为此、模型结构做出变化、计算的损失函数也发生了变化，deepseek的pretrain.py拥有单独的MTP损失计算函数以及单独的MTP损失环节。\n", "\n", "除此之外、当前的训练流程适合**数据规模大、模型复杂度高（MTP + MoE）**，同时多卡训练、显存敏感，为了更好应对分布式的需求，在全新的训练代码中，我做出了这些改进 ↓\n", "\n", "1. 训练稳定性（尤其是显存爆炸 / loss 爆炸问题）  \n", "2. 多卡分布式兼容性  \n", "3. DeepSpeed ZeRO 注册的正确性  \n", "4. 日志与调试能力  \n", "5. batch / micro_batch 的配置方式  \n", "\n", "具体来说 ↓\n", "\n", "| 方面 | MateConv训练代码 | minideepseek训练代码 |\n", "|------|------------------|------------------|\n", "| 🔁 `batch_size` 设计 | `args.batch_size`（全局） | `micro_batch_size + accumulation_steps` |\n", "| 💥 ZeRO参数注册 | ❌ 没有 | ✅ 完整注册所有手动实现的并行层 |\n", "| 💾 `load_state_dict` 处理 | 简单载入 | 自动 clean `module.` 前缀，避免多卡恢复冲突 |\n", "| 🚨 loss debug/grad 检查 | ❌ 没有 | ✅ `grad_fn` 检查、NaN/Inf 检查、loss 拆解输出 |\n", "| 🧠 内存调试 | ❌ 无 | ✅ `print_memory()` 跟踪每阶段内存 |\n", "| 🔎 logger 机制 | `print` | ✅ `logging` + `logger_utils` 支持调试/切换日志级别 |\n", "| 🔁 checkpoint 自动恢复 | 有，但无优化器注册 | ✅ 模型 & 优化器恢复都包含 |\n", "| 🔁 `train_batch_size` 参数 | `args.batch_size`（全局） | 分离 micro_batch / accumulation 更灵活 |\n", "| ⚙️ 设备设置 / 清理 | 较粗糙 | ✅ 启动前清理 GPU 释放显存 |\n", "| 🧮 loss all-reduce | ❌ 没有 | ✅ 使用 `reduce_loss_across_gpus` 跨卡同步 |\n", "| 🧵 DataLoader 设置 | num_worker 较低 | ✅ `num_worker=64` + `prefetch_factor=8` |"]}, {"cell_type": "markdown", "id": "af8576fe-4362-46f9-8366-1c6c49abb223", "metadata": {}, "source": ["具体脚本如下 ↓"]}, {"cell_type": "markdown", "id": "87800c41-b32c-476f-b47d-799152cadcdd", "metadata": {}, "source": ["```python\n", "import os\n", "import gc\n", "import platform\n", "import argparse\n", "import time\n", "import math\n", "import warnings\n", "import glob\n", "import json\n", "import deepspeed\n", "import torch\n", "import torch.distributed as dist\n", "import torch.nn.functional as F\n", "from torch import optim\n", "from torch.optim.lr_scheduler import CosineAnnealingLR\n", "from torch.utils.data import DataLoader\n", "from contextlib import nullcontext\n", "from model.deepseekv3_mtp_model import Transformer, ColumnParallelLinear, RowParallelLinear\n", "from model.deepseekv3_mtp_model import ModelArgs\n", "from tokenizer.step4_dataset import PretrainDataset\n", "from pathlib import Path\n", "from model.deepseekv3_mtp_model import get_rank, get_world_size\n", "from logger_utils import setup_logger\n", "import logging\n", "\n", "torch.autograd.set_detect_anomaly(True)\n", "\n", "logger = setup_logger()\n", "\n", "#不需要调试时用这一行\n", "logger.setLevel(logging.INFO)\n", "\n", "#需要调试时启用下面这一行\n", "#logger.setLevel(logging.DEBUG)\n", "\n", "\n", "logger.info(\"🚀 Logger initialized\")\n", "\n", "os.environ[\"PYTORCH_CUDA_ALLOC_CONF\"] = \"max_split_size_mb:128,expandable_segments:True\"\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "def create_ds_config():\n", "    # ds_config动态生成函数\n", "    ds_config = {\n", "        \"gradient_accumulation_steps\": args.accumulation_steps,\n", "        \"train_micro_batch_size_per_gpu\": args.micro_batch_size,\n", "        \"bf16\": {\"enabled\": True},\n", "        \"zero_optimization\": {\"stage\": 1},  # ✅ 启用 ZeRO-1\n", "        \"optimizer\": {\n", "            \"type\": \"AdamW\",\n", "            \"params\": {\n", "                \"lr\": args.learning_rate,\n", "                \"betas\": [0.9, 0.999],\n", "                \"eps\": 1e-8,\n", "                \"weight_decay\": 0.01\n", "            }\n", "        },\n", "        \"scheduler\": {\n", "            \"type\": \"WarmupLR\",\n", "            \"params\": {\n", "                \"warmup_min_lr\": 0,\n", "                \"warmup_max_lr\": args.learning_rate,\n", "                \"warmup_num_steps\": 10000\n", "            }\n", "        }\n", "    }\n", "\n", "    # ✅ 将 JSON 数据写入文件\n", "    config_path = os.path.join(args.save_dir, \"ds_config.json\")\n", "    with open(config_path, \"w\") as f:\n", "        json.dump(ds_config, f, indent=4) \n", "\n", "    return config_path\n", "\n", "def print_memory(tag=\"\"):\n", "    logger.debug(f\"\\n📌 CUDA Memory Status {tag}:\")\n", "    logger.debug(f\"   Allocated: {torch.cuda.memory_allocated() / 1024 ** 2:.2f} MB\")\n", "    logger.debug(f\"   Reserved:  {torch.cuda.memory_reserved() / 1024 ** 2:.2f} MB\")\n", "    logger.debug(f\"   Max Allocated: {torch.cuda.max_memory_allocated() / 1024 ** 2:.2f} MB\")\n", "    logger.debug(f\"   Max Reserved:  {torch.cuda.max_memory_reserved() / 1024 ** 2:.2f} MB\\n\")\n", "\n", "def Logger_main_rank(msg):\n", "    # ✅ 让 DeepSpeed 处理 rank\n", "    rank = int(os.environ.get(\"RANK\", \"0\"))  \n", "    if rank == 0:\n", "        logger.info(msg)\n", "\n", "def get_lr(it, all):\n", "    warmup_iters = args.warmup_iters\n", "    lr_decay_iters = all\n", "    min_lr = args.learning_rate / 10\n", "\n", "    if it < warmup_iters:\n", "        return args.learning_rate * it / warmup_iters\n", "    if it > lr_decay_iters:\n", "        return min_lr\n", "    decay_ratio = (it - warmup_iters) / (lr_decay_iters - warmup_iters)\n", "    assert 0 <= decay_ratio <= 1\n", "    coeff = 0.5 * (1.0 + math.cos(math.pi * decay_ratio))\n", "    return min_lr + coeff * (args.learning_rate - min_lr)\n", "\n", "def reduce_loss_across_gpus(loss):\n", "    \"\"\"对 loss 进行 all-reduce 并取平均，返回的是一个 detached 的 tensor，仅用于 log\"\"\"\n", "    if dist.is_available() and dist.is_initialized():\n", "        reduced_loss = loss.detach().clone()\n", "        dist.all_reduce(reduced_loss, op=dist.ReduceOp.SUM)\n", "        reduced_loss /= get_world_size()\n", "        return reduced_loss\n", "    else:\n", "        return loss\n", "\n", "def compute_loss(logits, mtp_logits, targets, lambda_mtp, vocab_size):\n", "    \"\"\"\n", "    计算 DeepSeek-V3 预训练的总损失 (L = L_main + L_MTP)\n", "\n", "    Args:\n", "        logits: [Bs, seq_len, vocab_size] —— 主模型输出\n", "        targets: [Bs, seq_len] —— 已偏移好的目标（dataset 中已是 input+1）\n", "        mtp_logits: list of [Bs, seq_len-k-1, vocab_size] —— 每个 k 的 MTP 输出\n", "        lambda_mtp: MTP 损失的权重因子 λ\n", "\n", "    Returns:\n", "        总损失 (scalar tensor)\n", "    \"\"\"\n", "\n", "    logger.debug(f\"[DEBUG] logits max: {logits.max().item()}, min: {logits.min().item()}\")\n", "    \n", "    for k, mtp_logit in enumerate(mtp_logits):\n", "        logger.debug(f\"[DEBUG] mtp_logits[{k}] max: {mtp_logit.max().item()}, min: {mtp_logit.min().item()}\")\n", "        \n", "        if torch.isnan(mtp_logit).any():\n", "            logger.error(f\"[NaN] ❌ mtp_logits[{k}] contains NaN!\")\n", "\n", "        if torch.isinf(mtp_logit).any():\n", "            logger.warning(f\"[Inf] ⚠️ mtp_logits[{k}] contains Inf!\")\n", "    \n", "    # ====== 主 loss ======\n", "    main_loss = F.cross_entropy(logits.reshape(-1, vocab_size), targets.reshape(-1))\n", "    logger.debug(f\"[DEBUG] main_loss: {main_loss.item()}, requires_grad: {main_loss.requires_grad}, grad_fn: {main_loss.grad_fn}\")\n", "\n", "    # ====== MTP loss ======\n", "    mtp_loss = None\n", "    for k, mtp_logit in enumerate(mtp_logits):\n", "        if targets.shape[1] <= k + 1:\n", "            continue\n", "\n", "        mtp_target = targets[:, k + 1:]\n", "        expected_len = mtp_logit.size(1)\n", "        mtp_target = mtp_target[:, :expected_len]\n", "\n", "        # 检查是否有 NaN 或非法值\n", "        if torch.isnan(mtp_logit).any():\n", "            logger.error(f\"[ERROR] NaN detected in mtp_logits[{k}]\")\n", "            continue\n", "        if mtp_target.max() >= vocab_size:\n", "            logger.error(f\"[ERROR] mtp_target contains token >= vocab_size at k={k}\")\n", "            continue\n", "        \n", "        this_loss = F.cross_entropy(mtp_logit.reshape(-1, vocab_size), mtp_target.reshape(-1))\n", "        logger.debug(f\"[DEBUG] mtp_loss[{k}]: {this_loss.item()}, requires_grad: {this_loss.requires_grad}, grad_fn: {this_loss.grad_fn}\")\n", "\n", "        if mtp_loss is None:\n", "            mtp_loss = this_loss\n", "        else:\n", "            mtp_loss = mtp_loss + this_loss\n", "\n", "    if mtp_loss is not None:\n", "        mtp_loss = (lambda_mtp / len(mtp_logits)) * mtp_loss\n", "    else:\n", "        mtp_loss = torch.tensor(0.0, device=main_loss.device, dtype=main_loss.dtype)\n", "    logger.debug(f\"[DEBUG] final mtp_loss: {mtp_loss.item()}, requires_grad: {mtp_loss.requires_grad}, grad_fn: {mtp_loss.grad_fn}\")\n", "\n", "    total_loss = main_loss + mtp_loss\n", "    logger.debug(f\"[DEBUG] total_loss: {total_loss.item()}, requires_grad: {total_loss.requires_grad}, grad_fn: {total_loss.grad_fn}\")\n", "\n", "    return total_loss\n", "\n", "def train_epoch(epoch, wandb):\n", "    epoch_start_time = time.time()\n", "    for step, (X, Y) in enumerate(train_loader):\n", "        step_start_time = time.time()\n", "        X = X.to(args.device)\n", "        Y = Y.to(args.device)\n", "\n", "        lr = get_lr(epoch * iter_per_epoch + step, args.epochs * iter_per_epoch)\n", "        for param_group in optimizer.param_groups:\n", "            param_group['lr'] = lr\n", "\n", "        with ctx:\n", "            #logger.debug(\"=\"*20 + \" 当前张量显存信息 \" + \"=\"*20)\n", "            #print_memory(\"Before forward\")\n", "            forward_start = time.time()\n", "            logits, mtp_logits = model(X,Y)\n", "            logger.debug(f\"logits grad check: requires_grad = {logits.requires_grad} | grad_fn = {logits.grad_fn}\")\n", "            logger.debug(f\"mtp_logits grad check: requires_grad = {mtp_logits[0].requires_grad} | grad_fn = {mtp_logits[0].grad_fn}\")\n", "            #print_memory(\"After forward\")\n", "            if torch.isnan(logits).any():\n", "                logger.debug(\"🚨 [NaN DETECTED] logits contains NaN\")\n", "            else:\n", "                logger.debug(\"✅ logits looks OK\")\n", "            forward_end_1 = time.time()\n", "            \n", "            for i, mtp in enumerate(mtp_logits):\n", "                if torch.isnan(mtp).any():\n", "                    logger.debug(f\"🚨 [NaN DETECTED] mtp_logits[{i}] contains NaN\")\n", "                else:\n", "                    logger.debug(f\"✅ mtp_logits[{i}] looks OK\")\n", "            \n", "            loss = compute_loss(logits, mtp_logits, Y\n", "                                , args.lambda_mtp, lm_config.vocab_size) / args.accumulation_steps\n", "            avg_loss = reduce_loss_across_gpus(loss)\n", "            logger.debug(f\"LOSS grad check: requires_grad = {loss.requires_grad} | grad_fn = {loss.grad_fn}\")\n", "            forward_end_2 = time.time()\n", "                            \n", "            print_memory(\"After loss\")\n", "            \n", "        with torch.autograd.set_detect_anomaly(True):\n", "            scaler.scale(loss).backward()\n", "            backward_end = time.time()\n", "            \n", "        del logits, Y\n", "\n", "        if (step + 1) % args.accumulation_steps == 0:\n", "            scaler.unscale_(optimizer)\n", "            torch.nn.utils.clip_grad_norm_(model.parameters(), args.grad_clip)\n", "\n", "            scaler.step(optimizer)\n", "            scaler.update()\n", "\n", "            optimizer.zero_grad(set_to_none=True)\n", "        \n", "        update_end = time.time()\n", "        \n", "        if step % args.log_interval == 0:\n", "            current_time = time.time()\n", "            epoch_spend_time = current_time - epoch_start_time\n", "\n", "            if step > 100:\n", "                # ✅ 新增：估算剩余 epoch 时间\n", "                avg_time_per_step = epoch_spend_time / (step + 1)\n", "                remaining_steps = iter_per_epoch - step - 1\n", "                estimated_remaining_time = avg_time_per_step * remaining_steps\n", "                estimated_remaining_min = estimated_remaining_time / 60\n", "            else:\n", "                estimated_remaining_min = -1 #暂不估计\n", "\n", "            # ✅ 打印进度\n", "            Logger_main_rank(\n", "                f\"Epoch:[{epoch}/{args.epochs}]({step}/{iter_per_epoch}) \"\n", "                f\"loss:{avg_loss.item() * args.accumulation_steps:.3f} \"\n", "                f\"lr:{optimizer.param_groups[-1]['lr']:.7f} \"\n", "                f\"epoch_RemainingTime:{estimated_remaining_min / 60:.1f}min\"\n", "            )\n", "\n", "            # ✅ 可选：记录 wandb\n", "            if (wandb is not None) and (os.environ.get(\"RANK\", \"0\") == \"0\"):\n", "                wandb.log({\n", "                    \"loss\": loss.item() * args.accumulation_steps,\n", "                    \"lr\": optimizer.param_groups[-1]['lr'],\n", "                    \"epoch_RemainingTime(min)\": estimated_remaining_time / 60\n", "                })\n", "                \n", "        if (step + 1) % args.save_interval == 0 and (os.environ.get(\"RANK\", \"0\") == \"0\"):\n", "            model.eval()\n", "            moe_path = '_moe' if lm_config.use_moe else ''\n", "            ckp = f'{args.save_dir}/{args.model_name}.pth'  # 使用用户提供的模型名称\n", "\n", "            if isinstance(model, torch.nn.parallel.DistributedDataParallel):\n", "                state_dict = model.module.state_dict()\n", "            else:\n", "                state_dict = model.state_dict()\n", "\n", "            torch.save(state_dict, ckp)\n", "            Logger_main_rank(f\"保存模型到 {ckp}\")\n", "            optimizer_state_path = f'{args.save_dir}/{args.model_name}_optimizer.pth'\n", "            torch.save(optimizer.state_dict(), optimizer_state_path)\n", "            Logger_main_rank(f\"保存优化器状态到 {optimizer_state_path}\")\n", "            \n", "            model.train()\n", "\n", "def init_model():\n", "    def count_parameters(model):\n", "        return sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "\n", "    model = Transformer(lm_config, logger=logger)\n", "    moe_path = '_moe' if lm_config.use_moe else ''\n", "\n", "    def clean_state_dict(state_dict):\n", "        return {k.replace(\"module.\", \"\"): v for k, v in state_dict.items()}\n", "        \n", "    # 加入恢复训练的逻辑\n", "    checkpoint_path = f'{args.save_dir}/{args.model_name}.pth'\n", "    if os.path.exists(checkpoint_path):\n", "        Logger_main_rank(f\"加载模型检查点 {checkpoint_path}\")\n", "        state_dict = torch.load(checkpoint_path, map_location=args.device)\n", "        model.load_state_dict(clean_state_dict(state_dict), strict=True)\n", "    else:\n", "        Logger_main_rank(f\"没有找到模型检查点，开始从头训练\")\n", "\n", "    # ✅ 注册所有你手动做了分布式的模块的参数\n", "    def register_external_parameters(model, optimizer):\n", "        raw_model = model.module if hasattr(model, \"module\") else model\n", "        optimizer = model.optimizer  # 拿到 ZeRO 优化器\n", "\n", "        def try_register(param):\n", "            try:\n", "                deepspeed.zero.register_external_parameter(optimizer,param)\n", "            except Exception as e:\n", "                Logger_main_rank(f\"⚠️ 注册参数失败: {e}\")              \n", "\n", "        # 注册 embedding 层\n", "        try_register(raw_model.embed.weight)\n", "\n", "        # 注册输出 head 层\n", "        try_register(raw_model.head.weight)\n", "\n", "        # 注册主模型 layers 中的 parallel linear\n", "        for layer in raw_model.layers:\n", "            for mod in layer.modules():\n", "                if isinstance(mod, (ColumnParallelLinear, RowParallelLinear)):\n", "                    try_register(mod.weight)\n", "                    if mod.bias is not None:\n", "                        try_register(mod.bias)\n", "                    if hasattr(mod, 'scale') and mod.scale is not None:\n", "                        try_register(mod.scale)\n", "\n", "        # 注册 MTP 中的 projection 和 layers\n", "        for proj in raw_model.mtp_projections:\n", "            for param in proj.parameters():\n", "                try_register(param)\n", "\n", "        for layer in raw_model.mtp_layers:\n", "            for mod in layer.modules():\n", "                if isinstance(mod, (ColumnParallelLinear, RowParallelLinear)):\n", "                    try_register(mod.weight)\n", "                    if mod.bias is not None:\n", "                        try_register(mod.bias)\n", "                    if hasattr(mod, 'scale') and mod.scale is not None:\n", "                        try_register(mod.scale)\n", "\n", "        # 注册 MoE 模块中的 gate 和 experts\n", "        for layer in raw_model.layers:\n", "            if hasattr(layer.ffn, 'gate'):\n", "                gate = layer.ffn.gate\n", "                try_register(gate.weight)\n", "                if gate.bias is not None:\n", "                    try_register(gate.bias)\n", "\n", "            if hasattr(layer.ffn, 'experts'):\n", "                for expert in layer.ffn.experts:\n", "                    if expert is not None:\n", "                        for param in expert.parameters():\n", "                            try_register(param)\n", "\n", "    # ✅ 使用 DeepSpeed ZeRO-1 数据并行\n", "    logger.debug(\"🚀 Initializing model with DeepSpeed...\")\n", "    Logger_main_rank(\"使用 DeepSpeed ZeRO-1\")\n", "    model, optimizer, _, _ = deepspeed.initialize(\n", "        model=model,\n", "        config=args.deepspeed,\n", "        model_parameters=model.parameters()\n", "    )\n", "    \n", "    # ✅ 执行注册\n", "    register_external_parameters(model, optimizer)\n", "    logger.debug(f\"NaN check passed on rank {get_rank()}\")\n", "    return model\n", "\n", "# torchrun --nproc_per_node 2 pretrain.py\n", "if __name__ == \"__main__\":\n", "    parser = argparse.ArgumentParser(description=\"DeepSeek Pretraining\")\n", "    parser.add_argument(\"--deepspeed\", type=str, default=None, help=\"DeepSpeed config file\")\n", "    parser.add_argument(\"--out_dir\", type=str, default=\"out\", help=\"Output directory\")\n", "    parser.add_argument(\"--epochs\", type=int, default=20, help=\"Number of epochs\")\n", "    parser.add_argument(\"--micro_batch_size\", type=int, default=2, help=\"Micro batch size per GPU\")\n", "    parser.add_argument(\"--accumulation_steps\", type=int, default=6, help=\"Gradient accumulation steps\")\n", "    parser.add_argument(\"--learning_rate\", type=float, default=2e-4, help=\"Learning rate\")\n", "    parser.add_argument(\"--device\", type=str, default=\"cuda:0\" if torch.cuda.is_available() else \"cpu\", help=\"Device to use\")\n", "    parser.add_argument(\"--dtype\", type=str, default=\"bfloat16\", help=\"Data type\")\n", "    parser.add_argument(\"--use_wandb\", action=\"store_true\", help=\"Use Weights & Biases\")\n", "    parser.add_argument(\"--wandb_project\", type=str, default=\"DeepSeek-Pretrain\", help=\"Weights & Biases project name\")\n", "    parser.add_argument(\"--num_workers\", type=int, default=64, help=\"Number of workers for data loading\")\n", "    parser.add_argument(\"--data_path\", type=str, default=\"/root/autodl-tmp/minideepseek/v3/data/final_data\", help=\"Path to training data\")\n", "    parser.add_argument(\"--grad_clip\", type=float, default=1.0, help=\"Gradient clipping threshold\")\n", "    parser.add_argument(\"--warmup_iters\", type=int, default=0, help=\"Number of warmup iterations\")\n", "    parser.add_argument(\"--log_interval\", type=int, default=100, help=\"Logging interval\")\n", "    parser.add_argument(\"--save_interval\", type=int, default=1000, help=\"Model saving interval\")\n", "    parser.add_argument(\"--model_name\", type=str, default=\"minideepseekbase\", help=\"模型名称，用于保存和加载检查点\")\n", "    # 新增关于MTP的相关参数lambda\n", "    parser.add_argument(\"--lambda_mtp\", type=float, default=0.5, help=\"MTP loss weight λ\")\n", "    parser.add_argument(\"--local_rank\", type=int, default=-1, help=\"Local rank for distributed training\")\n", "\n", "    args = parser.parse_args()\n", "\n", "    gc.collect()\n", "    torch.cuda.empty_cache()\n", "    torch.cuda.ipc_collect()\n", "\n", "    lm_config = ModelArgs()\n", "    max_seq_len = lm_config.max_seq_len\n", "    args.save_dir = os.path.join(args.out_dir)\n", "    \n", "    os.makedirs(args.save_dir, exist_ok=True)\n", "    os.makedirs(args.out_dir, exist_ok=True)\n", "    checkpoint_path = f'{args.save_dir}/{args.model_name}.pth'\n", "\n", "    # 动态创建DeepSpeed配置\n", "    ds_config_path = create_ds_config()\n", "    args.deepspeed = ds_config_path\n", "    \n", "    tokens_per_iter = args.micro_batch_size * max_seq_len\n", "    torch.manual_seed(1337)\n", "    device_type = \"cuda\" if \"cuda\" in args.device else \"cpu\"\n", "    args.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "    args.wandb_run_name = f\"minideepseek-Pretrain-Epoch-{args.epochs}-BatchSize-{args.micro_batch_size}-LearningRate-{args.learning_rate}\"\n", "\n", "    ctx = nullcontext() if device_type == \"cpu\" else torch.cuda.amp.autocast()\n", "    \n", "    if args.use_wandb and (os.environ.get(\"RANK\", \"0\") == \"0\"):\n", "        import wandb\n", "        wandb.init(project=args.wandb_project, name=args.wandb_run_name)\n", "    else:\n", "        wandb = None\n", "\n", "    data_path_list = sorted([str(p) for p in Path(args.data_path).glob(\"*.bin\")])\n", "    logger.debug(f\"🔍 Looking for .bin files in: {args.data_path}\")\n", "    logger.debug(f\"📦 Found {len(data_path_list)} bin files\")\n", "    for path in data_path_list[:5]:\n", "        logger.debug(f\"  - {path}\")\n", "    train_ds = PretrainDataset(data_path_list, max_length=max_seq_len, memmap=True)\n", "    #train_sampler = DistributedSampler(train_ds) if ddp else None\n", "    train_loader = DataLoader(\n", "        train_ds,\n", "        batch_size=args.micro_batch_size,\n", "        pin_memory=True,\n", "        drop_last=False,\n", "        shuffle=False, # 在step4里使用shuffle了\n", "        num_workers=args.num_workers,\n", "        prefetch_factor=8,  # 预取 8 个 batch\n", "        persistent_workers=True  # 让 worker 持续运行\n", "    )\n", "\n", "    model = init_model()\n", "    logger.debug(f\"[CHECK] head weight stats: max={model.head.weight.max().item()}, min={model.head.weight.min().item()}, mean={model.head.weight.mean().item()}\")\n", "    if hasattr(model.head, \"scale\") and model.head.scale is not None:\n", "        logger.debug(f\"[CHECK] head scale stats: max={model.head.scale.max().item()}, min={model.head.scale.min().item()}\")\n", "    for name, param in model.named_parameters():\n", "        if torch.isnan(param).any():\n", "            print(f\"⚠️ Parameter {name} contains NaN!\")\n", "    # torch 2.2.0版本不支持更智能的bf16自动混合精度训练，因此在设置中只能保留float16\n", "    # 但是由于我们是基于bf16进行训练，因此本质上AMP不会被触发\n", "    scaler = torch.cuda.amp.GradScaler(enabled=(args.dtype == 'float16'))\n", "    #scaler = torch.cuda.amp.GradScaler(enabled=(args.dtype in ['float16', 'bfloat16']))\n", "    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)\n", "    \n", "    # 恢复优化器状态\n", "    optimizer_state_path = f'{args.save_dir}/{args.model_name}_optimizer.pth'\n", "    if os.path.exists(checkpoint_path):\n", "\t    optimizer_state_path = f'{args.save_dir}/{args.model_name}_optimizer.pth'\n", "\t    if os.path.exists(optimizer_state_path):\n", "\t        Logger_main_rank(f\"加载优化器状态 {optimizer_state_path}\")\n", "\t        optimizer.load_state_dict(torch.load(optimizer_state_path, map_location=args.device))\n", "\t    else:\n", "\t        Logger_main_rank(f\"没有找到优化器状态，使用新的优化器\")\n", "\n", "    if False and platform.system() != 'Windows' and float(torch.__version__.split('.')[0]) >= 2:\n", "        Logger_main_rank(\"compiling the model... (takes a ~minute)\")\n", "        unoptimized_model = model\n", "        model = torch.compile(model)\n", "\n", "    iter_per_epoch = len(train_loader)\n", "    for epoch in range(args.epochs):\n", "        train_epoch(epoch, wandb)\n", "```"]}, {"cell_type": "markdown", "id": "afabb199-0840-43e8-933a-d3f81361e6d0", "metadata": {}, "source": ["### 9.3 预训练模型脚本的变形"]}, {"cell_type": "markdown", "id": "587744e7-0eaa-4c2b-bb05-c1cdbeca02a4", "metadata": {}, "source": ["```python\n", "\n", "import math\n", "import os\n", "from dataclasses import dataclass\n", "from typing import Tuple, Optional, Literal, List\n", "import torch\n", "import torch.distributed as dist\n", "import math\n", "import torch.nn.init as init\n", "from torch import nn\n", "import torch.nn.functional as F\n", "import torch.distributed as dist\n", "from transformers import PretrainedConfig\n", "from model.kernel import act_quant, weight_dequant, fp8_gemm\n", "from logger_utils import setup_logger\n", "import logging\n", "\n", "#分块量化的块尺寸\n", "block_size = 128\n", "\n", "#设置矩阵乘法所使用的精度\n", "gemm_impl: Literal[\"bf16\", \"fp8\"] = \"bf16\"\n", "#设置注意力的计算方法\n", "attn_impl: Literal[\"naive\", \"absorb\"] = \"absorb\"\n", "#设置环境变量\n", "os.environ[\"CUDA_LAUNCH_BLOCKING\"] = \"1\"\n", "\n", "@dataclass\n", "class ModelArgs(PretrainedConfig):\n", "    \"\"\"\n", "    Data class for defining model arguments and hyperparameters.\n", "\n", "    Attributes:\n", "        max_batch_size (int): Maximum batch size.\n", "        max_seq_len (int): Maximum sequence length.\n", "        dtype (Literal[\"bf16\", \"fp8\"]): Data type for computations.\n", "        vocab_size (int): Vocabulary size.\n", "        dim (int): Model dimension.\n", "        inter_dim (int): Intermediate dimension for MLP layers.\n", "        moe_inter_dim (int): Intermediate dimension for MoE layers.\n", "        n_layers (int): Number of transformer layers.\n", "        n_dense_layers (int): Number of dense layers in the model.\n", "        n_heads (int): Number of attention heads.\n", "        n_routed_experts (int): Number of routed experts for MoE layers.\n", "        n_shared_experts (int): Number of shared experts for MoE layers.\n", "        n_activated_experts (int): Number of activated experts in MoE layers.\n", "        n_expert_groups (int): Number of expert groups.\n", "        n_limited_groups (int): Number of limited groups for MoE routing.\n", "        score_func (Literal[\"softmax\", \"sigmoid\"]): Scoring function for MoE routing.\n", "        route_scale (float): Scaling factor for routing scores.\n", "        q_lora_rank (int): LoRA self.rank for query projections.\n", "        kv_lora_rank (int): LoRA self.rank for key-value projections.\n", "        qk_nope_head_dim (int): Dimension for query-key projections without positional embeddings.\n", "        qk_rope_head_dim (int): Dimension for query-key projections with rotary embeddings.\n", "        v_head_dim (int): Dimension for value projections.\n", "        original_seq_len (int): Original sequence length.\n", "        rope_theta (float): Base for rotary positional encoding.\n", "        rope_factor (float): Scaling factor for extended sequence lengths.\n", "        beta_fast (int): Fast beta correction factor.\n", "        beta_slow (int): Slow beta correction factor.\n", "        mscale (float): Scaling factor for extended attention.\n", "    \"\"\"\n", "    def __init__(self, **kwargs):\n", "        super().__init__(**kwargs)\n", "        # 增加kwargs.get以兼容代码中的属性引用\n", "        self.max_batch_size = kwargs.get(\"max_batch_size\", 8)\n", "        self.max_seq_len = kwargs.get(\"max_seq_len\", 4096)\n", "        self.dtype = kwargs.get(\"dtype\", \"bf16\")\n", "        self.vocab_size = kwargs.get(\"vocab_size\", 128000) # 按照deepseek的tokenizer重新修改\n", "        self.dim = kwargs.get(\"dim\", 1024) \n", "        self.inter_dim = kwargs.get(\"inter_dim\", 2048)  \n", "        self.moe_inter_dim = kwargs.get(\"moe_inter_dim\", 512) #MoE的inter_dim\n", "        self.n_layers = kwargs.get(\"n_layers\", 8) \n", "        self.n_dense_layers = kwargs.get(\"n_dense_layers\", 1)\n", "        self.n_heads = kwargs.get(\"n_heads\", 8) \n", "        # MoE\n", "        self.use_moe = kwargs.get(\"use_moe\", 1)\n", "        self.n_routed_experts = kwargs.get(\"n_routed_experts\", 4) #必须是gpu的倍数 \n", "        self.n_shared_experts = kwargs.get(\"n_shared_experts\", 1)  \n", "        self.n_activated_experts = kwargs.get(\"n_activated_experts\", 2) \n", "        self.n_expert_groups = kwargs.get(\"n_expert_groups\", 1)\n", "        self.n_limited_groups = kwargs.get(\"n_limited_groups\", 1)\n", "        self.score_func = kwargs.get(\"score_func\", \"softmax\")\n", "        self.route_scale = kwargs.get(\"route_scale\", 1.0)\n", "        # MLA\n", "        self.q_lora_rank = kwargs.get(\"q_lora_rank\", 0)\n", "        self.kv_lora_rank = kwargs.get(\"kv_lora_rank\", 256)  \n", "        self.qk_nope_head_dim = kwargs.get(\"qk_nope_head_dim\", 64)  \n", "        self.qk_rope_head_dim = kwargs.get(\"qk_rope_head_dim\", 32)  \n", "        self.v_head_dim = kwargs.get(\"v_head_dim\", 64)\n", "        # YARN\n", "        self.original_seq_len = kwargs.get(\"original_seq_len\", 4096)\n", "        self.rope_theta = kwargs.get(\"rope_theta\", 10000.0)\n", "        self.rope_factor = kwargs.get(\"rope_factor\", 40)\n", "        self.beta_fast = kwargs.get(\"beta_fast\", 32)\n", "        self.beta_slow = kwargs.get(\"beta_slow\", 1)\n", "        self.mscale = kwargs.get(\"mscale\", 1.0)\n", "        # MTP\n", "        self.n_mtp_depths = kwargs.get(\"n_mtp_depths\", 2)\n", "\n", "def get_rank_world_size():\n", "    if dist.is_initialized():\n", "        return dist.get_rank(), dist.get_world_size()\n", "    elif \"RANK\" in os.environ and \"WORLD_SIZE\" in os.environ:\n", "        return int(os.environ[\"RANK\"]), int(os.environ[\"WORLD_SIZE\"])\n", "    else:\n", "        return 0, 1\n", "\n", "def get_rank():\n", "    return get_rank_world_size()[0]\n", "\n", "def get_world_size():\n", "    return get_rank_world_size()[1]\n", "\n", "def get_logger(logger=None):\n", "    return logger or logging.getLogger(\"fallback_logger\")\n", "\n", "def print_memory(tag=\"\"):\n", "    print(f\"\\n📌 CUDA Memory Status {tag}:\")\n", "    print(f\"   Allocated: {torch.cuda.memory_allocated() / 1024 ** 2:.2f} MB\")\n", "    print(f\"   Reserved:  {torch.cuda.memory_reserved() / 1024 ** 2:.2f} MB\")\n", "    print(f\"   Max Allocated: {torch.cuda.max_memory_allocated() / 1024 ** 2:.2f} MB\")\n", "    print(f\"   Max Reserved:  {torch.cuda.max_memory_reserved() / 1024 ** 2:.2f} MB\\n\")\n", "\n", "class AllGatherWithGrad(torch.autograd.Function):\n", "    @staticmethod\n", "    def forward(ctx, input):\n", "        gathered = [torch.zeros_like(input) for _ in range(torch.distributed.get_world_size())]\n", "        torch.distributed.all_gather(gathered, input)\n", "        ctx.rank = torch.distributed.get_rank()\n", "        ctx.world_size = torch.distributed.get_world_size()\n", "        return torch.cat(gathered, dim=-1)\n", "\n", "    @staticmethod\n", "    def backward(ctx, grad_output):\n", "        return grad_output.chunk(ctx.world_size, dim=-1)[ctx.rank]\n", "\n", "class AllReduceWithGrad(torch.autograd.Function):\n", "    @staticmethod\n", "    def forward(ctx, input):\n", "        torch.distributed.all_reduce(input)\n", "        return input\n", "\n", "    @staticmethod\n", "    def backward(ctx, grad_output):\n", "        # 反向传播时，同样 all_reduce，保持一致性\n", "        torch.distributed.all_reduce(grad_output)\n", "        return grad_output\n", "\n", "#===================\n", "# 1. 遵循张量并行的Embedding层\n", "#===================\n", "\n", "class ParallelEmbedding(nn.Module):\n", "    \"\"\"\n", "    Embedding layer with parallelism support across distributed processes.\n", "\n", "    Args:\n", "        vocab_size (int): Vocabulary size.\n", "        dim (int): Embedding dimension.\n", "    \"\"\"\n", "    \n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, vocab_size: int, dim: int, logger=None):\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        self.vocab_size = vocab_size\n", "        self.dim = dim\n", "\n", "        #将需要向量化的token分到不同GPU上\n", "        assert self.vocab_size % self.world_size == 0, f\"Vocabulary size must be divisible by world size (world_size={self.world_size})\"\n", "        self.part_vocab_size = (self.vocab_size // self.world_size)\n", "\n", "        #划分为 [0,500],[500,1000],[1000,1500]这样的区间\n", "        self.vocab_start_idx = self.rank * self.part_vocab_size\n", "        self.vocab_end_idx = self.vocab_start_idx + self.part_vocab_size\n", "\n", "        #embedding层的权重等于一个GPU上的vocab_size乘以相应的权重\n", "        self.weight = nn.Parameter(torch.empty(self.part_vocab_size, self.dim, dtype=self.dtype or Linear.dtype))\n", "        nn.init.normal_(self.weight, mean=0.0, std=0.02)\n", "        self.logger.debug(f\"[DEBUG] ParallelEmbedding weight initialized: max={self.weight.max().item()}, min={self.weight.min().item()}, mean={self.weight.mean().item()}\")\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        assert x.max().item() < self.vocab_size, \\\n", "            f\"Token ID {x.max().item()} 超过 vocab_size {self.vocab_size}\"\n", "    \n", "        if self.world_size > 1:\n", "            mask = (x >= self.vocab_start_idx) & (x < self.vocab_end_idx)\n", "    \n", "            # 初始化输出\n", "            y = torch.zeros(x.shape + (self.dim,), device=x.device, dtype=self.weight.dtype)\n", "    \n", "            # 只处理 mask 内合法的局部 index\n", "            y[mask] = F.embedding(x[mask] - self.vocab_start_idx, self.weight)\n", "    \n", "            dist.all_reduce(y)\n", "        else:\n", "            y = F.embedding(x, self.weight)\n", "        return y\n", "\n", "#===================\n", "# 2. 支持混合精度 + 细粒度量化操作的线性函数与线性层\n", "#===================\n", "\n", "def linear(x: torch.Tensor, weight: torch.Tensor, bias: Optional[torch.Tensor] = None) -> torch.Tensor:\n", "    \"\"\"\n", "    自定义的、能够实现FP8、BF16、FP16、FP32各种精度之间的转化和GEMM的线性运算函数\n", "\n", "    这个函数的输入与nn.Linear层不同，函数需的是：\n", "    - 需要被线性变换的数据X，以及\n", "    - 线性变换所使用的权重weights，此时weights的结构就等同于\n", "    \n", "    补充：BF16和FP16的区别在于、都是16位数，但BF16是8个指数7个尾数（8E7M），FP16是5E10M，指数越大可表示的数值范围越大、尾数越大越精确。因此，BF16范围更大适用于梯度计算、激活值存储等场景、可以稳定训练；FP16更加精确、适用于权重存储等存储场景，更适合保存信息。\n", "    \"\"\"\n", "    # 如果字节大于1（精度大于fp8），则应该是fp16或者fp32、直接使用非量化计算\n", "    if weight.element_size() > 1:\n", "        return F.linear(x, weight, bias)\n", "    # 如果GEMM精度是bf16，那要将权重解量化为bf16\n", "    # 当我们默认训练使用BF16的时候，不会进入elif\n", "    # 下面的elif只能适应推理流程、不能用于训练\n", "    elif gemm_impl == \"bf16\":\n", "        weight = weight_dequant(weight, weight.scale)\n", "        return F.linear(x, weight, bias)\n", "    # 如果element_size不大于1，同时gemm_impl不是bf16（也就是fp8）\n", "    # 就说明是支持fp8计算的，则无论什么数据输入都量化成fp8\n", "    # 使用fp8精度进行fp8的GEMM\n", "    else:\n", "        x, scale = act_quant(x, block_size)\n", "        y = fp8_gemm(x, scale, weight, weight.scale)\n", "        if bias is not None:\n", "            y += bias\n", "        return y\n", "\n", "class Linear(nn.Module):\n", "    \"\"\"\n", "    自定义的、能够支持FP8精度的细粒度量化（主要是创建缩放因子进行缩放）的线性层。\n", "\n", "    Args:\n", "        in_features (int): Number of input features.\n", "        out_features (int): Number of output features.\n", "        bias (bool): Whether to include a bias term. Defaults to False.\n", "        dtype (optional): Data type for the layer. Defaults to `torch.bfloat16`.\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "\n", "    def __init__(self, in_features: int, out_features: int, bias: bool = False, dtype = None, logger=None):\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        self.in_features = in_features\n", "        self.out_features = out_features\n", "        self.weight = nn.Parameter(torch.empty(out_features, in_features, dtype=self.dtype or Linear.dtype or Linear.dtype))\n", "\n", "        # 权重初始化：kaiming_uniform\n", "        with torch.no_grad():\n", "            tmp_weight = torch.empty_like(self.weight, dtype=torch.float32)\n", "            init.xavier_uniform_(tmp_weight, gain=1.0 / math.sqrt(2))\n", "            self.weight.copy_(tmp_weight.to(self.weight.dtype))\n", "            if torch.isnan(self.weight).any():\n", "                self.logger.warning(f\"[WARNING] Linear weight contains NaN after kaiming init! rank={self.rank}\")\n", "        \n", "        # 如果字节 == 1，即weight精度是FP8，则执行归一化\n", "        # 将FP8权重缩放到FP8的动态范围内、则会需要对每一个“块”构建缩放因子\n", "        # 其中block_size是我们规定的“块”的大小，用下面的公式来确认缩放因子的具体数量\n", "        if self.weight.element_size() == 1:\n", "            scale_out_features = (out_features + block_size - 1) // block_size\n", "            scale_in_features = (in_features + block_size - 1) // block_size\n", "            self.weight.scale = self.scale = nn.Parameter(torch.empty(scale_out_features, scale_in_features, dtype=torch.float32))\n", "        else:\n", "            #如果不是fp8，则直接将scale设置为None来表示该参数不存在\n", "            self.register_parameter(\"scale\", None)\n", "        if bias:\n", "            self.bias = nn.Parameter(torch.empty(self.out_features),dtype=self.dtype or Linear.dtype)\n", "            with torch.no_grad():\n", "                init.zeros_(self.bias)\n", "        else:\n", "            self.register_parameter(\"bias\", None)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for the custom linear layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Transformed tensor after linear computation.\n", "        \"\"\"\n", "        return linear(x, self.weight, self.bias)\n", "\n", "#===================\n", "# 3. 支持分布式并行（张量并行）的线性层\n", "#===================\n", "\n", "class ColumnParallelLinear(Linear):\n", "    \"\"\"\n", "    具有列并行功能的线性层，将输出的features分布到不同的进程（GPU）上完成。\n", "\n", "    Args:\n", "        in_features (int): Number of input features.\n", "        out_features (int): Total number of output features.\n", "        bias (bool): Whether to include a bias term. Defaults to False.\n", "        dtype (optional): Data type for the layer. Defaults to `torch.bfloat16`.\n", "        gather_output (bool): 是否在前向中聚合输出（保持梯度计算图）\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, in_features: int, out_features: int, bias: bool = False, dtype=None, gather_output: bool = False, logger=None):\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        self.gather_output = gather_output\n", "\n", "        assert out_features % self.world_size == 0, \\\n", "            f\"Output features must be divisible by world size (world_size={self.world_size})\"\n", "        self.part_out_features = out_features // self.world_size\n", "\n", "        super().__init__(in_features, self.part_out_features, bias, dtype)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for column parallel linear layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor. Shape: [B, T, in_features]\n", "\n", "        Returns:\n", "            torch.Tensor: Output tensor. If gather_output=True, shape = [B, T, out_features]\n", "                          Otherwise, shape = [B, T, out_features / world_size]\n", "        \"\"\"\n", "        y = F.linear(x, self.weight, self.bias)  # y: [B, T, out_features / world_size]\n", "        #self.logger.debug(f\"[DEBUG] ColumnParallelLinear rank {self.rank} output shape: {y.shape}\")\n", "\n", "        if self.gather_output and self.world_size > 1:\n", "            y = AllGatherWithGrad.apply(y)\n", "        return y\n", "\n", "class RowParallelLinear(Linear):\n", "    \"\"\"\n", "    具有行并行功能的线性层，将输入的features分布到不同的进程（GPU）上完成。\n", "\n", "    Args:\n", "        in_features (int): Total number of input features.\n", "        out_features (int): Number of output features.\n", "        bias (bool): Whether to include a bias term. Defaults to False.\n", "        dtype (optional): Data type for the layer. Defaults to `torch.bfloat16`.\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, in_features: int, out_features: int, bias: bool = False, dtype = None, logger=None):\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        assert in_features % self.world_size == 0, f\"Input features must be divisible by world size (world_size={world_size})\"\n", "        self.part_in_features = in_features // self.world_size\n", "        super().__init__(self.part_in_features, out_features, bias, dtype)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for row parallel linear layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Transformed tensor with row-parallel computation.\n", "        \"\"\"\n", "        #print(\"Row\",self.rank, self.weight.shape)\n", "        y = linear(x, self.weight)\n", "\n", "        #如果是多线程运行，还需要进行all_reduce聚合，并加上偏置\n", "        if self.world_size > 1:\n", "            y = AllReduceWithGrad.apply(y)\n", "        if self.bias is not None:\n", "            y += self.bias\n", "        return y\n", "\n", "#===================\n", "# 4. 并行化的RMS Norm 根均方归一化\n", "#===================\n", "\n", "class RMSNorm(nn.Module):\n", "    \"\"\"\n", "    huggngface-style RMSNorm\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, dim, eps=1e-5, logger=None):\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.eps = eps\n", "        self.weight = nn.Parameter(torch.ones(dim,dtype=self.dtype or Linear.dtype))\n", "\n", "    def forward(self, x):\n", "        norm = x.pow(2).mean(-1, keepdim=True)\n", "        x = x / torch.sqrt(norm + self.eps)\n", "        return self.weight * x\n", "\n", "#===================\n", "# 5. 旋转位置编码\n", "# deepseek从序列长度、频率生成、平滑校正等多个角度改进了旋转位置编码\n", "# 其核心与llama所使用的旋转位置编码相同\n", "#===================\n", "\n", "def precompute_freqs_cis(args: ModelArgs) -> torch.Tensor:\n", "    \"\"\"\n", "    对旋转位置变啊中所需要的指数权重进行预计算，方便后续使用。\n", "\n", "    Args:\n", "        args (ModelArgs): Model arguments containing positional embedding parameters.\n", "\n", "    Returns:\n", "        torch.Tensor: Precomputed complex exponential values for positional embeddings.\n", "    \"\"\"\n", "    dim = args.qk_rope_head_dim\n", "    seqlen = args.max_seq_len\n", "    beta_fast = args.beta_fast\n", "    beta_slow = args.beta_slow\n", "    base = args.rope_theta\n", "    factor = args.rope_factor\n", "\n", "    def find_correction_dim(num_rotations, dim, base, max_seq_len):\n", "        \"\"\"\n", "        Computes the correction dimension for a given number of rotations in the rotary positional embedding.\n", "\n", "        Args:\n", "            num_rotations (float): Number of rotations to compute the correction for.\n", "            dim (int): Dimensionality of the embedding space.\n", "            base (float): Base value for the exponential computation.\n", "            max_seq_len (int): Maximum sequence length.\n", "\n", "        Returns:\n", "            float: The correction dimension based on the input parameters.\n", "        \"\"\"\n", "        return dim * math.log(max_seq_len / (num_rotations * 2 * math.pi)) / (2 * math.log(base))\n", "\n", "    def find_correction_range(low_rot, high_rot, dim, base, max_seq_len):\n", "        \"\"\"\n", "        Computes the range of correction dimensions for rotary positional embeddings.\n", "\n", "        Args:\n", "            low_rot (float): Lower bound for the number of rotations.\n", "            high_rot (float): Upper bound for the number of rotations.\n", "            dim (int): Dimensionality of the embedding space.\n", "            base (float): Base value for the exponential computation.\n", "            max_seq_len (int): Maximum sequence length.\n", "\n", "        Returns:\n", "            Tuple[int, int]: The range of correction dimensions (low, high), clamped to valid indices.\n", "        \"\"\"\n", "        low = math.floor(find_correction_dim(low_rot, dim, base, max_seq_len))\n", "        high = math.ceil(find_correction_dim(high_rot, dim, base, max_seq_len))\n", "        return max(low, 0), min(high, dim-1)\n", "\n", "    def linear_ramp_factor(min, max, dim):\n", "        \"\"\"\n", "        Computes a linear ramp function used to smooth values between a minimum and maximum range.\n", "\n", "        Args:\n", "            min (float): Minimum value for the ramp function.\n", "            max (float): Maximum value for the ramp function.\n", "            dim (int): Dimensionality of the ramp tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: A tensor of shape (dim,) with values linearly interpolated between 0 and 1,\n", "                clamped to the range [0, 1].\n", "        \"\"\"\n", "        if min == max:\n", "            max += 0.001\n", "        linear_func = (torch.arange(dim, dtype=torch.float32) - min) / (max - min)\n", "        ramp_func = torch.clamp(linear_func, 0, 1)\n", "        return ramp_func\n", "\n", "    freqs = 1.0 / (base ** (torch.arange(0, dim, 2, dtype=torch.float32) / dim))\n", "    if seqlen > args.original_seq_len:\n", "        low, high = find_correction_range(beta_fast, beta_slow, dim, base, args.original_seq_len)\n", "        smooth = 1 - linear_ramp_factor(low, high, dim // 2)\n", "        freqs = freqs / factor * (1 - smooth) + freqs * smooth\n", "\n", "    t = torch.arange(seqlen)\n", "    freqs = torch.outer(t, freqs)\n", "    freqs_cis = torch.polar(torch.ones_like(freqs), freqs)\n", "    return freqs_cis\n", "\n", "\n", "def apply_rotary_emb(x: torch.Tensor, freqs_cis: torch.Tensor) -> torch.Tensor:\n", "    \"\"\"\n", "    Applies rotary positional embeddings to the input tensor.\n", "\n", "    Args:\n", "        x (torch.Tensor): Input tensor with positional embeddings to be applied.\n", "        freqs_cis (torch.Tensor): Precomputed complex exponential values for positional embeddings.\n", "\n", "    Returns:\n", "        torch.Tensor: Tensor with rotary embeddings applied.\n", "    \"\"\"\n", "    dtype = x.dtype\n", "    x = torch.view_as_complex(x.float().view(*x.shape[:-1], -1, 2))\n", "    freqs_cis = freqs_cis.view(1, x.size(1), 1, x.size(-1))\n", "    y = torch.view_as_real(x * freqs_cis).flatten(3)\n", "    return y.to(dtype)\n", "\n", "#===================\n", "# 6. MLA 潜在注意力机制\n", "#===================\n", "\n", "class MLA(nn.<PERSON><PERSON><PERSON>):\n", "    \"\"\"\n", "    Multi-Headed Attention Layer (MLA).\n", "\n", "    Attributes:\n", "        dim (int): Dimensionality of the input features.\n", "        n_heads (int): Number of attention heads.\n", "        n_local_heads (int): Number of local attention heads for distributed systems.\n", "        q_lora_rank (int): self.rank for low-rank query projection.\n", "        kv_lora_rank (int): self.rank for low-rank key/value projection.\n", "        qk_nope_head_dim (int): Dimensionality of non-positional query/key projections.\n", "        qk_rope_head_dim (int): Dimensionality of rotary-positional query/key projections.\n", "        qk_head_dim (int): Total dimensionality of query/key projections.\n", "        v_head_dim (int): Dimensionality of value projections.\n", "        softmax_scale (float): Scaling factor for softmax in attention computation.\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, args: ModelArgs, logger=None):\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        self.dim = args.dim\n", "        self.n_heads = args.n_heads\n", "        self.n_local_heads = args.n_heads // self.world_size\n", "        self.q_lora_rank = args.q_lora_rank\n", "        self.kv_lora_rank = args.kv_lora_rank\n", "        self.qk_nope_head_dim = args.qk_nope_head_dim\n", "        self.qk_rope_head_dim = args.qk_rope_head_dim\n", "        self.qk_head_dim = args.qk_nope_head_dim + args.qk_rope_head_dim\n", "        self.v_head_dim = args.v_head_dim\n", "\n", "        if self.q_lora_rank == 0:\n", "            self.wq = ColumnParallelLinear(self.dim, self.n_heads * self.qk_head_dim, logger=self.logger)\n", "            self.logger.debug(\"[DEBUG] wq weight NaN: %s\", torch.isnan(self.wq.weight).any())\n", "        else:\n", "            self.wq_a = Linear(self.dim, self.q_lora_rank, logger=self.logger)\n", "            self.q_norm = RMSNorm(self.q_lora_rank, logger=self.logger)\n", "            self.wq_b = ColumnParallelLinear(self.q_lora_rank, self.n_heads * self.qk_head_dim, logger=self.logger)\n", "        self.wkv_a = Linear(self.dim, self.kv_lora_rank + self.qk_rope_head_dim, logger=self.logger)\n", "        self.kv_norm = RMSNorm(self.kv_lora_rank, logger=self.logger)\n", "        self.wkv_b = ColumnParallelLinear(self.kv_lora_rank, self.n_heads * (self.qk_nope_head_dim + self.v_head_dim), logger=self.logger)\n", "        self.wo = RowParallelLinear(self.n_heads * self.v_head_dim, self.dim, logger=self.logger)\n", "        self.softmax_scale = self.qk_head_dim ** -0.5\n", "        if args.max_seq_len > args.original_seq_len:\n", "            mscale = 0.1 * args.mscale * math.log(args.rope_factor) + 1.0\n", "            self.softmax_scale = self.softmax_scale * mscale * mscale\n", "\n", "        if attn_impl == \"naive\":\n", "            self.register_buffer(\"k_cache\", torch.zeros(args.max_batch_size, args.max_seq_len, self.n_local_heads, self.qk_head_dim), persistent=False)\n", "            self.register_buffer(\"v_cache\", torch.zeros(args.max_batch_size, args.max_seq_len, self.n_local_heads, self.v_head_dim), persistent=False)\n", "        else:\n", "            self.register_buffer(\"kv_cache\", torch.zeros(args.max_batch_size, args.max_seq_len, self.kv_lora_rank), persistent=False)\n", "            self.register_buffer(\"pe_cache\", torch.zeros(args.max_batch_size, args.max_seq_len, self.qk_rope_head_dim), persistent=False)\n", "\n", "    def forward(self, x: torch.Tensor, start_pos: int, freqs_cis: torch.Tensor, mask: Optional[torch.Tensor]):\n", "        \"\"\"\n", "        Forward pass for the Multi-Head<PERSON> <PERSON><PERSON><PERSON> (MLA).\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor of shape (batch_size, seq_len, dim).\n", "            start_pos (int): Starting position in the sequence for caching.\n", "            freqs_cis (torch.Tensor): Precomputed complex exponential values for rotary embeddings.\n", "            mask (Optional[torch.Tensor]): Mask tensor to exclude certain positions from attention.\n", "\n", "        Returns:\n", "            torch.Tensor: Output tensor with the same shape as the input.\n", "        \"\"\"\n", "        bsz, seqlen, _ = x.size()\n", "        end_pos = start_pos + seqlen\n", "    \n", "        # 1. Q projection\n", "        if self.q_lora_rank == 0:\n", "            q = self.wq(x)\n", "        else:\n", "            q = self.wq_b(self.q_norm(self.wq_a(x)))\n", "        self.logger.debug(\"[DEBUG] q before nope NaN: %s, inf: %s\", torch.isnan(q).any(), torch.isinf(q).any())\n", "        q = q.view(b<PERSON>, seqlen, self.n_local_heads, self.qk_head_dim)\n", "        q_nope, q_pe = torch.split(q, [self.qk_nope_head_dim, self.qk_rope_head_dim], dim=-1)\n", "        q_pe = apply_rotary_emb(q_pe, freqs_cis)\n", "    \n", "        # 2. KV projection\n", "        kv = self.wkv_a(x)\n", "        kv, k_pe = torch.split(kv, [self.kv_lora_rank, self.qk_rope_head_dim], dim=-1)\n", "        k_pe = apply_rotary_emb(k_pe.unsqueeze(2), freqs_cis)\n", "    \n", "        if self.training:\n", "            # === Training Mode ===\n", "            q = torch.cat([q_nope, q_pe], dim=-1)\n", "            kv = self.wkv_b(self.kv_norm(kv))\n", "            kv = kv.view(bsz, seqlen, self.n_local_heads, self.qk_nope_head_dim + self.v_head_dim)\n", "            k_nope, v = torch.split(kv, [self.qk_nope_head_dim, self.v_head_dim], dim=-1)\n", "            k = torch.cat([k_nope, k_pe.expand(-1, -1, self.n_local_heads, -1)], dim=-1)\n", "\n", "            if torch.isnan(q).any():\n", "                logger.error(\"[NaN] ❌ Q input contains NaN before einsum\")\n", "            scores = torch.einsum(\"bshd,bthd->bsht\", q, k) * self.softmax_scale\n", "            if mask is not None:\n", "                scores += mask.unsqueeze(1)\n", "            scores = scores.softmax(dim=-1, dtype=torch.float32).type_as(x)\n", "            x = torch.einsum(\"bsht,bthd->bshd\", scores, v)\n", "    \n", "        else:\n", "            # === Inference Mode ===\n", "            if attn_impl == \"naive\":\n", "                q = torch.cat([q_nope, q_pe], dim=-1)\n", "                kv = self.wkv_b(self.kv_norm(kv))\n", "                kv = kv.view(bsz, seqlen, self.n_local_heads, self.qk_nope_head_dim + self.v_head_dim)\n", "                k_nope, v = torch.split(kv, [self.qk_nope_head_dim, self.v_head_dim], dim=-1)\n", "                k = torch.cat([k_nope, k_pe.expand(-1, -1, self.n_local_heads, -1)], dim=-1)\n", "                self.k_cache[:bsz, start_pos:end_pos] = k.detach()\n", "                self.v_cache[:bsz, start_pos:end_pos] = v.detach()\n", "    \n", "                scores = torch.einsum(\"bshd,bthd->bsht\", q, self.k_cache[:bsz, :end_pos]) * self.softmax_scale\n", "            else:\n", "                wkv_b = self.wkv_b.weight if self.wkv_b.scale is None else weight_dequant(self.wkv_b.weight, self.wkv_b.scale, block_size)\n", "                wkv_b = wkv_b.view(self.n_local_heads, self.qk_nope_head_dim + self.v_head_dim, self.kv_lora_rank)\n", "                q_nope = torch.einsum(\"bshd,hdc->bshc\", q_nope, wkv_b[:, :self.qk_nope_head_dim])\n", "                self.kv_cache[:bsz, start_pos:end_pos] = self.kv_norm(kv).detach()\n", "                self.pe_cache[:bsz, start_pos:end_pos] = k_pe.squeeze(2).detach()\n", "    \n", "                scores = (torch.einsum(\"bshc,btc->bsht\", q_nope, self.kv_cache[:bsz, :end_pos]) +\n", "                          torch.einsum(\"bshr,btr->bsht\", q_pe, self.pe_cache[:bsz, :end_pos])) * self.softmax_scale\n", "    \n", "            if mask is not None:\n", "                scores += mask.unsqueeze(1)\n", "            scores = scores.softmax(dim=-1, dtype=torch.float32).type_as(x)\n", "    \n", "            if attn_impl == \"naive\":\n", "                x = torch.einsum(\"bsht,bthd->bshd\", scores, self.v_cache[:bsz, :end_pos])\n", "            else:\n", "                x = torch.einsum(\"bsht,btc->bshc\", scores, self.kv_cache[:bsz, :end_pos])\n", "                x = torch.einsum(\"bshc,hdc->bshd\", x, wkv_b[:, -self.v_head_dim:])\n", "    \n", "        x = self.wo(x.flatten(2))\n", "        return x\n", "\n", "#===================\n", "# 7. DeepSeekMOE\n", "#===================\n", "\n", "class MLP(nn.Mo<PERSON>le):\n", "    \"\"\"\n", "    支持张量并行的SwiGLU FFN前馈神经网络，并行方式为切分features，用于支持共享专家的构建。\n", "    Multi-Layer Perceptron (MLP) used as a feed-forward layer.\n", "\n", "    Attributes:\n", "        w1 (nn.<PERSON><PERSON><PERSON>): Linear layer for input-to-hidden transformation.\n", "        w2 (nn.<PERSON><PERSON><PERSON>): Linear layer for hidden-to-output transformation.\n", "        w3 (nn.<PERSON><PERSON><PERSON>): Additional linear layer for feature transformation.\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, dim: int, inter_dim: int, logger=None):\n", "        \"\"\"\n", "        Initializes the MLP layer.\n", "\n", "        Args:\n", "            dim (int): Input and output dimensionality.\n", "            inter_dim (int): Hidden layer dimensionality.\n", "        \"\"\"\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        self.w1 = ColumnParallelLinear(dim, inter_dim, logger=self.logger)\n", "        self.w2 = RowParallelLinear(inter_dim, dim, logger=self.logger)\n", "        self.w3 = ColumnParallelLinear(dim, inter_dim, logger=self.logger)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for the MLP layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Output tensor after MLP computation.\n", "        \"\"\"\n", "        return self.w2(<PERSON><PERSON>(self.w1(x)) * self.w3(x))\n", "\n", "class Gate(nn.Mo<PERSON>le):\n", "    \"\"\"\n", "    Gating mechanism for routing inputs in a mixture-of-experts (MoE) model.\n", "\n", "    Attributes:\n", "        dim (int): Dimensionality of input features.\n", "        topk (int): Number of top experts activated for each input.\n", "        n_groups (int): Number of groups for routing.\n", "        topk_groups (int): Number of groups to route inputs to.\n", "        score_func (str): Scoring function ('softmax' or 'sigmoid').\n", "        route_scale (float): Scaling factor for routing weights.\n", "        weight (torch.nn.Parameter): Learnable weights for the gate.\n", "        bias (Optional[torch.nn.Parameter]): Optional bias term for the gate.\n", "    \"\"\"\n", "\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, args: ModelArgs, logger=None):\n", "        \"\"\"\n", "        Initializes the Gate module.\n", "\n", "        Args:\n", "            args (ModelArgs): Model arguments containing gating parameters.\n", "        \"\"\"\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        self.dim = args.dim\n", "        self.topk = args.n_activated_experts\n", "        self.n_groups = args.n_expert_groups\n", "        self.topk_groups = args.n_limited_groups\n", "        self.score_func = args.score_func\n", "        self.route_scale = args.route_scale\n", "        self.weight = nn.Parameter(torch.empty(args.n_routed_experts, args.dim, dtype=self.dtype or Linear.dtype))\n", "        with torch.no_grad():\n", "            tmp = torch.empty_like(self.weight, dtype=torch.float32)\n", "            init.kaiming_uniform_(tmp, a=math.sqrt(5))\n", "            self.weight.copy_(tmp.to(self.dtype or Linear.dtype))\n", "            if torch.isnan(self.weight).any():\n", "                self.logger.warning(f\"[WARNING] Gate weight contains NaN! rank={self.rank}\")\n", "        \n", "        if self.dim == 7168:\n", "            self.bias = nn.Parameter(torch.empty(args.n_routed_experts, dtype=self.dtype or Linear.dtype))\n", "            with torch.no_grad():\n", "                init.zeros_(self.bias)\n", "        else:\n", "            self.bias = None\n", "\n", "    def forward(self, x: torch.Tensor) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:\n", "        \"\"\"\n", "        Forward pass for the gating mechanism.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            Tuple[torch.Tensor, torch.Tensor]: Routing weights and selected expert indices.\n", "        \"\"\"\n", "        scores = linear(x, self.weight)\n", "        if self.score_func == \"softmax\":\n", "            scores = scores.to(torch.float32).softmax(dim=-1).to(x.dtype)\n", "        else:\n", "            scores = scores.sigmoid()\n", "        original_scores = scores\n", "        if self.bias is not None:\n", "            scores = scores + self.bias\n", "        if self.n_groups > 1:\n", "            scores = scores.view(x.size(0), self.n_groups, -1)\n", "            if self.bias is None:\n", "                group_scores = scores.amax(dim=-1)\n", "            else:\n", "                group_scores = scores.topk(2, dim=-1)[0].sum(dim=-1)\n", "            indices = group_scores.topk(self.topk_groups, dim=-1)[1]\n", "            mask = scores.new_ones(x.size(0), self.n_groups, dtype=bool).scatter_(1, indices, False)\n", "            scores = scores.masked_fill_(mask.unsqueeze(-1), float(\"-inf\")).flatten(1)\n", "        indices = torch.topk(scores, self.topk, dim=-1)[1]\n", "        weights = original_scores.gather(1, indices)\n", "        if self.score_func == \"sigmoid\":\n", "            weights /= weights.sum(dim=-1, keepdim=True)\n", "        weights *= self.route_scale\n", "        self.logger.debug(f\"[DEBUG] Gate weights grad_fn: {weights.grad_fn}, requires_grad: {weights.requires_grad}\")\n", "        return weights.type_as(x), indices\n", "\n", "class Expert(nn.<PERSON><PERSON><PERSON>):\n", "    \"\"\"\n", "    Expert layer for Mixture-of-Experts (MoE) models.\n", "\n", "    Attributes:\n", "        w1 (nn.<PERSON><PERSON><PERSON>): Linear layer for input-to-hidden transformation.\n", "        w2 (nn.<PERSON><PERSON><PERSON>): Linear layer for hidden-to-output transformation.\n", "        w3 (nn.<PERSON><PERSON><PERSON>): Additional linear layer for feature transformation.\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, dim: int, inter_dim: int, logger=None):\n", "        \"\"\"\n", "        Initializes the Expert layer.\n", "\n", "        Args:\n", "            dim (int): Input and output dimensionality.\n", "            inter_dim (int): Hidden layer dimensionality.\n", "        \"\"\"\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        self.w1 = Linear(dim, inter_dim, logger=self.logger)\n", "        self.w2 = Linear(inter_dim, dim, logger=self.logger)\n", "        self.w3 = Linear(dim, inter_dim, logger=self.logger)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for the Expert layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Output tensor after expert computation.\n", "        \"\"\"\n", "        return self.w2(<PERSON><PERSON>(self.w1(x)) * self.w3(x))\n", "\n", "\n", "class MoE(nn.Module):\n", "    \"\"\"\n", "    Mixture-of-Experts (MoE) module.\n", "\n", "    Attributes:\n", "        dim (int): Dimensionality of input features.\n", "        n_routed_experts (int): Total number of experts in the model.\n", "        n_local_experts (int): Number of experts handled locally in distributed systems.\n", "        n_activated_experts (int): Number of experts activated for each input.\n", "        gate (nn.Mo<PERSON><PERSON>): Gating mechanism to route inputs to experts.\n", "        experts (nn.ModuleList): List of expert modules.\n", "        shared_experts (nn.<PERSON><PERSON><PERSON>): Shared experts applied to all inputs.\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "\n", "    def __init__(self, args: ModelArgs, logger=None):\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        self.dim = args.dim\n", "\n", "        assert args.n_routed_experts % self.world_size == 0, \\\n", "            f\"Number of experts must be divisible by world size (world_size={self.world_size})\"\n", "\n", "        self.n_routed_experts = args.n_routed_experts\n", "        self.n_local_experts = args.n_routed_experts // self.world_size\n", "        self.n_activated_experts = args.n_activated_experts\n", "\n", "        self.experts_start_idx = self.rank * self.n_local_experts\n", "        self.experts_end_idx = self.experts_start_idx + self.n_local_experts\n", "\n", "        self.gate = Gate(args, logger=self.logger)\n", "        self.experts = nn.ModuleList([\n", "            Expert(args.dim, args.moe_inter_dim, logger=self.logger)\n", "            if self.experts_start_idx <= i < self.experts_end_idx else None\n", "            for i in range(self.n_routed_experts)\n", "        ])\n", "        self.shared_experts = MLP(args.dim, args.n_shared_experts * args.moe_inter_dim, logger=self.logger)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        if self.training:\n", "            return self._forward_train(x)\n", "        else:\n", "            with torch.no_grad():\n", "                return self._forward_infer(x)\n", "\n", "    def _forward_train(self, x: torch.Tensor) -> torch.Tensor:\n", "        shape = x.size()\n", "        x = x.view(-1, self.dim)\n", "        weights, indices = self.gate(x)  # shape: [B*T, topk]\n", "        y = torch.zeros_like(x)\n", "\n", "        for i in range(self.experts_start_idx, self.experts_end_idx):\n", "            expert = self.experts[i]\n", "            idx, top = torch.where(indices == i)\n", "            if idx.numel() == 0:\n", "                continue\n", "            x_selected = x[idx]\n", "            self.logger.debug(f\"[Expert {i}] x_selected stats: max={x_selected.max().item():.4f}, min={x_selected.min().item():.4f}\")\n", "            expert_out = expert(x_selected)\n", "            if torch.isnan(expert_out).any():\n", "                self.logger.error(\"[NaN] ❌ expert_out contains NaN before index_add\")\n", "            self.logger.debug(f\"[Expert {i}] weights stats: max={weights[idx, top].max().item():.4f}, min={weights[idx, top].min().item():.4f}\")\n", "            y = y.index_add(0, idx, expert_out * weights[idx, top].unsqueeze(-1))\n", "\n", "        z = self.shared_experts(x)\n", "\n", "        if self.world_size > 1:\n", "            dist.all_reduce(y)\n", "\n", "        return (y + z).view(shape)\n", "\n", "    def _forward_infer(self, x: torch.Tensor) -> torch.Tensor:\n", "        shape = x.size()\n", "        x = x.view(-1, self.dim)\n", "        weights, indices = self.gate(x)\n", "\n", "        flat_indices = indices.view(-1)\n", "        flat_weights = weights.view(-1)\n", "        total_experts = self.n_routed_experts\n", "\n", "        counts = torch.bincount(flat_indices, minlength=total_experts)\n", "        tokens_per_expert = counts.tolist()\n", "        idxs = flat_indices.argsort()\n", "        sorted_x = x[idxs]\n", "\n", "        local_tokens = []\n", "        for i in range(self.experts_start_idx, self.experts_end_idx):\n", "            num_tokens = tokens_per_expert[i]\n", "            if num_tokens == 0:\n", "                continue\n", "            start_idx = sum(tokens_per_expert[:i])\n", "            end_idx = start_idx + num_tokens\n", "            tokens_for_expert = sorted_x[start_idx:end_idx]\n", "            expert_out = self.experts[i](tokens_for_expert)\n", "            local_tokens.append(expert_out)\n", "\n", "        local_output = torch.cat(local_tokens, dim=0) if local_tokens else sorted_x.new_empty(0)\n", "\n", "        output_gather = [torch.empty_like(local_output) for _ in range(self.world_size)]\n", "        dist.all_gather(output_gather, local_output)\n", "        gathered_output = torch.cat(output_gather, dim=0)\n", "\n", "        gathered_x = torch.empty_like(gathered_output)\n", "        gathered_x[idxs] = gathered_output\n", "\n", "        final_out = (\n", "            gathered_x.view(*indices.shape, -1)\n", "            .type(weights.dtype)\n", "            .mul_(weights.unsqueeze(-1))\n", "            .sum(dim=1)\n", "            .type(x.dtype)\n", "        )\n", "\n", "        z = self.shared_experts(x)\n", "\n", "        if self.world_size > 1:\n", "            dist.all_reduce(final_out)\n", "\n", "        return (final_out + z).view(shape)\n", "\n", "#===================\n", "# 8. Decoder layers\n", "#===================\n", "\n", "class Block(nn.Module):\n", "    \"\"\"\n", "    Transformer block combining attention and feed-forward layers.\n", "\n", "    Attributes:\n", "        attn (nn.Mo<PERSON>le): Attention layer (MLA).\n", "        ffn (nn.Module): Feed-forward network (MLP or MoE).\n", "        attn_norm (nn.<PERSON><PERSON><PERSON>): Layer normalization for attention.\n", "        ffn_norm (nn.Module): Layer normalization for feed-forward network.\n", "    \"\"\"\n", "    dtype = torch.bfloat16 if gemm_impl == \"bf16\" else torch.float8_e4m3fn\n", "    \n", "    def __init__(self, layer_id: int, args: ModelArgs, logger=None):\n", "        \"\"\"\n", "        Initializes the Transformer block.\n", "\n", "        Args:\n", "            layer_id (int): Layer index in the transformer.\n", "            args (ModelArgs): Model arguments containing block parameters.\n", "        \"\"\"\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.rank = get_rank()\n", "        self.layer_id = layer_id\n", "        self.world_size = get_world_size()\n", "        self.attn = MLA(args, logger=self.logger)\n", "        self.ffn = MLP(args.dim, args.inter_dim, logger=self.logger) if layer_id < args.n_dense_layers else MoE(args, logger=self.logger)\n", "        self.attn_norm = RMSNorm(args.dim, logger=self.logger)\n", "        self.ffn_norm = RMSNorm(args.dim, logger=self.logger)\n", "\n", "    def forward(self, x: torch.Tensor, start_pos: int, freqs_cis: torch.Tensor, mask: Optional[torch.Tensor]) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for the Transformer block.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "            start_pos (int): Starting position in the sequence.\n", "            freqs_cis (torch.Tensor): Precomputed complex exponential values for rotary embeddings.\n", "            mask (Optional[torch.Tensor]): Mask tensor to exclude certain positions from attention.\n", "\n", "        Returns:\n", "            torch.Tensor: Output tensor after block computation.\n", "        \"\"\"\n", "        self.logger.debug(f\"[Attention Layer {self.layer_id}]\")\n", "        self.logger.debug(f\"[DEBUG] input contains NaN: {torch.isnan(x).any()}\")\n", "        fn = self.attn_norm(x)\n", "        self.logger.debug(f\"[DEBUG] attn_norm NaN: {torch.isnan(fn).any()}\")\n", "        fn = self.attn(fn, start_pos, freqs_cis, mask)\n", "        self.logger.debug(f\"[DEBUG] attn NaN: {torch.isnan(fn).any()}\")\n", "        x = x + fn\n", "        self.logger.debug(f\"[DEBUG] first_res NaN: {torch.isnan(x).any()}\")\n", "        fn2 = self.ffn_norm(x)\n", "        self.logger.debug(f\"[DEBUG] ffn_norm NaN: {torch.isnan(fn2).any()}\")\n", "        fn2 = self.ffn(fn2)\n", "        self.logger.debug(f\"[DEBUG] ffn NaN: {torch.isnan(fn2).any()}\")\n", "        x = x + fn2\n", "        self.logger.debug(f\"[DEBUG] second_res NaN: {torch.isnan(x).any()}\")\n", "        return x\n", "\n", "#===================\n", "# 9. 完整的DeepSeek\n", "#===================\n", "\n", "class Transformer(nn.Mo<PERSON>le):\n", "    \"\"\"\n", "    Transformer model with positional embeddings, multiple layers, and output projection.\n", "\n", "    Attributes:\n", "        max_seq_len (int): Maximum sequence length for the transformer.\n", "        embed (nn.Mo<PERSON><PERSON>): Embedding layer for input tokens.\n", "        layers (torch.nn.ModuleList): List of transformer blocks.\n", "        norm (nn.Module): Layer normalization applied after all blocks.\n", "        head (nn.<PERSON><PERSON><PERSON>): Output projection layer mapping to vocabulary size.\n", "        freqs_cis (torch.Tensor): Precomputed complex exponential values for rotary embeddings.\n", "    \"\"\"    \n", "    def __init__(self, args: ModelArgs, logger=None):\n", "        \"\"\"\n", "        Initializes the Transformer model.\n", "\n", "        Args:\n", "            args (ModelArgs): Model arguments containing transformer parameters.\n", "        \"\"\"\n", "        self.rank = get_rank()\n", "        self.world_size = get_world_size()\n", "        Linear.dtype = torch.bfloat16\n", "        #Linear.dtype = torch.float8_e4m3fn if args.dtype == \"fp8\" else torch.bfloat16\n", "        super().__init__()\n", "        self.logger = get_logger(logger)\n", "        self.max_seq_len = args.max_seq_len\n", "        self.embed = ParallelEmbedding(args.vocab_size, args.dim, logger=self.logger) #在MTP模块中共享\n", "        self.layers = nn.ModuleList([Block(i, args, logger=self.logger) for i in range(args.n_layers)])\n", "        self.norm = RMSNorm(args.dim, logger=self.logger)\n", "        self.head = ColumnParallelLinear(args.dim, args.vocab_size, dtype=torch.get_default_dtype(), gather_output=True, logger=self.logger)\n", "        self.logger.debug(f\"[CHECK] head weight stats: max={self.head.weight.max().item()}, min={self.head.weight.min().item()}, mean={self.head.weight.mean().item()}\")\n", "        self.register_buffer(\"freqs_cis\", precompute_freqs_cis(args), persistent=False)\n", "\n", "        # ✅ 构建MTPlayers所所需的组件\n", "        # 其中embedding与output层是与之前的trans共享\n", "        # 定义预测未来token的数量\n", "        self.n_mtp_depths = args.n_mtp_depths\n", "        self.mtp_rms_norm1 = RMSNorm(args.dim)  # 归一化 h_i^{k-1}\n", "        self.mtp_rms_norm2 = RMSNorm(args.dim)  # 归一化 Emb(t_{i+k})\n", "        # 按照未来token数量确定投影层的数量\n", "        self.mtp_projections = nn.ModuleList([\n", "            Linear(args.dim*2, args.dim, logger=self.logger) for _ in range(self.n_mtp_depths) \n", "        ])\n", "        # 按照未来token数量确定要使用的decoder layer数量\n", "        self.mtp_layers = nn.ModuleList([\n", "            Block(args.n_layers + i, args, logger=self.logger) for i in range(self.n_mtp_depths) \n", "        ])\n", "\n", "        def register_nan_hook(module, name, logger):\n", "            def hook(module, grad_input, grad_output):\n", "                logger.debug(f\"[HOOK ✅] {name} backward triggered\")\n", "                for i, g in enumerate(grad_output):\n", "                    if g is not None and torch.isnan(g).any():\n", "                        logger.error(f\"🚨 NaN detected in {name} grad_output[{i}]\")\n", "        \n", "            def forward_hook(module, input, output):\n", "                logger.debug(f\"[HOOK 🚀] {name} forward triggered\")\n", "        \n", "            module.register_forward_hook(forward_hook)\n", "            module.register_full_backward_hook(hook)\n", "        \n", "        for i, layer in enumerate(self.layers):\n", "            register_nan_hook(layer.ffn, f\"main_ffn_{i}\", self.logger)\n", "            register_nan_hook(layer.attn.wo, f\"main_attn_wo_{i}\", self.logger)\n", "        \n", "        for i, layer in enumerate(self.mtp_layers):\n", "            register_nan_hook(layer.ffn, f\"mtp_ffn_{i}\", self.logger)\n", "            register_nan_hook(layer.attn.wo, f\"mtp_attn_wo_{i}\", self.logger)\n", "        \n", "        for i, proj in enumerate(self.mtp_projections):\n", "            register_nan_hook(proj, f\"mtp_proj_{i}\", self.logger)\n", "        \n", "        register_nan_hook(self.head, \"head\", self.logger)\n", "    \n", "    def forward(self, tokens: Optional[torch.Tensor] = None, targets: Optional[torch.Tensor] = None, start_pos: int = 0):\n", "        \"\"\"\n", "        Forward pass for the Transformer model.\n", "\n", "        Args:\n", "            tokens (torch.Tensor): Input tensor of token IDs with shape (batch_size, seq_len).\n", "            start_pos (int, optional): Starting position in the sequence for rotary embeddings. Defaults to 0.\n", "\n", "        Returns:\n", "            torch.Tensor: Logits tensor of shape (batch_size, vocab_size).\n", "        \"\"\"\n", "        seqlen = tokens.size(1)\n", "        \n", "        self.logger.debug(f\"[DEBUG] input check: max={tokens.max().item()}, min={tokens.min().item()}, mean={tokens.float().mean().item()}\")\n", "        \n", "        #print_memory(\"🔹 Before embedding\")\n", "        h = self.embed(tokens)\n", "        self.logger.debug(f\"[DEBUG] embed weight stats: max={self.embed.weight.max().item()}, min={self.embed.weight.min().item()}, mean={self.embed.weight.mean().item()}\")\n", "        self.logger.debug(f\"[DEBUG] logits embd_h NaN: {torch.isnan(h).any()}\")\n", "        self.logger.debug(f\"[DEBUG] h after emb: max={h.max().item()}, min={h.min().item()}, mean={h.mean().item()}\")\n", "        #print_memory(\"🔹 After embedding\")\n", "        \n", "        freqs_cis = self.freqs_cis[start_pos:start_pos+seqlen]\n", "        mask = None\n", "        if seqlen > 1:\n", "            mask = torch.triu(torch.ones(seqlen, seqlen, dtype=torch.bool, device=tokens.device), diagonal=1)\n", "        \n", "        # ✅ Main model计算\n", "        # Decoderlayers输出结果\n", "        for i, layer in enumerate(self.layers):\n", "            h = layer(h, start_pos, freqs_cis, mask)\n", "            self.logger.debug(f\"[DEBUG] logits layer_mask_h NaN: {torch.isnan(h).any()}\")\n", "            self.logger.debug(f\"[DEBUG] h in {i} layer: max={h.max().item()}, min={h.min().item()}, mean={h.mean().item()}\")\n", "            #print_memory(f\"🔹 After layer {i}\")\n", "\n", "        self.logger.debug(f\"[DEBUG] h before norm: max={h.max().item()}, min={h.min().item()}, mean={h.mean().item()}\")\n", "        # 获取模型输出，结构为[bs, seq_len, D]\n", "        h = self.norm(h)  # [B, T, D]\n", "        self.logger.debug(f\"[DEBUG] logits norm_h NaN: {torch.isnan(h).any()}\")\n", "        self.logger.debug(f\"[DEBUG] h before head: max={h.max().item()}, min={h.min().item()}, mean={h.mean().item()}\")\n", "        logits = self.head(h)  # [B, T, V]\n", "        self.logger.debug(f\"[DEBUG] logits head_NaN: {torch.isnan(h).any()}\")\n", "        #print_memory(\"🔹 After main model\")\n", "        \n", "        mtp_logits = []\n", "        for k in range(self.n_mtp_depths):\n", "            if start_pos + k + 1 >= tokens.size(1):\n", "                break\n", "            h_k = h[:, :-k-1]  # [B, T-k-1, D]\n", "            self.logger.debug(f\"[DEBUG] mtp_index_h NaN: {torch.isnan(h_k).any()}\")\n", "            emb_k = self.embed(tokens[:, k+1:])  # [B, T-k-1, D]\n", "            self.logger.debug(f\"[DEBUG] mtp_emb_k NaN: {torch.isnan(emb_k).any()}\")\n", "            \n", "            norm_h = self.mtp_rms_norm1(h_k)\n", "            self.logger.debug(f\"[DEBUG] mtp_norm_h NaN: {torch.isnan(norm_h).any()}\")\n", "            norm_emb = self.mtp_rms_norm2(emb_k)\n", "            self.logger.debug(f\"[DEBUG] mtp_norm_k NaN: {torch.isnan(norm_emb).any()}\")\n", "            combined_h = torch.cat([norm_h, norm_emb], dim=-1)  # [B, T-k-1, 2D]\n", "            self.logger.debug(f\"[DEBUG] mtp_combined_h NaN: {torch.isnan(combined_h).any()}\")\n", "            \n", "            mtp_h = self.mtp_projections[k](combined_h)\n", "            mtp_h = torch.clamp(mtp_h, min=-20.0, max=20.0)\n", "            self.logger.debug(f\"[DEBUG] mtp_h NaN check after projection: {torch.isnan(mtp_h).any()}\")\n", "            self.logger.debug(f\"[DEBUG] mtp_h before head[{k}]: max={mtp_h.max().item()}, min={mtp_h.min().item()}, mean={mtp_h.mean().item()}\")\n", "            freqs_cis_k = self.freqs_cis[:mtp_h.size(1)]\n", "            mask_k = torch.triu(torch.ones(mtp_h.size(1), mtp_h.size(1), dtype=torch.bool, device=tokens.device), diagonal=1)\n", "            \n", "            mtp_h = self.mtp_layers[k](mtp_h, 0, freqs_cis_k, mask_k)\n", "            self.logger.debug(f\"[DEBUG] mtp_h NaN check after layer: {torch.isnan(mtp_h).any()}\")\n", "            #print_memory(f\"🔸 After MTP layer {k} - before head\")\n", "            mtp_logit = self.head(mtp_h)  # [B, T-k-1, V]\n", "            #print_memory(f\"🔸 After MTP layer {k} - after head\")\n", "            self.logger.debug(f\"[DEBUG] mtp_logits[{k}] stats: max={mtp_logit.max().item()}, min={mtp_logit.min().item()}, mean={mtp_logit.mean().item()}\")\n", "            if torch.isinf(mtp_logit).any():\n", "                self.logger.warning(f\"[Inf] ⚠️ mtp_logits[{k}] contains Inf! Saving inputs for debug...\")\n", "                torch.save({\n", "                    \"mtp_h\": mtp_h.detach().cpu(),\n", "                    \"head_weight\": self.head.weight.detach().cpu(),\n", "                    \"head_bias\": getattr(self.head, \"bias\", None).detach().cpu() if hasattr(self.head, \"bias\") else None\n", "                }, f\"debug_mtp_inf_k{k}.pt\")\n", "                \n", "            mtp_logits.append(mtp_logit)\n", "        \n", "        return logits, mtp_logits\n", "\n", "    @torch.inference_mode()\n", "    def generate(self, tokens: torch.Tensor, start_pos: int = 0):\n", "        \"\"\"\n", "        Forward pass for the Transformer model.\n", "\n", "        Args:\n", "            tokens (torch.Tensor): Input tensor of token IDs with shape (batch_size, seq_len).\n", "            start_pos (int, optional): Starting position in the sequence for rotary embeddings. Defaults to 0.\n", "\n", "        Returns:\n", "            torch.Tensor: Logits tensor of shape (batch_size, vocab_size).\n", "        \"\"\"\n", "        seqlen = tokens.size(1)\n", "        h = self.embed(tokens)\n", "        freqs_cis = self.freqs_cis[start_pos:start_pos+seqlen]\n", "        mask = None\n", "        if seqlen > 1:\n", "            mask = torch.full((seqlen, seqlen), float(\"-inf\"), device=tokens.device).triu_(1)\n", "        for layer in self.layers:\n", "            h = layer(h, start_pos, freqs_cis, mask)\n", "        h = self.norm(h)[:, -1]\n", "        logits = self.head(h)\n", "        return logits\n", "\n", "if __name__ == \"__main__\":\n", "    torch.set_default_dtype(torch.bfloat16)\n", "    torch.set_default_device(\"cuda\")\n", "    torch.manual_seed(0)\n", "    args = ModelArgs()\n", "    x = torch.randint(0, args.vocab_size, (2, 128))\n", "    model = Transformer(args)\n", "    print(model(x).size())\n", "\n", "```"]}, {"cell_type": "markdown", "id": "b0b2ac04-7871-479a-804a-6ee843bd3b93", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["### 9.4 Multi-token Prediction多步预测"]}, {"cell_type": "markdown", "id": "6456b462-cb20-4a6d-b60a-dcc3e0a3790f", "metadata": {}, "source": ["- **常规的MTP vs DeepSeek的MTP**"]}, {"cell_type": "markdown", "id": "9c7eb93d-63dd-4fe6-a4a5-15b51c669c37", "metadata": {}, "source": ["Multi-Token Prediction (MTP)** 指的是在一次前向传播中同时预测多个未来 token，相比于传统的 **自回归 (autoregressive) 单步预测**，MTP 允许模型在每个时间步输出多个 token，这就是说，在 Transformer 解码器的 **每个时间步 t**，模型不仅预测 **t+1 处的 token**，还会同时预测 **t+2, t+3, ..., t+D** 处的 token。具体地来说，我们会在每个预测位置使用 **独立的输出头 (independent output heads)**，每个输出头给出自己预测的token，这些输出头之间没有强制的依赖关系，这些预测的 token 彼此之间没有直接联系，因此**它们可能无法互相调整**。\n", "\n", "```\n", "设置D个输出头，然后——\n", "t=0  ->  预测 {t+1, t+2, ..., t+D}\n", "t=1  ->  预测 {t+2, t+3, ..., t+D+1}\n", "t=2  ->  预测 {t+3, t+4, ..., t+D+2}\n", "```\n", "DeepSeek的MTP采用的是 **顺序预测 MTP**，并且强调它 **保留了完整的因果链**。这是它们的关键创新点。具体流程如下：\n", "\n", "> - 在 Transformer 解码器的 **每个时间步 t**，模型仍然预测多个 token：\n", "  1. **首先预测 t+1 处的 token**（与自回归模型相同）。\n", "  2. **然后使用携带 t+1 预测信息的数据，再预测 t+2**。\n", "  3. **再使用携带 {t+1, t+2} 预测信息的数据，继续预测 t+3**，依此类推。"]}, {"cell_type": "markdown", "id": "50dd67ff-e150-433d-bb4b-ecc0df8b811f", "metadata": {}, "source": ["> - **所有额外预测的 token 之间存在依赖关系**，每个新预测的 token 都是基于先前的预测 **因果地** 生成的，而不是独立生成的。\n", "\n", "```\n", "t=0  ->  用[t-N,....t-1,t] 预测 {t+1}\n", "t=0  ->  用 t+1 时间点收集的预测信息 加上 [t-N+1,....t,t+1] 预测 {t+2}\n", "t=0  ->  用 {t+1, t+2} 时间点收集的预测信息、加上 [t-N+2,....t+1,t+2] 预测 {t+3}\n", "...\n", "```\n", "\n", "与传统 MTP **独立预测多个 token** 不同，DeepSeek 的 MTP 使得**额外预测的 token 之间具有依赖关系**，形成一个 **完整的因果链条**。这种方式加剧了前面的预测结果对后续预测结果的影响、对损失函数提出了更高的要求。\n", "\n", "**那具体是如何实现这种关联的呢？↓**\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/158.png)"]}, {"cell_type": "markdown", "id": "dfa51cc6-2d14-4c5c-a75d-d4ddddb921cf", "metadata": {}, "source": ["- **MTP的实现**"]}, {"cell_type": "markdown", "id": "a3fb39d3-493b-4203-9fb4-aa0a90b09fb0", "metadata": {}, "source": ["```python\n", "\n", "    def forward(self, tokens: Optional[torch.Tensor] = None, targets: Optional[torch.Tensor] = None, start_pos: int = 0):\n", "        \"\"\"\n", "        Forward pass for the Transformer model.\n", "\n", "        Args:\n", "            tokens (torch.Tensor): Input tensor of token IDs with shape (batch_size, seq_len).\n", "            start_pos (int, optional): Starting position in the sequence for rotary embeddings. Defaults to 0.\n", "\n", "        Returns:\n", "            torch.Tensor: Logits tensor of shape (batch_size, vocab_size).\n", "        \"\"\"\n", "        seqlen = tokens.size(1)\n", "        \n", "        self.logger.debug(f\"[DEBUG] input check: max={tokens.max().item()}, min={tokens.min().item()}, mean={tokens.float().mean().item()}\")\n", "        \n", "        #print_memory(\"🔹 Before embedding\")\n", "        h = self.embed(tokens)\n", "        \n", "        freqs_cis = self.freqs_cis[start_pos:start_pos+seqlen]\n", "        mask = None\n", "        if seqlen > 1:\n", "            mask = torch.triu(torch.ones(seqlen, seqlen, dtype=torch.bool, device=tokens.device), diagonal=1)\n", "        \n", "        # ✅ Main model计算\n", "        # Decoderlayers输出结果\n", "        for i, layer in enumerate(self.layers):\n", "            h = layer(h, start_pos, freqs_cis, mask)\n", "\n", "        # 获取模型输出，结构为[bs, seq_len, D]\n", "        h = self.norm(h)  # [B, T, D]\n", "        logits = self.head(h)  # [B, T, V]\n", "        \n", "        mtp_logits = []\n", "        for k in range(self.n_mtp_depths):\n", "            if start_pos + k + 1 >= tokens.size(1):\n", "                break\n", "            h_k = h[:, :-k-1]  # [B, T-k-1, D]\n", "            emb_k = self.embed(tokens[:, k+1:])  # [B, T-k-1, D]\n", "            \n", "            norm_h = self.mtp_rms_norm1(h_k)\n", "            norm_emb = self.mtp_rms_norm2(emb_k)\n", "            combined_h = torch.cat([norm_h, norm_emb], dim=-1)  # [B, T-k-1, 2D]\n", "            \n", "            mtp_h = self.mtp_projections[k](combined_h)\n", "            mtp_h = torch.clamp(mtp_h, min=-20.0, max=20.0)\n", "            freqs_cis_k = self.freqs_cis[:mtp_h.size(1)]\n", "            mask_k = torch.triu(torch.ones(mtp_h.size(1), mtp_h.size(1), dtype=torch.bool, device=tokens.device), diagonal=1)\n", "            \n", "            mtp_h = self.mtp_layers[k](mtp_h, 0, freqs_cis_k, mask_k)\n", "            mtp_logit = self.head(mtp_h)  # [B, T-k-1, V]\n", "            self.logger.debug(f\"[DEBUG] mtp_logits[{k}] stats: max={mtp_logit.max().item()}, min={mtp_logit.min().item()}, mean={mtp_logit.mean().item()}\")\n", "            if torch.isinf(mtp_logit).any():\n", "                self.logger.warning(f\"[Inf] ⚠️ mtp_logits[{k}] contains Inf! Saving inputs for debug...\")\n", "                torch.save({\n", "                    \"mtp_h\": mtp_h.detach().cpu(),\n", "                    \"head_weight\": self.head.weight.detach().cpu(),\n", "                    \"head_bias\": getattr(self.head, \"bias\", None).detach().cpu() if hasattr(self.head, \"bias\") else None\n", "                }, f\"debug_mtp_inf_k{k}.pt\")\n", "                \n", "            mtp_logits.append(mtp_logit)\n", "        \n", "        return logits, mtp_logits\n", "\n", "    @torch.inference_mode()\n", "    def generate(self, tokens: torch.Tensor, start_pos: int = 0):\n", "        \"\"\"\n", "        Forward pass for the Transformer model.\n", "\n", "        Args:\n", "            tokens (torch.Tensor): Input tensor of token IDs with shape (batch_size, seq_len).\n", "            start_pos (int, optional): Starting position in the sequence for rotary embeddings. Defaults to 0.\n", "\n", "        Returns:\n", "            torch.Tensor: Logits tensor of shape (batch_size, vocab_size).\n", "        \"\"\"\n", "        seqlen = tokens.size(1)\n", "        h = self.embed(tokens)\n", "        freqs_cis = self.freqs_cis[start_pos:start_pos+seqlen]\n", "        mask = None\n", "        if seqlen > 1:\n", "            mask = torch.full((seqlen, seqlen), float(\"-inf\"), device=tokens.device).triu_(1)\n", "        for layer in self.layers:\n", "            h = layer(h, start_pos, freqs_cis, mask)\n", "        h = self.norm(h)[:, -1]\n", "        logits = self.head(h)\n", "        return logits\n", "```"]}, {"cell_type": "markdown", "id": "3bdebf7c-6500-4b50-98d8-e2876202825d", "metadata": {}, "source": ["### 9.5 预训练经验与debug流程详谈"]}, {"cell_type": "markdown", "id": "b4b171d0-5069-4df8-b8ea-ee95bb5386fa", "metadata": {}, "source": ["- 9.5.1 预训练经验做简历项目，怎样才更真实？\n", "- 9.5.2 数据bug(1)：应该如何选择适合的tokenizer？\n", "- 9.5.3 数据bug(2)：分词后数据大小激增如何应对？\n", "- 9.5.4 模型bug(1)：开源推理模型用于训练都有哪些陷阱？\n", "- 9.5.5 模型bug(2)：多机多卡真的启用了吗？如何拉满GPU效率？\n", "- 9.5.6 模型bug(3)：落地debug工具与代码有哪些？\n", "- 9.5.7 模型bug(4)：如何修复系统性梯度问题？\n", "- 9.5.8 模型bug(5)：训练时间太长应该如何改善？"]}, {"cell_type": "markdown", "id": "903c9f9e-fd02-40fc-8336-3a1b83b58a60", "metadata": {}, "source": ["## 10 FSDP多机多卡分布式训练代码一览"]}, {"cell_type": "markdown", "id": "4810fa8d-564d-4224-9a85-4e4459ba521f", "metadata": {}, "source": ["```python\n", "### 📦 1. 基础引入和路径设置 ###\n", "import glob\n", "import math\n", "import sys\n", "import time\n", "from pathlib import Path\n", "from typing import Optional, Tuple, Union\n", "import os\n", "import lightning as L\n", "import torch\n", "\n", "from lightning.fabric.strategies import FSDPStrategy  # ✅ 使用 FSDP 策略\n", "from torch.utils.data import DataLoader\n", "from functools import partial\n", "\n", "# 添加工程目录到路径中，支持模块 import\n", "wd = Path(__file__).parent.parent.resolve()\n", "sys.path.append(str(wd))\n", "\n", "from transformers import AutoConfig\n", "from lit_gpt.packed_dataset import create_dataloader\n", "from lit_gpt.speed_monitor import SpeedMonitorFabric as Monitor\n", "from lit_gpt.speed_monitor import estimate_flops\n", "from lit_gpt.utils import get_default_supported_precision, num_parameters, step_csv_logger\n", "from pytorch_lightning.loggers import WandbLogger\n", "from lit_gpt.triton_cross_entropy import TritonCrossEntropyLoss\n", "from loguru import logger\n", "\n", "# ✅ 加载自定义模型（例如 Steel 模型）\n", "parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\n", "sys.path.append(os.path.join(parent_dir, \"model\", \"steel_modify_from_qwen_1_5\"))\n", "from modeling_steel import SteelForCausalLM, SteelDecoderLayer\n", "from steel_llm_utils import compatible_tiny_llama_config\n", "\n", "### 📁 2. 配置与训练参数设置 ###\n", "name = \"steel_llm\"\n", "out_dir = Path(\"/data/gu_data/ckpt\") / name\n", "TRAIN_DATA_DIR = Path(\"/data/gu_data/step3_input\")\n", "MODEL_PATH = \"../model/steel_modify_from_qwen_1_5\"\n", "BLOCK_SIZE = 2048\n", "RESUME = Path(\"/data/gu_data/ckpt/steel_llm/step-860000-iter-6880000-ckpt\")\n", "ONLY_RESUME_MODEL = False\n", "ADD_NEW_DATA_DIR = None\n", "IGNORE_INDEX = 151643\n", "USE_FLASH_ATTN = True\n", "\n", "### ✅ 多卡训练核心参数设置\n", "num_of_devices = 8  # ✅ 设置 GPU 数量\n", "micro_batch_size = 8\n", "global_batch_size = 64 * num_of_devices  # ✅ 全局 batch 是所有卡的 batch 总和\n", "gradient_accumulation_steps = global_batch_size // (micro_batch_size * num_of_devices)  # ✅ 梯度累积\n", "\n", "### 学习率调度参数\n", "learning_rate = 3e-4\n", "max_step = 430000 * 2 + 220000\n", "warmup_steps = 1000\n", "log_step_interval = 20\n", "eval_step_interval = 20000\n", "save_step_interval = 20000\n", "\n", "### 日志与监控工具\n", "hparams = {k: v for k, v in locals().items() if isinstance(v, (int, float, str)) and not k.startswith(\"_\")}\n", "logger = step_csv_logger(\"out\", name)\n", "wandb_logger = WandbLogger()\n", "\n", "### 🛠️ 3. FSDP 模型训练启动逻辑 ###\n", "def setup(devices: int = num_of_devices, ...):\n", "    precision = precision or get_default_supported_precision(training=True)\n", "\n", "    config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)\n", "\n", "    # ✅ 多卡使用 FSDP 策略\n", "    if devices > 1:\n", "        strategy = FSDPStrategy(\n", "            auto_wrap_policy={SteelDecoderLayer},  # ✅ 按 Transformer Block 自动 wrap\n", "            activation_checkpointing_policy=None,\n", "            state_dict_type=\"full\",\n", "            limit_all_gathers=True,\n", "            cpu_offload=False,\n", "        )\n", "    else:\n", "        strategy = \"auto\"\n", "\n", "    # ✅ 使用 Lightning Fabric 构建训练环境\n", "    fabric = <PERSON>.Fabric(devices=devices, strategy=strategy, precision=precision, loggers=[logger, wandb_logger])\n", "\n", "    config.model_path = model_path\n", "    config.use_flash_attn = USE_FLASH_ATTN\n", "    config = compatible_tiny_llama_config(config, block_size)\n", "\n", "    if devices > 1:\n", "        fabric.launch(main, train_data_dir, val_data_dir, resume, config, only_resume_model)  # ✅ 启动多进程训练\n", "    else:\n", "        main(fabric, train_data_dir, val_data_dir, resume, config, only_resume_model)\n", "\n", "\n", "### 🚀 4. 主训练流程逻辑（包含 resume、数据加载、模型初始化） ###\n", "def main(fabric, train_data_dir, val_data_dir, resume, config, only_resume_model):\n", "    monitor = Monitor(fabric)\n", "    if fabric.global_rank == 0:\n", "        out_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    # ✅ 创建多进程 dataloader\n", "    train_dataloader, val_dataloader, train_datasets, val_datasets = create_dataloaders(...)\n", "\n", "    fabric.seed_everything(3407)\n", "\n", "    # ✅ 初始化模型结构\n", "    with fabric.init_module(empty_init=False):\n", "        model = SteelForCausalLM(config)\n", "        model.apply(model._init_weights)\n", "\n", "    # ✅ 参数统计与 FLOPs 估算\n", "    fabric.print(f\"Total parameters {num_parameters(model):,}\")\n", "    with torch.device(\"meta\"):\n", "        meta_model = SteelForCausalLM(config)\n", "        config.estimated_flops = estimate_flops(meta_model) * micro_batch_size\n", "\n", "    # ✅ 设置 optimizer 与 FSDP 组网\n", "    model = fabric.setup(model)\n", "    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.05)\n", "    optimizer = fabric.setup_optimizers(optimizer)\n", "\n", "    # ✅ 从 checkpoint 恢复模型状态\n", "    state = {\"model\": model, \"optimizer\": optimizer, \"iter_num\": 0, \"step_count\": 0}\n", "    if resume or only_resume_model:\n", "        state_dir = resume / \"state.pth\" if resume else only_resume_model / \"state.pth\"\n", "        fabric.load(state_dir, state)\n", "        fabric.print(f\"Resumed from {state_dir}\")\n", "\n", "    train(fabric, state, train_dataloader, val_dataloader, monitor, resume, config, train_datasets, model)\n", "\n", "\n", "### 🧪 5. 训练循环核心代码（包含梯度同步与日志记录） ###\n", "def train(fabric, state, train_dataloader, val_dataloader, monitor, resume, config, train_datasets, huggingface_format_model):\n", "    model, optimizer = state[\"model\"], state[\"optimizer\"]\n", "    loss_func = TritonCrossEntropyLoss(ignore_index=IGNORE_INDEX)\n", "\n", "    for train_data in train_dataloader:\n", "        if state[\"iter_num\"] >= max_step:\n", "            break\n", "\n", "        input_ids = train_data[:, :config.block_size]\n", "        targets = train_data[:, 1:config.block_size+1]\n", "\n", "        is_accumulating = (state[\"iter_num\"] + 1) % gradient_accumulation_steps != 0\n", "        with fabric.no_backward_sync(model, enabled=is_accumulating):  # ✅ 支持多卡的梯度累积\n", "            logits = model(input_ids).logits\n", "            loss = loss_func(logits, targets)\n", "            fabric.backward(loss / gradient_accumulation_steps)\n", "\n", "        if not is_accumulating:\n", "            fabric.clip_gradients(model, optimizer, max_norm=1.0)\n", "            optimizer.step()\n", "            optimizer.zero_grad()\n", "            state[\"step_count\"] += 1\n", "\n", "        state[\"iter_num\"] += 1\n", "        monitor.on_train_batch_end(...)\n", "\n", "        # ✅ 多卡保存 checkpoint，只主进程保存\n", "        if not is_accumulating and state[\"step_count\"] % save_step_interval == 0:\n", "            if fabric.global_rank == 0:\n", "                ckpt_path = out_dir / f\"step-{state['step_count']:06d}-iter-{state['iter_num']:06d}-ckpt\"\n", "                os.makedirs(ckpt_path, exist_ok=True)\n", "                fabric.save(ckpt_path / \"state.pth\", state)\n", "            fabric.barrier()  # ✅ 多进程间同步\n", "\n", "\n", "### 🔍 6. 验证函数（用于 eval loss） ###\n", "@torch.no_grad()\n", "def validate(fabric: <PERSON><PERSON>, model: torch.nn.<PERSON>, val_dataloader: DataLoader) -> torch.Tensor:\n", "    model.eval()\n", "    losses = []\n", "    for val_data in val_dataloader:\n", "        input_ids = val_data[:, :model.config.block_size]\n", "        targets = val_data[:, 1:model.config.block_size+1]\n", "        logits = model(input_ids)\n", "        loss = TritonCrossEntropyLoss()(logits, targets)\n", "        losses.append(loss.item())\n", "    model.train()\n", "    return torch.tensor(losses).mean()\n", "\n", "\n", "### 🧵 7. CLI 接口启动器 ###\n", "if __name__ == \"__main__\":\n", "    from jsonargparse import CLI\n", "    CLI(setup)\n", "```"]}, {"cell_type": "markdown", "id": "ee2acd75-f4c4-42e6-a2a8-a609b19bd38a", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## 附录"]}, {"cell_type": "markdown", "id": "d6e2dc4f-a7b4-49ee-acda-d4714faab43a", "metadata": {}, "source": ["- **文本数据高频清洗算子**\n", "\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Filter** | `character_repetition_filter` | 过滤字符重复率过高的文本 |\n", "| **Filter** | `flagged_words_filter`        | 过滤包含特定敏感词的文本 |\n", "| **Filter** | `perplexity_filter`           | 过滤困惑度（Perplexity）过高的文本 |\n", "| **Filter** | `special_characters_filter`   | 过滤特殊字符占比过高的文本 |\n", "| **Filter** | `text_length_filter`          | 过滤文本长度不符合设定范围的样本 |\n", "| **Filter** | `token_num_filter`            | 过滤标记（token）数目不符合设定范围的文本 |\n", "| **Filter** | `word_repetition_filter`      | 过滤词重复率过高的文本 |\n", "| **Filter** | `words_num_filter`            | 过滤单词数量不符合设定范围的文本 |\n", "| **Deduplicator** | `document_deduplicator`           | 基于内容的文档去重，移除重复或相似的文本文档 |\n", "| **Deduplicator** | `document_minhash_deduplicator`   | 通过 MinHash 技术检测并去除相似文档 |\n", "| **Deduplicator** | `document_simhash_deduplicator`  | 通过 SimHash 技术检测并去除相似文档 |\n", "| **Mapper** | `clean_html_mapper`                   | 清理文本中的 HTML 标签和格式 |\n", "| **Mapper** | `chinese_convert_mapper`              | 进行简繁体和日文汉字转换 |\n", "| **Mapper** | `clean_links_mapper`                  | 删除文本中的超链接 |\n", "| **Mapper** | `fix_unicode_mapper`                  | 修正 Unicode 编码错误 |\n", "| **Mapper** | `punctuation_normalization_mapper`    | 进行标点符号规范化 |\n", "| **Mapper** | `remove_non_chinese_character_mapper` | 删除非中文字符 |\n", "| **Mapper** | `remove_repeat_sentences_mapper`      | 删除重复句子 |\n", "| **Mapper** | `remove_comments_mapper`              | 移除文本中的注释 |\n", "| **Mapper** | `whitespace_normalization_mapper`     | 进行空白字符标准化 |\n", "\n", "---\n", "\n", "- **文本数据其他常见清洗算子**\n", "\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Filter** | `alphanumeric_filter`         | 过滤字母/数字比例不在特定范围内的文本 |\n", "| **Filter** | `average_line_length_filter`  | 过滤平均行长超出范围的文本 |\n", "| **Filter** | `language_id_score_filter`    | 过滤语言识别得分过低的文本 |\n", "| **Filter** | `maximum_line_length_filter`  | 过滤最大行长超过设定值的文本 |\n", "| **Filter** | `stopwords_filter`            | 过滤包含过多停用词的文本 |\n", "| **Filter** | `text_pair_similarity_filter` | 过滤文本对相似度过低的样本 |\n", "| **Mapper** | `remove_long_words_mapper`            | 删除超长单词 |\n", "| **Mapper** | `remove_specific_chars_mapper`        | 移除特定字符 |\n", "| **Mapper** | `clean_email_mapper`                  | 移除文本中的电子邮件地址 |\n", "| **Deduplicator** | `ray_document_deduplicator`      | 基于 Ray 并行计算框架进行文档去重 |\n", "| **Deduplicator** | `ray_bts_minhash_deduplicator`   | 结合 Ray 和 MinHash 算法进行大规模文档去重 |\n", "| **Common** | `special_characters`         | 处理特殊字符的转换、清理和替换 |\n", "\n", "- **相对冷门的文本清洗算子**\n", "\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Filter** | `suffix_filter`               | 过滤特定后缀的文件或文本 |\n", "| **Filter** | `text_action_filter`          | 过滤包含特定操作或动词的文本 |\n", "| **Filter** | `text_entity_dependency_filter` | 过滤特定实体依赖结构的文本 |\n", "| **Filter** | `specified_field_filter`      | 过滤特定字段不符合要求的样本 |\n", "| **Filter** | `specified_numeric_field_filter` | 过滤数值字段不符合要求的样本 |\n", "| **Deduplicator** | `ray_basic_deduplicator`         | 使用 Ray 进行并行处理的基本去重算子 |\n", "| **Common** | `helper_func`                | 提供通用的辅助函数，支持不同算子的计算和数据处理 |\n", "| **Mapper** | `clean_copyright_mapper`              | 删除文本中的版权声明 |\n", "| **Mapper** | `clean_ip_mapper`                     | 移除文本中的 IP 地址 |\n", "| **Mapper** | `python_file_mapper`                  | 处理 Python 文件格式 |\n", "| **Mapper** | `python_lambda_mapper`                | 处理 Python Lambda 表达式 |\n", "| **Mapper** | `remove_bibliography_mapper`          | 移除文献引用部分 |\n", "| **Mapper** | `remove_header_mapper`                | 删除文本中的页眉信息 |\n", "| **Mapper** | `remove_table_text_mapper`            | 删除表格文本 |\n", "| **Mapper** | `remove_words_with_incorrect_substrings_mapper` | 删除包含错误子串的单词 |\n", "| **Mapper** | `replace_content_mapper`              | 替换文本内容 |\n", "\n", "---\n", "    \n", "- **文本的数据增强算子**\n", "\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Common** | `prompt2prompt_pipeline`     | 处理 `Prompt-to-Prompt` 任务，实现基于文本的提示词转换 |\n", "| **Grouper** | `key_value_grouper`      | 根据键值对对数据进行分组，适用于结构化数据处理 |\n", "| **Grouper** | `naive_grouper`          | 采用简单的规则将数据进行分组处理 |\n", "| **Grouper** | `naive_reverse_grouper`  | 反向分组算子，将之前分组的数据恢复为原始形式 |\n", "| **Mapper** | `sdxl_prompt2prompt_mapper`           | SDXL 模型的 Prompt-to-Prompt 处理 |\n", "| **Mapper** | `calibrate_qa_mapper`                 | 重新校准问答数据，提高问答质量 |\n", "| **Mapper** | `calibrate_query_mapper`              | 校准用户查询，使其更加标准化 |\n", "| **Mapper** | `calibrate_response_mapper`           | 调整模型生成的响应，使其更符合预期 |\n", "| **Mapper** | `dialog_intent_detection_mapper`      | 识别对话中的意图 |\n", "| **Mapper** | `dialog_sentiment_detection_mapper`   | 进行对话的情感分析 |\n", "| **Mapper** | `dialog_sentiment_intensity_mapper`   | 计算对话情感强度 |\n", "| **Mapper** | `dialog_topic_detection_mapper`       | 识别对话主题 |\n", "| **Mapper** | `expand_macro_mapper`                 | 展开 LaTeX 文档中的宏定义 |\n", "| **Mapper** | `extract_entity_attribute_mapper`     | 提取实体及其属性信息 |\n", "| **Mapper** | `extract_entity_relation_mapper`      | 抽取实体关系数据 |\n", "| **Mapper** | `extract_event_mapper`                | 从文本中提取事件信息 |\n", "| **Mapper** | `extract_keyword_mapper`              | 提取文本中的关键词 |\n", "| **Mapper** | `extract_nickname_mapper`             | 提取昵称或别名信息 |\n", "| **Mapper** | `extract_support_text_mapper`         | 提取文本中支持某观点的内容 |\n", "| **Mapper** | `generate_qa_from_examples_mapper`    | 从示例生成 QA 数据 |\n", "| **Mapper** | `generate_qa_from_text_mapper`        | 从文本内容生成 QA 数据 |\n", "| **Mapper** | `nlpaug_en_mapper`                    | 进行英文 NLP 数据增强 |\n", "| **Mapper** | `nlpcda_zh_mapper`                    | 进行中文 NLP 数据增强 |\n", "| **Mapper** | `optimize_qa_mapper`                  | 优化 QA 数据 |\n", "| **Mapper** | `optimize_query_mapper`               | 优化查询文本 |\n", "| **Mapper** | `optimize_response_mapper`            | 优化模型响应文本 |\n", "| **Mapper** | `pair_preference_mapper`              | 处理成对偏好数据 |\n", "| **Mapper** | `query_intent_detection_mapper`       | 识别查询意图 |\n", "| **Mapper** | `query_sentiment_detection_mapper`    | 进行查询的情感分析 |\n", "| **Mapper** | `query_topic_detection_mapper`        | 识别查询主题 |\n", "| **Mapper** | `relation_identity_mapper`            | 识别关系身份 |\n", "| **Mapper** | `sentence_augmentation_mapper`        | 进行句子级别的数据增强 |\n", "| **Mapper** | `sentence_split_mapper`               | 句子拆分 |\n", "| **Mapper** | `text_chunk_mapper`                   | 进行文本切片 |\n", "\n", "---\n", "\n", "- **图像数据数据清洗算子**\n", "\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Filter** | `image_aesthetics_filter`     | 过滤美学质量较低的图片 |\n", "| **Filter** | `image_aspect_ratio_filter`   | 过滤长宽比不符合要求的图片 |\n", "| **Filter** | `image_face_count_filter`     | 过滤人脸数量不在设定范围内的图片 |\n", "| **Filter** | `image_face_ratio_filter`     | 过滤人脸占比过高或过低的图片 |\n", "| **Filter** | `image_nsfw_filter`           | 过滤包含不适宜内容（NSFW）的图片 |\n", "| **Filter** | `image_pair_similarity_filter` | 过滤两张图片相似度过高的样本 |\n", "| **Filter** | `image_shape_filter`          | 过滤尺寸不符合要求的图片 |\n", "| **Filter** | `image_size_filter`           | 过滤文件大小超出设定值的图片 |\n", "| **Filter** | `image_watermark_filter`      | 过滤带有水印的图片 |\n", " | **Deduplicator** | `ray_image_deduplicator`         | 使用 Ray 进行大规模图片去重 |\n", " | **Deduplicator** | `image_deduplicator`             | 基于视觉特征对图片进行去重，移除重复图像 |\n", " | **Deduplicator** | `ray_basic_deduplicator`         | 使用 Ray 进行并行处理的基本去重算子 |\n", " | **Common** | `helper_func`                | 提供通用的辅助函数，支持不同算子的计算和数据处理 |\n", "\n", "---\n", "\n", "- **图像数据数据增强算子**\n", "\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Mapper** | `image_blur_mapper`                   | 处理图像模糊化 |\n", "| **Mapper** | `image_captioning_from_gpt4v_mapper`  | 使用 GPT-4V 生成图像描述 |\n", "| **Mapper** | `image_captioning_mapper`             | 生成图像描述 |\n", "| **Mapper** | `image_diffusion_mapper`              | 生成图像扩散特效 |\n", "| **Mapper** | `image_face_blur_mapper`              | 进行人脸模糊化处理 |\n", "| **Mapper** | `image_remove_background_mapper`      | 移除图像背景 |\n", "| **Mapper** | `image_segment_mapper`                | 进行图像分割 |\n", "| **Mapper** | `image_tagging_mapper`                | 给图像添加标签 |\n", "\n", "---\n", "\n", "- **音频数据数据清洗算子**\n", " \n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Mapper** | `audio_ffmpeg_wrapped_mapper`          | 使用 FFmpeg 处理音频，如转换格式、降噪等 |\n", "| **Mapper** | `video_captioning_from_audio_mapper`  | 从音频生成视频字幕 |\n", "\n", "---\n", "\n", "- **视频数据算子**\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Filter** | `video_aesthetics_filter`     | 过滤美学质量较低的视频 |\n", "| **Filter** | `video_aspect_ratio_filter`   | 过滤长宽比不符合要求的视频 |\n", "| **Filter** | `video_duration_filter`       | 过滤时长不符合要求的视频 |\n", "| **Filter** | `video_motion_score_filter`   | 过滤运动评分过低的视频 |\n", "| **Filter** | `video_motion_score_raft_filter` | 过滤基于 RAFT 计算的运动评分过低的视频 |\n", "| **Filter** | `video_nsfw_filter`           | 过滤包含不适宜内容（NSFW）的视频 |\n", "| **Filter** | `video_ocr_area_ratio_filter` | 过滤 OCR 文本区域占比过高或过低的视频 |\n", "| **Filter** | `video_resolution_filter`     | 过滤分辨率不符合要求的视频 |\n", "| **Filter** | `video_watermark_filter`      | 过滤带有水印的视频 |\n", "| **Deduplicator** | `ray_video_deduplicator`         | 使用 Ray 进行大规模视频去重 |\n", "| **Deduplicator** | `video_deduplicator`             | 基于视觉特征对视频进行去重，移除重复视频 |\n", "| **Deduplicator** | `ray_basic_deduplicator`         | 使用 Ray 进行并行处理的基本去重算子 |\n", "| **Common** | `helper_func`                | 提供通用的辅助函数，支持不同算子的计算和数据处理 |\n", "| **Mapper** | `video_remove_watermark_mapper`       | 去除视频水印 |\n", "| **Mapper** | `video_resize_aspect_ratio_mapper`    | 调整视频长宽比 |\n", "| **Mapper** | `video_resize_resolution_mapper`      | 调整视频分辨率 |\n", "| **Mapper** | `video_split_by_duration_mapper`      | 根据时间切割视频 |\n", "| **Mapper** | `video_split_by_key_frame_mapper`     | 根据关键帧切割视频 |\n", "| **Mapper** | `video_split_by_scene_mapper`         | 根据场景切割视频 |\n", "| **Mapper** | `video_extract_frames_mapper`         | 从视频中提取帧 |\n", "| **Mapper** | `video_face_blur_mapper`              | 进行视频人脸模糊处理 |\n", "| **Mapper** | `video_ffmpeg_wrapped_mapper`         | 使用 FFmpeg 处理视频 |\n", "\n", "---\n", "\n", "- **多模态数据算子**\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Mapper** | `mllm_mapper`                         | 多模态大模型数据处理 |\n", "| **Filter** | `image_text_matching_filter`  | 过滤图像和文本匹配度过低的样本 |\n", "| **Filter** | `image_text_similarity_filter` | 过滤图像和文本相似度过低的样本 |\n", "| **Filter** | `phrase_grounding_recall_filter` | 过滤短语对齐召回率较低的文本 |\n", "| **Filter** | `video_frames_text_similarity_filter` | 过滤视频帧与文本相似度过低的样本 |\n", "| **Filter** | `video_tagging_from_frames_filter` | 过滤视频帧标签不符合要求的视频 |\n", "| **Common** | `helper_func`                | 提供通用的辅助函数，支持不同算子的计算和数据处理 |\n", "| **Mapper** | `video_captioning_from_frames_mapper` | 从视频帧生成字幕 |\n", "| **Mapper** | `video_captioning_from_summarizer_mapper` | 从摘要生成视频字幕 |\n", "| **Mapper** | `video_captioning_from_video_mapper`  | 直接从视频生成字幕 |\n", "| **Mapper** | `video_tagging_from_audio_mapper`     | 从音频为视频生成标签 |\n", "| **Mapper** | `video_tagging_from_frames_mapper`    | 从帧中提取视频标签 |\n", "\n", "---\n", "\n", "- **其他通用算子**\n", "| 算子类型 | 具体操作名称                          | 功能描述 |\n", "|---------|---------------------------------|-------------------------------------------|\n", "| **Selector** | `__init__.py`  | Selector 模块的初始化文件 |\n", "| **Selector** | `base_op.py`   | 提供 Selector 的基础操作接口 |\n", "| **Selector** | `load.py`      | 负责加载和管理数据选择操作 |\n", "| **Selector** | `op_fusion.py` | 进行算子融合，优化多个 Selector 操作的执行效率 |"]}, {"cell_type": "markdown", "id": "343a14e1-5f99-4c48-8cdf-828afb42e6c8", "metadata": {}, "source": ["apt update && apt install -y lsof\n", "\n", "lsof | grep deleted\n", "\n", "pkill -9 python"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}