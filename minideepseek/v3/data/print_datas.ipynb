{"cells": [{"cell_type": "markdown", "id": "4c1070c2-bd12-4572-b9a6-09f6f3a40d47", "metadata": {}, "source": ["### 读取展示各个文件数据"]}, {"cell_type": "markdown", "id": "c628da95-5bcd-41a8-a33a-01a1ff4647c8", "metadata": {}, "source": ["- **skypile**"]}, {"cell_type": "code", "execution_count": 1, "id": "c376bd8a-8718-4063-944e-aa83dbccaf83", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"text\": \"近日，由微软(中国)有限公司携手实验室合作伙伴张江集团打造的微软人工智能和物联网实验室，即微软第四个全球实验室在张江人工智能岛正式启用并投入运营。蜜度信息(媒体大数据专家)同ABB、泛亚汽车(大型跨国企业)等30家企业，一道成为实验室首批赋能企业。\\n微软人工智能和物联网实验室，是微软为推动人工智能和物联网解决方案及应用的创新、研发和产业化而专门设立的全球性研发机构，旨在通过为企业和合作伙伴开发物联网产品和解决方案提供全方位支持，切实推动技术创新与制造、零售、医疗、金融、城市建设等行业数字化转型的深度融合和创新发展。\\n据了解，包括蜜度信息在内的首批30家被微软人工智能和物联网实验室赋能的企业，将在未来3-6个月得到微软的全方位支持，包括实验室提供的硬件及软件资源、微软云服务资源、接入微软生态系统与大中小产业资源实现联动等，同时这些被赋能的企业也将享受张江集团提供的创新及投资服务支持。\\n蜜度信息，作为新浪微博投资的媒体大数据公司，每日采集微博、微信、新闻、报刊、政务、外媒、博客、论坛、视频、网站、客户端等全网11大信息源，其中，且独家拥有新浪微博全量政务舆情数据。据统计，蜜度信息日新增1亿+条数据。基于海量数据，蜜度信息通过产、学、研合作的方式，与不同机构分别推出了网络传播热度指数、情绪地图、新媒体账号影响力排行榜等一系列颇有影响力的产品。而接下来，在微软人工智能和物联网实验室的加持之下，蜜度信息也将进一步发挥其在数据采集与自然语言处理上全快准稳的优势，成为国内媒体大数据应用领域的佼佼者。（完）\\n\"\n", "}\n"]}], "source": ["import json\n", "\n", "# 指定 JSONL 文件路径\n", "file_path = \"/root/autodl-tmp/minideepseek/v3/data/skypile/data/2022-40_zh_head_0000.jsonl\"\n", "\n", "# 读取 JSONL 文件的第一行\n", "with open(file_path, \"r\", encoding=\"utf-8\") as file:\n", "    first_line = file.readline().strip()  # 读取第一行并去掉首尾空格或换行符\n", "    first_json = json.loads(first_line)  # 解析 JSON 格式\n", "\n", "# 打印第一行内容\n", "print(json.dumps(first_json, indent=4, ensure_ascii=False))  # 格式化打印 JSON 数据"]}, {"cell_type": "markdown", "id": "4c358932-051a-486e-9171-1e20efd2f180", "metadata": {}, "source": ["- **starcoder**"]}, {"cell_type": "code", "execution_count": 6, "id": "e7627088-4137-41d5-a93e-28f2e056ef22", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['max_stars_repo_path', 'max_stars_repo_name', 'max_stars_count', 'id', 'content'], dtype='object')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 指定 Parquet 文件路径\n", "file_path = \"/root/autodl-tmp/minideepseek/v3/data/starcoder/python/train-00003-of-00059.parquet\"\n", "\n", "# 读取 Parquet 文件\n", "df = pd.read_parquet(file_path, engine=\"pyarrow\")\n", "\n", "# 设置 Pandas 选项，确保完整显示内容\n", "pd.set_option(\"display.max_columns\", None)  # 显示所有列\n", "pd.set_option(\"display.max_colwidth\", None)  # 显示所有单元格内容\n", "pd.set_option(\"display.width\", 1000)  # 设置输出宽度，防止换行\n", "\n", "# 展示表格结构\n", "\n", "df.columns"]}, {"cell_type": "code", "execution_count": 7, "id": "69be9058-51a4-4595-bac0-b4065bb9cdfb", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"from functools import partial\\nfrom utils.fmap_visualize import FeatureMapVis_siamese,FeatureMapVis\\nfrom pathlib import Path\\nfrom utils.fmap_visualize import show_tensor, show_img\\nimport numpy as np\\nimport os\\nimport cv2\\nimport torch\\nimport torchvision\\n#from models.Models_tmp import SiameseCGNet_v2\\n\\ndef forward(self, img, img_metas=None, return_loss=False, **kwargs):\\n    outs = self.forward(img)\\n    return outs\\n\\n\\ndef create_featuremap_vis(model=None, use_gpu=True, init_shape=(768, 768, 3)):\\n    #model.forward = partial(forward, model)\\n    featurevis = FeatureMapVis_siamese(model, use_gpu)\\n    featurevis.set_hook_style(init_shape[2], init_shape[:2])\\n    return featurevis\\n\\n\\ndef _show_save_data(featurevis, input, img_orig, feature_indexs, filepath, is_show, output_dir):\\n    show_datas = []\\n    for feature_index in feature_indexs:\\n        feature_map = featurevis.run(input, feature_index=feature_index)[0]\\n        data = show_tensor(feature_map[0], resize_hw=input.shape[:2], show_split=False, is_show=False)[0]\\n        am_data = cv2.addWeighted(data, 0.5, img_orig, 0.5, 0)\\n        show_datas.append(am_data)\\n    if is_show:\\n        show_img(show_datas)\\n    if output_dir is not None:\\n        filename = os.path.join(output_dir,\\n                                Path(filepath).name\\n                                )\\n        if len(show_datas) == 1:\\n            cv2.imwrite(show_datas[0], filename)\\n        else:\\n            for i in range(len(show_datas)):\\n                fname, suffix = os.path.splitext(filename)\\n                cv2.imwrite(fname + '_{}'.format(str(i)) + suffix,show_datas[i])\\n\\ndef _show_save_data_siamese(featurevis, input, img_orig, feature_indexs, fmaps = None):\\n    show_datas = []\\n    imgs = img_orig.detach().cpu().numpy().transpose([0, 2, 3, 1])\\n    if fmaps is None:\\n        for feature_index in feature_indexs:\\n            feature_maps = featurevis.run(input, feature_index=feature_index)[0]\\n            am_data = []\\n            for img,feature_map in zip(imgs,feature_maps[:3]):\\n                data = show_tensor(feature_map, resize_hw=input.shape[2:], show_split=False, is_show=False)[0]\\n                am_data.append(255-data)\\n            show_datas.append(am_data)\\n    else:\\n        for i in range(len(fmaps)):\\n            am_data = []\\n            for img, feature_map in zip(imgs, fmaps[i]):\\n                data = show_tensor(feature_map, resize_hw=input.shape[2:], show_split=False, is_show=False)[0]\\n                am_data.append(data)\\n            show_datas.append(am_data)\\n    return show_datas\\n\\ndef show_featuremap_from_imgs(featurevis, feature_indexs, img_dir, is_show, output_dir):\\n    if not isinstance(feature_indexs, (list, tuple)):\\n        feature_indexs = [feature_indexs]\\n    img_paths = [os.path.join(img_dir, x) for x in os.listdir(img_dir) if 'jpg' in x]\\n    for path in img_paths:\\n        img = cv2.imread(path)\\n        # 这里是输入模型前的图片处理\\n        input = img.astype(np.float32).copy()\\n        # 显示特征图\\n        _show_save_data(featurevis, input, img, feature_indexs, path, is_show, output_dir)\\n\\n\\n\\nif __name__ == '__main__':\\n    img_dir = '.'\\n    out_dir = './out'\\n\\n    init_shape = (1024, 1024, 3)  # 值不重要，只要前向一遍网络时候不报错即可\\n    feature_index = [32, 70, 96, 155]  # 可视化的层索引\\n    use_gpu = True\\n    is_show = False\\n    model = torchvision.models.resnet50(pretrained=True)  # 这里创建模型\\n    #model = torch.load(path)\\n    if use_gpu:\\n        model.cuda()\\n    featurevis = create_featuremap_vis(model, use_gpu, init_shape)\\n    show_featuremap_from_imgs(featurevis, feature_index, img_dir, is_show, out_dir)\\n\""]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 显示第一行\n", "df.loc[0,\"content\"]"]}, {"cell_type": "code", "execution_count": 8, "id": "a47a691c-1644-4f32-9473-6790fc6cd563", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>pre { line-height: 125%; }\n", "td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", ".output_html .hll { background-color: #ffffcc }\n", ".output_html { background: #f8f8f8; }\n", ".output_html .c { color: #3D7B7B; font-style: italic } /* Comment */\n", ".output_html .err { border: 1px solid #F00 } /* Error */\n", ".output_html .k { color: #008000; font-weight: bold } /* Keyword */\n", ".output_html .o { color: #666 } /* Operator */\n", ".output_html .ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */\n", ".output_html .cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */\n", ".output_html .cp { color: #9C6500 } /* Comment.Preproc */\n", ".output_html .cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */\n", ".output_html .c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */\n", ".output_html .cs { color: #3D7B7B; font-style: italic } /* Comment.Special */\n", ".output_html .gd { color: #A00000 } /* Generic.Deleted */\n", ".output_html .ge { font-style: italic } /* Generic.Emph */\n", ".output_html .ges { font-weight: bold; font-style: italic } /* Generic.EmphStrong */\n", ".output_html .gr { color: #E40000 } /* Generic.Error */\n", ".output_html .gh { color: #000080; font-weight: bold } /* Generic.Heading */\n", ".output_html .gi { color: #008400 } /* Generic.Inserted */\n", ".output_html .go { color: #717171 } /* Generic.Output */\n", ".output_html .gp { color: #000080; font-weight: bold } /* Generic.Prompt */\n", ".output_html .gs { font-weight: bold } /* Generic.Strong */\n", ".output_html .gu { color: #800080; font-weight: bold } /* Generic.Subheading */\n", ".output_html .gt { color: #04D } /* Generic.Traceback */\n", ".output_html .kc { color: #008000; font-weight: bold } /* Keyword.Constant */\n", ".output_html .kd { color: #008000; font-weight: bold } /* Keyword.Declaration */\n", ".output_html .kn { color: #008000; font-weight: bold } /* Keyword.Namespace */\n", ".output_html .kp { color: #008000 } /* Keyword.Pseudo */\n", ".output_html .kr { color: #008000; font-weight: bold } /* Keyword.Reserved */\n", ".output_html .kt { color: #B00040 } /* Keyword.Type */\n", ".output_html .m { color: #666 } /* Literal.Number */\n", ".output_html .s { color: #BA2121 } /* Literal.String */\n", ".output_html .na { color: #687822 } /* Name.Attribute */\n", ".output_html .nb { color: #008000 } /* Name.Builtin */\n", ".output_html .nc { color: #00F; font-weight: bold } /* Name.Class */\n", ".output_html .no { color: #800 } /* Name.Constant */\n", ".output_html .nd { color: #A2F } /* Name.Decorator */\n", ".output_html .ni { color: #717171; font-weight: bold } /* Name.Entity */\n", ".output_html .ne { color: #CB3F38; font-weight: bold } /* Name.Exception */\n", ".output_html .nf { color: #00F } /* Name.Function */\n", ".output_html .nl { color: #767600 } /* Name.Label */\n", ".output_html .nn { color: #00F; font-weight: bold } /* Name.Namespace */\n", ".output_html .nt { color: #008000; font-weight: bold } /* Name.Tag */\n", ".output_html .nv { color: #19177C } /* Name.Variable */\n", ".output_html .ow { color: #A2F; font-weight: bold } /* Operator.Word */\n", ".output_html .w { color: #BBB } /* Text.Whitespace */\n", ".output_html .mb { color: #666 } /* Literal.Number.Bin */\n", ".output_html .mf { color: #666 } /* Literal.Number.Float */\n", ".output_html .mh { color: #666 } /* Literal.Number.Hex */\n", ".output_html .mi { color: #666 } /* Literal.Number.Integer */\n", ".output_html .mo { color: #666 } /* Literal.Number.Oct */\n", ".output_html .sa { color: #BA2121 } /* Literal.String.Affix */\n", ".output_html .sb { color: #BA2121 } /* Literal.String.Backtick */\n", ".output_html .sc { color: #BA2121 } /* Literal.String.Char */\n", ".output_html .dl { color: #BA2121 } /* Literal.String.Delimiter */\n", ".output_html .sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */\n", ".output_html .s2 { color: #BA2121 } /* Literal.String.Double */\n", ".output_html .se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */\n", ".output_html .sh { color: #BA2121 } /* Literal.String.Heredoc */\n", ".output_html .si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */\n", ".output_html .sx { color: #008000 } /* Literal.String.Other */\n", ".output_html .sr { color: #A45A77 } /* Literal.String.Regex */\n", ".output_html .s1 { color: #BA2121 } /* Literal.String.Single */\n", ".output_html .ss { color: #19177C } /* Literal.String.Symbol */\n", ".output_html .bp { color: #008000 } /* Name.Builtin.Pseudo */\n", ".output_html .fm { color: #00F } /* Name.Function.Magic */\n", ".output_html .vc { color: #19177C } /* Name.Variable.Class */\n", ".output_html .vg { color: #19177C } /* Name.Variable.Global */\n", ".output_html .vi { color: #19177C } /* Name.Variable.Instance */\n", ".output_html .vm { color: #19177C } /* Name.Variable.Magic */\n", ".output_html .il { color: #666 } /* Literal.Number.Integer.Long */</style><div class=\"highlight\"><pre><span></span><span class=\"kn\">from</span><span class=\"w\"> </span><span class=\"nn\">functools</span><span class=\"w\"> </span><span class=\"kn\">import</span> <span class=\"n\">partial</span>\n", "<span class=\"kn\">from</span><span class=\"w\"> </span><span class=\"nn\">utils.fmap_visualize</span><span class=\"w\"> </span><span class=\"kn\">import</span> <span class=\"n\">FeatureMapVis_siamese</span><span class=\"p\">,</span><span class=\"n\">FeatureMapVis</span>\n", "<span class=\"kn\">from</span><span class=\"w\"> </span><span class=\"nn\">pathlib</span><span class=\"w\"> </span><span class=\"kn\">import</span> <span class=\"n\">Path</span>\n", "<span class=\"kn\">from</span><span class=\"w\"> </span><span class=\"nn\">utils.fmap_visualize</span><span class=\"w\"> </span><span class=\"kn\">import</span> <span class=\"n\">show_tensor</span><span class=\"p\">,</span> <span class=\"n\">show_img</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">numpy</span><span class=\"w\"> </span><span class=\"k\">as</span><span class=\"w\"> </span><span class=\"nn\">np</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">os</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">cv2</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">torch</span>\n", "<span class=\"kn\">import</span><span class=\"w\"> </span><span class=\"nn\">torchvision</span>\n", "<span class=\"c1\">#from models.Models_tmp import SiameseCGNet_v2</span>\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">forward</span><span class=\"p\">(</span><span class=\"bp\">self</span><span class=\"p\">,</span> <span class=\"n\">img</span><span class=\"p\">,</span> <span class=\"n\">img_metas</span><span class=\"o\">=</span><span class=\"kc\">None</span><span class=\"p\">,</span> <span class=\"n\">return_loss</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">,</span> <span class=\"o\">**</span><span class=\"n\">kwargs</span><span class=\"p\">):</span>\n", "    <span class=\"n\">outs</span> <span class=\"o\">=</span> <span class=\"bp\">self</span><span class=\"o\">.</span><span class=\"n\">forward</span><span class=\"p\">(</span><span class=\"n\">img</span><span class=\"p\">)</span>\n", "    <span class=\"k\">return</span> <span class=\"n\">outs</span>\n", "\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">create_featuremap_vis</span><span class=\"p\">(</span><span class=\"n\">model</span><span class=\"o\">=</span><span class=\"kc\">None</span><span class=\"p\">,</span> <span class=\"n\">use_gpu</span><span class=\"o\">=</span><span class=\"kc\">True</span><span class=\"p\">,</span> <span class=\"n\">init_shape</span><span class=\"o\">=</span><span class=\"p\">(</span><span class=\"mi\">768</span><span class=\"p\">,</span> <span class=\"mi\">768</span><span class=\"p\">,</span> <span class=\"mi\">3</span><span class=\"p\">)):</span>\n", "    <span class=\"c1\">#model.forward = partial(forward, model)</span>\n", "    <span class=\"n\">featurevis</span> <span class=\"o\">=</span> <span class=\"n\">FeatureMapVis_siamese</span><span class=\"p\">(</span><span class=\"n\">model</span><span class=\"p\">,</span> <span class=\"n\">use_gpu</span><span class=\"p\">)</span>\n", "    <span class=\"n\">featurevis</span><span class=\"o\">.</span><span class=\"n\">set_hook_style</span><span class=\"p\">(</span><span class=\"n\">init_shape</span><span class=\"p\">[</span><span class=\"mi\">2</span><span class=\"p\">],</span> <span class=\"n\">init_shape</span><span class=\"p\">[:</span><span class=\"mi\">2</span><span class=\"p\">])</span>\n", "    <span class=\"k\">return</span> <span class=\"n\">featurevis</span>\n", "\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">_show_save_data</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">img_orig</span><span class=\"p\">,</span> <span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"n\">filepath</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"p\">,</span> <span class=\"n\">output_dir</span><span class=\"p\">):</span>\n", "    <span class=\"n\">show_datas</span> <span class=\"o\">=</span> <span class=\"p\">[]</span>\n", "    <span class=\"k\">for</span> <span class=\"n\">feature_index</span> <span class=\"ow\">in</span> <span class=\"n\">feature_indexs</span><span class=\"p\">:</span>\n", "        <span class=\"n\">feature_map</span> <span class=\"o\">=</span> <span class=\"n\">featurevis</span><span class=\"o\">.</span><span class=\"n\">run</span><span class=\"p\">(</span><span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">feature_index</span><span class=\"o\">=</span><span class=\"n\">feature_index</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "        <span class=\"n\">data</span> <span class=\"o\">=</span> <span class=\"n\">show_tensor</span><span class=\"p\">(</span><span class=\"n\">feature_map</span><span class=\"p\">[</span><span class=\"mi\">0</span><span class=\"p\">],</span> <span class=\"n\">resize_hw</span><span class=\"o\">=</span><span class=\"nb\">input</span><span class=\"o\">.</span><span class=\"n\">shape</span><span class=\"p\">[:</span><span class=\"mi\">2</span><span class=\"p\">],</span> <span class=\"n\">show_split</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "        <span class=\"n\">am_data</span> <span class=\"o\">=</span> <span class=\"n\">cv2</span><span class=\"o\">.</span><span class=\"n\">addWeighted</span><span class=\"p\">(</span><span class=\"n\">data</span><span class=\"p\">,</span> <span class=\"mf\">0.5</span><span class=\"p\">,</span> <span class=\"n\">img_orig</span><span class=\"p\">,</span> <span class=\"mf\">0.5</span><span class=\"p\">,</span> <span class=\"mi\">0</span><span class=\"p\">)</span>\n", "        <span class=\"n\">show_datas</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"n\">am_data</span><span class=\"p\">)</span>\n", "    <span class=\"k\">if</span> <span class=\"n\">is_show</span><span class=\"p\">:</span>\n", "        <span class=\"n\">show_img</span><span class=\"p\">(</span><span class=\"n\">show_datas</span><span class=\"p\">)</span>\n", "    <span class=\"k\">if</span> <span class=\"n\">output_dir</span> <span class=\"ow\">is</span> <span class=\"ow\">not</span> <span class=\"kc\">None</span><span class=\"p\">:</span>\n", "        <span class=\"n\">filename</span> <span class=\"o\">=</span> <span class=\"n\">os</span><span class=\"o\">.</span><span class=\"n\">path</span><span class=\"o\">.</span><span class=\"n\">join</span><span class=\"p\">(</span><span class=\"n\">output_dir</span><span class=\"p\">,</span>\n", "                                <span class=\"n\">Path</span><span class=\"p\">(</span><span class=\"n\">filepath</span><span class=\"p\">)</span><span class=\"o\">.</span><span class=\"n\">name</span>\n", "                                <span class=\"p\">)</span>\n", "        <span class=\"k\">if</span> <span class=\"nb\">len</span><span class=\"p\">(</span><span class=\"n\">show_datas</span><span class=\"p\">)</span> <span class=\"o\">==</span> <span class=\"mi\">1</span><span class=\"p\">:</span>\n", "            <span class=\"n\">cv2</span><span class=\"o\">.</span><span class=\"n\">imwrite</span><span class=\"p\">(</span><span class=\"n\">show_datas</span><span class=\"p\">[</span><span class=\"mi\">0</span><span class=\"p\">],</span> <span class=\"n\">filename</span><span class=\"p\">)</span>\n", "        <span class=\"k\">else</span><span class=\"p\">:</span>\n", "            <span class=\"k\">for</span> <span class=\"n\">i</span> <span class=\"ow\">in</span> <span class=\"nb\">range</span><span class=\"p\">(</span><span class=\"nb\">len</span><span class=\"p\">(</span><span class=\"n\">show_datas</span><span class=\"p\">)):</span>\n", "                <span class=\"n\">fname</span><span class=\"p\">,</span> <span class=\"n\">suffix</span> <span class=\"o\">=</span> <span class=\"n\">os</span><span class=\"o\">.</span><span class=\"n\">path</span><span class=\"o\">.</span><span class=\"n\">splitext</span><span class=\"p\">(</span><span class=\"n\">filename</span><span class=\"p\">)</span>\n", "                <span class=\"n\">cv2</span><span class=\"o\">.</span><span class=\"n\">imwrite</span><span class=\"p\">(</span><span class=\"n\">fname</span> <span class=\"o\">+</span> <span class=\"s1\">&#39;_</span><span class=\"si\">{}</span><span class=\"s1\">&#39;</span><span class=\"o\">.</span><span class=\"n\">format</span><span class=\"p\">(</span><span class=\"nb\">str</span><span class=\"p\">(</span><span class=\"n\">i</span><span class=\"p\">))</span> <span class=\"o\">+</span> <span class=\"n\">suffix</span><span class=\"p\">,</span><span class=\"n\">show_datas</span><span class=\"p\">[</span><span class=\"n\">i</span><span class=\"p\">])</span>\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">_show_save_data_siamese</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">img_orig</span><span class=\"p\">,</span> <span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"n\">fmaps</span> <span class=\"o\">=</span> <span class=\"kc\">None</span><span class=\"p\">):</span>\n", "    <span class=\"n\">show_datas</span> <span class=\"o\">=</span> <span class=\"p\">[]</span>\n", "    <span class=\"n\">imgs</span> <span class=\"o\">=</span> <span class=\"n\">img_orig</span><span class=\"o\">.</span><span class=\"n\">detach</span><span class=\"p\">()</span><span class=\"o\">.</span><span class=\"n\">cpu</span><span class=\"p\">()</span><span class=\"o\">.</span><span class=\"n\">numpy</span><span class=\"p\">()</span><span class=\"o\">.</span><span class=\"n\">transpose</span><span class=\"p\">([</span><span class=\"mi\">0</span><span class=\"p\">,</span> <span class=\"mi\">2</span><span class=\"p\">,</span> <span class=\"mi\">3</span><span class=\"p\">,</span> <span class=\"mi\">1</span><span class=\"p\">])</span>\n", "    <span class=\"k\">if</span> <span class=\"n\">fmaps</span> <span class=\"ow\">is</span> <span class=\"kc\">None</span><span class=\"p\">:</span>\n", "        <span class=\"k\">for</span> <span class=\"n\">feature_index</span> <span class=\"ow\">in</span> <span class=\"n\">feature_indexs</span><span class=\"p\">:</span>\n", "            <span class=\"n\">feature_maps</span> <span class=\"o\">=</span> <span class=\"n\">featurevis</span><span class=\"o\">.</span><span class=\"n\">run</span><span class=\"p\">(</span><span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">feature_index</span><span class=\"o\">=</span><span class=\"n\">feature_index</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "            <span class=\"n\">am_data</span> <span class=\"o\">=</span> <span class=\"p\">[]</span>\n", "            <span class=\"k\">for</span> <span class=\"n\">img</span><span class=\"p\">,</span><span class=\"n\">feature_map</span> <span class=\"ow\">in</span> <span class=\"nb\">zip</span><span class=\"p\">(</span><span class=\"n\">imgs</span><span class=\"p\">,</span><span class=\"n\">feature_maps</span><span class=\"p\">[:</span><span class=\"mi\">3</span><span class=\"p\">]):</span>\n", "                <span class=\"n\">data</span> <span class=\"o\">=</span> <span class=\"n\">show_tensor</span><span class=\"p\">(</span><span class=\"n\">feature_map</span><span class=\"p\">,</span> <span class=\"n\">resize_hw</span><span class=\"o\">=</span><span class=\"nb\">input</span><span class=\"o\">.</span><span class=\"n\">shape</span><span class=\"p\">[</span><span class=\"mi\">2</span><span class=\"p\">:],</span> <span class=\"n\">show_split</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "                <span class=\"n\">am_data</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"mi\">255</span><span class=\"o\">-</span><span class=\"n\">data</span><span class=\"p\">)</span>\n", "            <span class=\"n\">show_datas</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"n\">am_data</span><span class=\"p\">)</span>\n", "    <span class=\"k\">else</span><span class=\"p\">:</span>\n", "        <span class=\"k\">for</span> <span class=\"n\">i</span> <span class=\"ow\">in</span> <span class=\"nb\">range</span><span class=\"p\">(</span><span class=\"nb\">len</span><span class=\"p\">(</span><span class=\"n\">fmaps</span><span class=\"p\">)):</span>\n", "            <span class=\"n\">am_data</span> <span class=\"o\">=</span> <span class=\"p\">[]</span>\n", "            <span class=\"k\">for</span> <span class=\"n\">img</span><span class=\"p\">,</span> <span class=\"n\">feature_map</span> <span class=\"ow\">in</span> <span class=\"nb\">zip</span><span class=\"p\">(</span><span class=\"n\">imgs</span><span class=\"p\">,</span> <span class=\"n\">fmaps</span><span class=\"p\">[</span><span class=\"n\">i</span><span class=\"p\">]):</span>\n", "                <span class=\"n\">data</span> <span class=\"o\">=</span> <span class=\"n\">show_tensor</span><span class=\"p\">(</span><span class=\"n\">feature_map</span><span class=\"p\">,</span> <span class=\"n\">resize_hw</span><span class=\"o\">=</span><span class=\"nb\">input</span><span class=\"o\">.</span><span class=\"n\">shape</span><span class=\"p\">[</span><span class=\"mi\">2</span><span class=\"p\">:],</span> <span class=\"n\">show_split</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"o\">=</span><span class=\"kc\">False</span><span class=\"p\">)[</span><span class=\"mi\">0</span><span class=\"p\">]</span>\n", "                <span class=\"n\">am_data</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"n\">data</span><span class=\"p\">)</span>\n", "            <span class=\"n\">show_datas</span><span class=\"o\">.</span><span class=\"n\">append</span><span class=\"p\">(</span><span class=\"n\">am_data</span><span class=\"p\">)</span>\n", "    <span class=\"k\">return</span> <span class=\"n\">show_datas</span>\n", "\n", "<span class=\"k\">def</span><span class=\"w\"> </span><span class=\"nf\">show_featuremap_from_imgs</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"n\">img_dir</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"p\">,</span> <span class=\"n\">output_dir</span><span class=\"p\">):</span>\n", "    <span class=\"k\">if</span> <span class=\"ow\">not</span> <span class=\"nb\">isinstance</span><span class=\"p\">(</span><span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"p\">(</span><span class=\"nb\">list</span><span class=\"p\">,</span> <span class=\"nb\">tuple</span><span class=\"p\">)):</span>\n", "        <span class=\"n\">feature_indexs</span> <span class=\"o\">=</span> <span class=\"p\">[</span><span class=\"n\">feature_indexs</span><span class=\"p\">]</span>\n", "    <span class=\"n\">img_paths</span> <span class=\"o\">=</span> <span class=\"p\">[</span><span class=\"n\">os</span><span class=\"o\">.</span><span class=\"n\">path</span><span class=\"o\">.</span><span class=\"n\">join</span><span class=\"p\">(</span><span class=\"n\">img_dir</span><span class=\"p\">,</span> <span class=\"n\">x</span><span class=\"p\">)</span> <span class=\"k\">for</span> <span class=\"n\">x</span> <span class=\"ow\">in</span> <span class=\"n\">os</span><span class=\"o\">.</span><span class=\"n\">listdir</span><span class=\"p\">(</span><span class=\"n\">img_dir</span><span class=\"p\">)</span> <span class=\"k\">if</span> <span class=\"s1\">&#39;jpg&#39;</span> <span class=\"ow\">in</span> <span class=\"n\">x</span><span class=\"p\">]</span>\n", "    <span class=\"k\">for</span> <span class=\"n\">path</span> <span class=\"ow\">in</span> <span class=\"n\">img_paths</span><span class=\"p\">:</span>\n", "        <span class=\"n\">img</span> <span class=\"o\">=</span> <span class=\"n\">cv2</span><span class=\"o\">.</span><span class=\"n\">imread</span><span class=\"p\">(</span><span class=\"n\">path</span><span class=\"p\">)</span>\n", "        <span class=\"c1\"># 这里是输入模型前的图片处理</span>\n", "        <span class=\"nb\">input</span> <span class=\"o\">=</span> <span class=\"n\">img</span><span class=\"o\">.</span><span class=\"n\">astype</span><span class=\"p\">(</span><span class=\"n\">np</span><span class=\"o\">.</span><span class=\"n\">float32</span><span class=\"p\">)</span><span class=\"o\">.</span><span class=\"n\">copy</span><span class=\"p\">()</span>\n", "        <span class=\"c1\"># 显示特征图</span>\n", "        <span class=\"n\">_show_save_data</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"nb\">input</span><span class=\"p\">,</span> <span class=\"n\">img</span><span class=\"p\">,</span> <span class=\"n\">feature_indexs</span><span class=\"p\">,</span> <span class=\"n\">path</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"p\">,</span> <span class=\"n\">output_dir</span><span class=\"p\">)</span>\n", "\n", "\n", "\n", "<span class=\"k\">if</span> <span class=\"vm\">__name__</span> <span class=\"o\">==</span> <span class=\"s1\">&#39;__main__&#39;</span><span class=\"p\">:</span>\n", "    <span class=\"n\">img_dir</span> <span class=\"o\">=</span> <span class=\"s1\">&#39;.&#39;</span>\n", "    <span class=\"n\">out_dir</span> <span class=\"o\">=</span> <span class=\"s1\">&#39;./out&#39;</span>\n", "\n", "    <span class=\"n\">init_shape</span> <span class=\"o\">=</span> <span class=\"p\">(</span><span class=\"mi\">1024</span><span class=\"p\">,</span> <span class=\"mi\">1024</span><span class=\"p\">,</span> <span class=\"mi\">3</span><span class=\"p\">)</span>  <span class=\"c1\"># 值不重要，只要前向一遍网络时候不报错即可</span>\n", "    <span class=\"n\">feature_index</span> <span class=\"o\">=</span> <span class=\"p\">[</span><span class=\"mi\">32</span><span class=\"p\">,</span> <span class=\"mi\">70</span><span class=\"p\">,</span> <span class=\"mi\">96</span><span class=\"p\">,</span> <span class=\"mi\">155</span><span class=\"p\">]</span>  <span class=\"c1\"># 可视化的层索引</span>\n", "    <span class=\"n\">use_gpu</span> <span class=\"o\">=</span> <span class=\"kc\">True</span>\n", "    <span class=\"n\">is_show</span> <span class=\"o\">=</span> <span class=\"kc\">False</span>\n", "    <span class=\"n\">model</span> <span class=\"o\">=</span> <span class=\"n\">torchvision</span><span class=\"o\">.</span><span class=\"n\">models</span><span class=\"o\">.</span><span class=\"n\">resnet50</span><span class=\"p\">(</span><span class=\"n\">pretrained</span><span class=\"o\">=</span><span class=\"kc\">True</span><span class=\"p\">)</span>  <span class=\"c1\"># 这里创建模型</span>\n", "    <span class=\"c1\">#model = torch.load(path)</span>\n", "    <span class=\"k\">if</span> <span class=\"n\">use_gpu</span><span class=\"p\">:</span>\n", "        <span class=\"n\">model</span><span class=\"o\">.</span><span class=\"n\">cuda</span><span class=\"p\">()</span>\n", "    <span class=\"n\">featurevis</span> <span class=\"o\">=</span> <span class=\"n\">create_featuremap_vis</span><span class=\"p\">(</span><span class=\"n\">model</span><span class=\"p\">,</span> <span class=\"n\">use_gpu</span><span class=\"p\">,</span> <span class=\"n\">init_shape</span><span class=\"p\">)</span>\n", "    <span class=\"n\">show_featuremap_from_imgs</span><span class=\"p\">(</span><span class=\"n\">featurevis</span><span class=\"p\">,</span> <span class=\"n\">feature_index</span><span class=\"p\">,</span> <span class=\"n\">img_dir</span><span class=\"p\">,</span> <span class=\"n\">is_show</span><span class=\"p\">,</span> <span class=\"n\">out_dir</span><span class=\"p\">)</span>\n", "</pre></div>\n"], "text/latex": ["\\begin{Verbatim}[commandchars=\\\\\\{\\}]\n", "\\PY{k+kn}{from}\\PY{+w}{ }\\PY{n+nn}{functools}\\PY{+w}{ }\\PY{k+kn}{import} \\PY{n}{partial}\n", "\\PY{k+kn}{from}\\PY{+w}{ }\\PY{n+nn}{utils}\\PY{n+nn}{.}\\PY{n+nn}{fmap\\PYZus{}visualize}\\PY{+w}{ }\\PY{k+kn}{import} \\PY{n}{FeatureMapVis\\PYZus{}siamese}\\PY{p}{,}\\PY{n}{FeatureMapVis}\n", "\\PY{k+kn}{from}\\PY{+w}{ }\\PY{n+nn}{pathlib}\\PY{+w}{ }\\PY{k+kn}{import} \\PY{n}{Path}\n", "\\PY{k+kn}{from}\\PY{+w}{ }\\PY{n+nn}{utils}\\PY{n+nn}{.}\\PY{n+nn}{fmap\\PYZus{}visualize}\\PY{+w}{ }\\PY{k+kn}{import} \\PY{n}{show\\PYZus{}tensor}\\PY{p}{,} \\PY{n}{show\\PYZus{}img}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{numpy}\\PY{+w}{ }\\PY{k}{as}\\PY{+w}{ }\\PY{n+nn}{np}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{os}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{cv2}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{torch}\n", "\\PY{k+kn}{import}\\PY{+w}{ }\\PY{n+nn}{torchvision}\n", "\\PY{c+c1}{\\PYZsh{}from models.Models\\PYZus{}tmp import SiameseCGNet\\PYZus{}v2}\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{forward}\\PY{p}{(}\\PY{n+nb+bp}{self}\\PY{p}{,} \\PY{n}{img}\\PY{p}{,} \\PY{n}{img\\PYZus{}metas}\\PY{o}{=}\\PY{k+kc}{None}\\PY{p}{,} \\PY{n}{return\\PYZus{}loss}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{,} \\PY{o}{*}\\PY{o}{*}\\PY{n}{kwargs}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{n}{outs} \\PY{o}{=} \\PY{n+nb+bp}{self}\\PY{o}{.}\\PY{n}{forward}\\PY{p}{(}\\PY{n}{img}\\PY{p}{)}\n", "    \\PY{k}{return} \\PY{n}{outs}\n", "\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{create\\PYZus{}featuremap\\PYZus{}vis}\\PY{p}{(}\\PY{n}{model}\\PY{o}{=}\\PY{k+kc}{None}\\PY{p}{,} \\PY{n}{use\\PYZus{}gpu}\\PY{o}{=}\\PY{k+kc}{True}\\PY{p}{,} \\PY{n}{init\\PYZus{}shape}\\PY{o}{=}\\PY{p}{(}\\PY{l+m+mi}{768}\\PY{p}{,} \\PY{l+m+mi}{768}\\PY{p}{,} \\PY{l+m+mi}{3}\\PY{p}{)}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{c+c1}{\\PYZsh{}model.forward = partial(forward, model)}\n", "    \\PY{n}{featurevis} \\PY{o}{=} \\PY{n}{FeatureMapVis\\PYZus{}siamese}\\PY{p}{(}\\PY{n}{model}\\PY{p}{,} \\PY{n}{use\\PYZus{}gpu}\\PY{p}{)}\n", "    \\PY{n}{featurevis}\\PY{o}{.}\\PY{n}{set\\PYZus{}hook\\PYZus{}style}\\PY{p}{(}\\PY{n}{init\\PYZus{}shape}\\PY{p}{[}\\PY{l+m+mi}{2}\\PY{p}{]}\\PY{p}{,} \\PY{n}{init\\PYZus{}shape}\\PY{p}{[}\\PY{p}{:}\\PY{l+m+mi}{2}\\PY{p}{]}\\PY{p}{)}\n", "    \\PY{k}{return} \\PY{n}{featurevis}\n", "\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{\\PYZus{}show\\PYZus{}save\\PYZus{}data}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{img\\PYZus{}orig}\\PY{p}{,} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{n}{filepath}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{p}{,} \\PY{n}{output\\PYZus{}dir}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{n}{show\\PYZus{}datas} \\PY{o}{=} \\PY{p}{[}\\PY{p}{]}\n", "    \\PY{k}{for} \\PY{n}{feature\\PYZus{}index} \\PY{o+ow}{in} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{:}\n", "        \\PY{n}{feature\\PYZus{}map} \\PY{o}{=} \\PY{n}{featurevis}\\PY{o}{.}\\PY{n}{run}\\PY{p}{(}\\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{feature\\PYZus{}index}\\PY{o}{=}\\PY{n}{feature\\PYZus{}index}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "        \\PY{n}{data} \\PY{o}{=} \\PY{n}{show\\PYZus{}tensor}\\PY{p}{(}\\PY{n}{feature\\PYZus{}map}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\\PY{p}{,} \\PY{n}{resize\\PYZus{}hw}\\PY{o}{=}\\PY{n+nb}{input}\\PY{o}{.}\\PY{n}{shape}\\PY{p}{[}\\PY{p}{:}\\PY{l+m+mi}{2}\\PY{p}{]}\\PY{p}{,} \\PY{n}{show\\PYZus{}split}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "        \\PY{n}{am\\PYZus{}data} \\PY{o}{=} \\PY{n}{cv2}\\PY{o}{.}\\PY{n}{addWeighted}\\PY{p}{(}\\PY{n}{data}\\PY{p}{,} \\PY{l+m+mf}{0.5}\\PY{p}{,} \\PY{n}{img\\PYZus{}orig}\\PY{p}{,} \\PY{l+m+mf}{0.5}\\PY{p}{,} \\PY{l+m+mi}{0}\\PY{p}{)}\n", "        \\PY{n}{show\\PYZus{}datas}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{n}{am\\PYZus{}data}\\PY{p}{)}\n", "    \\PY{k}{if} \\PY{n}{is\\PYZus{}show}\\PY{p}{:}\n", "        \\PY{n}{show\\PYZus{}img}\\PY{p}{(}\\PY{n}{show\\PYZus{}datas}\\PY{p}{)}\n", "    \\PY{k}{if} \\PY{n}{output\\PYZus{}dir} \\PY{o+ow}{is} \\PY{o+ow}{not} \\PY{k+kc}{None}\\PY{p}{:}\n", "        \\PY{n}{filename} \\PY{o}{=} \\PY{n}{os}\\PY{o}{.}\\PY{n}{path}\\PY{o}{.}\\PY{n}{join}\\PY{p}{(}\\PY{n}{output\\PYZus{}dir}\\PY{p}{,}\n", "                                \\PY{n}{Path}\\PY{p}{(}\\PY{n}{filepath}\\PY{p}{)}\\PY{o}{.}\\PY{n}{name}\n", "                                \\PY{p}{)}\n", "        \\PY{k}{if} \\PY{n+nb}{len}\\PY{p}{(}\\PY{n}{show\\PYZus{}datas}\\PY{p}{)} \\PY{o}{==} \\PY{l+m+mi}{1}\\PY{p}{:}\n", "            \\PY{n}{cv2}\\PY{o}{.}\\PY{n}{imwrite}\\PY{p}{(}\\PY{n}{show\\PYZus{}datas}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\\PY{p}{,} \\PY{n}{filename}\\PY{p}{)}\n", "        \\PY{k}{else}\\PY{p}{:}\n", "            \\PY{k}{for} \\PY{n}{i} \\PY{o+ow}{in} \\PY{n+nb}{range}\\PY{p}{(}\\PY{n+nb}{len}\\PY{p}{(}\\PY{n}{show\\PYZus{}datas}\\PY{p}{)}\\PY{p}{)}\\PY{p}{:}\n", "                \\PY{n}{fname}\\PY{p}{,} \\PY{n}{suffix} \\PY{o}{=} \\PY{n}{os}\\PY{o}{.}\\PY{n}{path}\\PY{o}{.}\\PY{n}{splitext}\\PY{p}{(}\\PY{n}{filename}\\PY{p}{)}\n", "                \\PY{n}{cv2}\\PY{o}{.}\\PY{n}{imwrite}\\PY{p}{(}\\PY{n}{fname} \\PY{o}{+} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{\\PYZus{}}\\PY{l+s+si}{\\PYZob{}\\PYZcb{}}\\PY{l+s+s1}{\\PYZsq{}}\\PY{o}{.}\\PY{n}{format}\\PY{p}{(}\\PY{n+nb}{str}\\PY{p}{(}\\PY{n}{i}\\PY{p}{)}\\PY{p}{)} \\PY{o}{+} \\PY{n}{suffix}\\PY{p}{,}\\PY{n}{show\\PYZus{}datas}\\PY{p}{[}\\PY{n}{i}\\PY{p}{]}\\PY{p}{)}\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{\\PYZus{}show\\PYZus{}save\\PYZus{}data\\PYZus{}siamese}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{img\\PYZus{}orig}\\PY{p}{,} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{n}{fmaps} \\PY{o}{=} \\PY{k+kc}{None}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{n}{show\\PYZus{}datas} \\PY{o}{=} \\PY{p}{[}\\PY{p}{]}\n", "    \\PY{n}{imgs} \\PY{o}{=} \\PY{n}{img\\PYZus{}orig}\\PY{o}{.}\\PY{n}{detach}\\PY{p}{(}\\PY{p}{)}\\PY{o}{.}\\PY{n}{cpu}\\PY{p}{(}\\PY{p}{)}\\PY{o}{.}\\PY{n}{numpy}\\PY{p}{(}\\PY{p}{)}\\PY{o}{.}\\PY{n}{transpose}\\PY{p}{(}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{,} \\PY{l+m+mi}{2}\\PY{p}{,} \\PY{l+m+mi}{3}\\PY{p}{,} \\PY{l+m+mi}{1}\\PY{p}{]}\\PY{p}{)}\n", "    \\PY{k}{if} \\PY{n}{fmaps} \\PY{o+ow}{is} \\PY{k+kc}{None}\\PY{p}{:}\n", "        \\PY{k}{for} \\PY{n}{feature\\PYZus{}index} \\PY{o+ow}{in} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{:}\n", "            \\PY{n}{feature\\PYZus{}maps} \\PY{o}{=} \\PY{n}{featurevis}\\PY{o}{.}\\PY{n}{run}\\PY{p}{(}\\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{feature\\PYZus{}index}\\PY{o}{=}\\PY{n}{feature\\PYZus{}index}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "            \\PY{n}{am\\PYZus{}data} \\PY{o}{=} \\PY{p}{[}\\PY{p}{]}\n", "            \\PY{k}{for} \\PY{n}{img}\\PY{p}{,}\\PY{n}{feature\\PYZus{}map} \\PY{o+ow}{in} \\PY{n+nb}{zip}\\PY{p}{(}\\PY{n}{imgs}\\PY{p}{,}\\PY{n}{feature\\PYZus{}maps}\\PY{p}{[}\\PY{p}{:}\\PY{l+m+mi}{3}\\PY{p}{]}\\PY{p}{)}\\PY{p}{:}\n", "                \\PY{n}{data} \\PY{o}{=} \\PY{n}{show\\PYZus{}tensor}\\PY{p}{(}\\PY{n}{feature\\PYZus{}map}\\PY{p}{,} \\PY{n}{resize\\PYZus{}hw}\\PY{o}{=}\\PY{n+nb}{input}\\PY{o}{.}\\PY{n}{shape}\\PY{p}{[}\\PY{l+m+mi}{2}\\PY{p}{:}\\PY{p}{]}\\PY{p}{,} \\PY{n}{show\\PYZus{}split}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "                \\PY{n}{am\\PYZus{}data}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{l+m+mi}{255}\\PY{o}{\\PYZhy{}}\\PY{n}{data}\\PY{p}{)}\n", "            \\PY{n}{show\\PYZus{}datas}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{n}{am\\PYZus{}data}\\PY{p}{)}\n", "    \\PY{k}{else}\\PY{p}{:}\n", "        \\PY{k}{for} \\PY{n}{i} \\PY{o+ow}{in} \\PY{n+nb}{range}\\PY{p}{(}\\PY{n+nb}{len}\\PY{p}{(}\\PY{n}{fmaps}\\PY{p}{)}\\PY{p}{)}\\PY{p}{:}\n", "            \\PY{n}{am\\PYZus{}data} \\PY{o}{=} \\PY{p}{[}\\PY{p}{]}\n", "            \\PY{k}{for} \\PY{n}{img}\\PY{p}{,} \\PY{n}{feature\\PYZus{}map} \\PY{o+ow}{in} \\PY{n+nb}{zip}\\PY{p}{(}\\PY{n}{imgs}\\PY{p}{,} \\PY{n}{fmaps}\\PY{p}{[}\\PY{n}{i}\\PY{p}{]}\\PY{p}{)}\\PY{p}{:}\n", "                \\PY{n}{data} \\PY{o}{=} \\PY{n}{show\\PYZus{}tensor}\\PY{p}{(}\\PY{n}{feature\\PYZus{}map}\\PY{p}{,} \\PY{n}{resize\\PYZus{}hw}\\PY{o}{=}\\PY{n+nb}{input}\\PY{o}{.}\\PY{n}{shape}\\PY{p}{[}\\PY{l+m+mi}{2}\\PY{p}{:}\\PY{p}{]}\\PY{p}{,} \\PY{n}{show\\PYZus{}split}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{o}{=}\\PY{k+kc}{False}\\PY{p}{)}\\PY{p}{[}\\PY{l+m+mi}{0}\\PY{p}{]}\n", "                \\PY{n}{am\\PYZus{}data}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{n}{data}\\PY{p}{)}\n", "            \\PY{n}{show\\PYZus{}datas}\\PY{o}{.}\\PY{n}{append}\\PY{p}{(}\\PY{n}{am\\PYZus{}data}\\PY{p}{)}\n", "    \\PY{k}{return} \\PY{n}{show\\PYZus{}datas}\n", "\n", "\\PY{k}{def}\\PY{+w}{ }\\PY{n+nf}{show\\PYZus{}featuremap\\PYZus{}from\\PYZus{}imgs}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{n}{img\\PYZus{}dir}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{p}{,} \\PY{n}{output\\PYZus{}dir}\\PY{p}{)}\\PY{p}{:}\n", "    \\PY{k}{if} \\PY{o+ow}{not} \\PY{n+nb}{isinstance}\\PY{p}{(}\\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{p}{(}\\PY{n+nb}{list}\\PY{p}{,} \\PY{n+nb}{tuple}\\PY{p}{)}\\PY{p}{)}\\PY{p}{:}\n", "        \\PY{n}{feature\\PYZus{}indexs} \\PY{o}{=} \\PY{p}{[}\\PY{n}{feature\\PYZus{}indexs}\\PY{p}{]}\n", "    \\PY{n}{img\\PYZus{}paths} \\PY{o}{=} \\PY{p}{[}\\PY{n}{os}\\PY{o}{.}\\PY{n}{path}\\PY{o}{.}\\PY{n}{join}\\PY{p}{(}\\PY{n}{img\\PYZus{}dir}\\PY{p}{,} \\PY{n}{x}\\PY{p}{)} \\PY{k}{for} \\PY{n}{x} \\PY{o+ow}{in} \\PY{n}{os}\\PY{o}{.}\\PY{n}{listdir}\\PY{p}{(}\\PY{n}{img\\PYZus{}dir}\\PY{p}{)} \\PY{k}{if} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{jpg}\\PY{l+s+s1}{\\PYZsq{}} \\PY{o+ow}{in} \\PY{n}{x}\\PY{p}{]}\n", "    \\PY{k}{for} \\PY{n}{path} \\PY{o+ow}{in} \\PY{n}{img\\PYZus{}paths}\\PY{p}{:}\n", "        \\PY{n}{img} \\PY{o}{=} \\PY{n}{cv2}\\PY{o}{.}\\PY{n}{imread}\\PY{p}{(}\\PY{n}{path}\\PY{p}{)}\n", "        \\PY{c+c1}{\\PYZsh{} 这里是输入模型前的图片处理}\n", "        \\PY{n+nb}{input} \\PY{o}{=} \\PY{n}{img}\\PY{o}{.}\\PY{n}{astype}\\PY{p}{(}\\PY{n}{np}\\PY{o}{.}\\PY{n}{float32}\\PY{p}{)}\\PY{o}{.}\\PY{n}{copy}\\PY{p}{(}\\PY{p}{)}\n", "        \\PY{c+c1}{\\PYZsh{} 显示特征图}\n", "        \\PY{n}{\\PYZus{}show\\PYZus{}save\\PYZus{}data}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n+nb}{input}\\PY{p}{,} \\PY{n}{img}\\PY{p}{,} \\PY{n}{feature\\PYZus{}indexs}\\PY{p}{,} \\PY{n}{path}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{p}{,} \\PY{n}{output\\PYZus{}dir}\\PY{p}{)}\n", "\n", "\n", "\n", "\\PY{k}{if} \\PY{n+nv+vm}{\\PYZus{}\\PYZus{}name\\PYZus{}\\PYZus{}} \\PY{o}{==} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{\\PYZus{}\\PYZus{}main\\PYZus{}\\PYZus{}}\\PY{l+s+s1}{\\PYZsq{}}\\PY{p}{:}\n", "    \\PY{n}{img\\PYZus{}dir} \\PY{o}{=} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{.}\\PY{l+s+s1}{\\PYZsq{}}\n", "    \\PY{n}{out\\PYZus{}dir} \\PY{o}{=} \\PY{l+s+s1}{\\PYZsq{}}\\PY{l+s+s1}{./out}\\PY{l+s+s1}{\\PYZsq{}}\n", "\n", "    \\PY{n}{init\\PYZus{}shape} \\PY{o}{=} \\PY{p}{(}\\PY{l+m+mi}{1024}\\PY{p}{,} \\PY{l+m+mi}{1024}\\PY{p}{,} \\PY{l+m+mi}{3}\\PY{p}{)}  \\PY{c+c1}{\\PYZsh{} 值不重要，只要前向一遍网络时候不报错即可}\n", "    \\PY{n}{feature\\PYZus{}index} \\PY{o}{=} \\PY{p}{[}\\PY{l+m+mi}{32}\\PY{p}{,} \\PY{l+m+mi}{70}\\PY{p}{,} \\PY{l+m+mi}{96}\\PY{p}{,} \\PY{l+m+mi}{155}\\PY{p}{]}  \\PY{c+c1}{\\PYZsh{} 可视化的层索引}\n", "    \\PY{n}{use\\PYZus{}gpu} \\PY{o}{=} \\PY{k+kc}{True}\n", "    \\PY{n}{is\\PYZus{}show} \\PY{o}{=} \\PY{k+kc}{False}\n", "    \\PY{n}{model} \\PY{o}{=} \\PY{n}{torchvision}\\PY{o}{.}\\PY{n}{models}\\PY{o}{.}\\PY{n}{resnet50}\\PY{p}{(}\\PY{n}{pretrained}\\PY{o}{=}\\PY{k+kc}{True}\\PY{p}{)}  \\PY{c+c1}{\\PYZsh{} 这里创建模型}\n", "    \\PY{c+c1}{\\PYZsh{}model = torch.load(path)}\n", "    \\PY{k}{if} \\PY{n}{use\\PYZus{}gpu}\\PY{p}{:}\n", "        \\PY{n}{model}\\PY{o}{.}\\PY{n}{cuda}\\PY{p}{(}\\PY{p}{)}\n", "    \\PY{n}{featurevis} \\PY{o}{=} \\PY{n}{create\\PYZus{}featuremap\\PYZus{}vis}\\PY{p}{(}\\PY{n}{model}\\PY{p}{,} \\PY{n}{use\\PYZus{}gpu}\\PY{p}{,} \\PY{n}{init\\PYZus{}shape}\\PY{p}{)}\n", "    \\PY{n}{show\\PYZus{}featuremap\\PYZus{}from\\PYZus{}imgs}\\PY{p}{(}\\PY{n}{featurevis}\\PY{p}{,} \\PY{n}{feature\\PYZus{}index}\\PY{p}{,} \\PY{n}{img\\PYZus{}dir}\\PY{p}{,} \\PY{n}{is\\PYZus{}show}\\PY{p}{,} \\PY{n}{out\\PYZus{}dir}\\PY{p}{)}\n", "\\end{Verbatim}\n"], "text/plain": ["from functools import partial\n", "from utils.fmap_visualize import FeatureMapVis_siamese,FeatureMapVis\n", "from pathlib import Path\n", "from utils.fmap_visualize import show_tensor, show_img\n", "import numpy as np\n", "import os\n", "import cv2\n", "import torch\n", "import torchvision\n", "#from models.Models_tmp import SiameseCGNet_v2\n", "\n", "def forward(self, img, img_metas=None, return_loss=False, **kwargs):\n", "    outs = self.forward(img)\n", "    return outs\n", "\n", "\n", "def create_featuremap_vis(model=None, use_gpu=True, init_shape=(768, 768, 3)):\n", "    #model.forward = partial(forward, model)\n", "    featurevis = FeatureMapVis_siamese(model, use_gpu)\n", "    featurevis.set_hook_style(init_shape[2], init_shape[:2])\n", "    return featurevis\n", "\n", "\n", "def _show_save_data(featurevis, input, img_orig, feature_indexs, filepath, is_show, output_dir):\n", "    show_datas = []\n", "    for feature_index in feature_indexs:\n", "        feature_map = featurevis.run(input, feature_index=feature_index)[0]\n", "        data = show_tensor(feature_map[0], resize_hw=input.shape[:2], show_split=False, is_show=False)[0]\n", "        am_data = cv2.addWeighted(data, 0.5, img_orig, 0.5, 0)\n", "        show_datas.append(am_data)\n", "    if is_show:\n", "        show_img(show_datas)\n", "    if output_dir is not None:\n", "        filename = os.path.join(output_dir,\n", "                                Path(filepath).name\n", "                                )\n", "        if len(show_datas) == 1:\n", "            cv2.imwrite(show_datas[0], filename)\n", "        else:\n", "            for i in range(len(show_datas)):\n", "                fname, suffix = os.path.splitext(filename)\n", "                cv2.imwrite(fname + '_{}'.format(str(i)) + suffix,show_datas[i])\n", "\n", "def _show_save_data_siamese(featurevis, input, img_orig, feature_indexs, fmaps = None):\n", "    show_datas = []\n", "    imgs = img_orig.detach().cpu().numpy().transpose([0, 2, 3, 1])\n", "    if fmaps is None:\n", "        for feature_index in feature_indexs:\n", "            feature_maps = featurevis.run(input, feature_index=feature_index)[0]\n", "            am_data = []\n", "            for img,feature_map in zip(imgs,feature_maps[:3]):\n", "                data = show_tensor(feature_map, resize_hw=input.shape[2:], show_split=False, is_show=False)[0]\n", "                am_data.append(255-data)\n", "            show_datas.append(am_data)\n", "    else:\n", "        for i in range(len(fmaps)):\n", "            am_data = []\n", "            for img, feature_map in zip(imgs, fmaps[i]):\n", "                data = show_tensor(feature_map, resize_hw=input.shape[2:], show_split=False, is_show=False)[0]\n", "                am_data.append(data)\n", "            show_datas.append(am_data)\n", "    return show_datas\n", "\n", "def show_featuremap_from_imgs(featurevis, feature_indexs, img_dir, is_show, output_dir):\n", "    if not isinstance(feature_indexs, (list, tuple)):\n", "        feature_indexs = [feature_indexs]\n", "    img_paths = [os.path.join(img_dir, x) for x in os.listdir(img_dir) if 'jpg' in x]\n", "    for path in img_paths:\n", "        img = cv2.imread(path)\n", "        # 这里是输入模型前的图片处理\n", "        input = img.astype(np.float32).copy()\n", "        # 显示特征图\n", "        _show_save_data(featurevis, input, img, feature_indexs, path, is_show, output_dir)\n", "\n", "\n", "\n", "if __name__ == '__main__':\n", "    img_dir = '.'\n", "    out_dir = './out'\n", "\n", "    init_shape = (1024, 1024, 3)  # 值不重要，只要前向一遍网络时候不报错即可\n", "    feature_index = [32, 70, 96, 155]  # 可视化的层索引\n", "    use_gpu = True\n", "    is_show = False\n", "    model = torchvision.models.resnet50(pretrained=True)  # 这里创建模型\n", "    #model = torch.load(path)\n", "    if use_gpu:\n", "        model.cuda()\n", "    featurevis = create_featuremap_vis(model, use_gpu, init_shape)\n", "    show_featuremap_from_imgs(featurevis, feature_index, img_dir, is_show, out_dir)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Ipython display来展示\n", "\n", "from IPython.display import display, Code\n", "\n", "code_str = df.loc[0,\"content\"]\n", "display(Code(code_str, language=\"python\"))"]}, {"cell_type": "markdown", "id": "5a5d803a-0d24-4e04-b582-0bf7c2a1facb", "metadata": {}, "source": ["- **<PERSON><PERSON><PERSON><PERSON>**"]}, {"cell_type": "code", "execution_count": 17, "id": "4e6acf04-83b6-4fb7-9122-f87a02e1b801", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1/example_train_0.jsonl.zst: 45705517 bytes \n"]}], "source": ["# 先解压\n", "!zstd -d /root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1/example_train_0.jsonl.zst -o /root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1/example_train_0_.jsonl"]}, {"cell_type": "code", "execution_count": 18, "id": "f944c1ad-d5a0-4ff2-a207-1ef9480a6586", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一行 JSON 数据： {\n", "    \"text\": \"<PERSON><PERSON><PERSON><PERSON> Returns To Write And Direct 'Star Wars: Episode IX'\\n09/12/2017 11:11 am ET Updated Sep 12, 2017\\nThe return of the J.J.\\n<PERSON><PERSON> <PERSON>\\nUPDATE: 4:00 p.m. ET — In addition to the director news, \\\"Star Wars\\\" announced that the premiere date for \\\"Episode IX\\\" will be Dec. 20, 2019.\\nStar Wars: Episode IX is scheduled for release on December 20, 2019. pic.twitter.com/rDBqmuHX89\\n— Star Wars (@starwars) September 12, 2017\\nThe Force was with <PERSON><PERSON><PERSON><PERSON> when he launched the new set of \\\"Star Wars\\\" films with \\\"The Force Awakens,\\\" so now <PERSON> is bringing him back.\\nAs Deadline reported on Tuesday, and according to a press release on StarWars.com, <PERSON> will return to write and direct \\\"Star Wars: Episode IX.\\\" The statement reads:\\nA post shared by Star Wars (@starwars) on Sep 12, 2017 at 7:28am PDT\\nAfter Disney unexpectedly parted ways with former \\\"Episode IX\\\" director <PERSON> earlier this month, rumors that <PERSON><PERSON>, who is directing \\\"Star Wars: The Last Jedi,\\\" would take over surfaced. But Deadline reports that <PERSON> decided not to take the offer to direct.\\nOn <PERSON>' hiring, Lucasfilm President <PERSON> said, \\\"With 'The Force Awakens,' J<PERSON>J<PERSON> delivered everything we could have possibly hoped for, and I am so excited that he is coming back to close out this trilogy.\\\"\\nAfter what we saw in \\\"Force Awakens,\\\" we're pretty excited about it, too. We just hope they call it \\\"The Return of the J.J.\\\"\\nThere Were 2 Royal Moments You Might Have Missed At Biden's Inauguration Joe Biden Removed Trump's Diet Coke Button, Twitter Bubbled Up With Jokes Princess Charlene Defends New Buzzcut Hairstyle: 'It Was My Decision' Katy Perry Closes Out Biden's Inauguration Celebration With A Literal Bang\\n'Star Wars' Postage Stamps\\nEntertainment Editor, HuffPost\\nMovies Star Wars J.J. Abrams\",\n", "    \"meta\": {\n", "        \"redpajama_set_name\": \"RedPajamaCommonCrawl\"\n", "    }\n", "}\n"]}], "source": ["import json\n", "\n", "output_file = \"/root/autodl-tmp/minideepseek/v3/data/slimpajama/train/chunk1/example_train_0_.jsonl\"\n", "\n", "# 读取 `.jsonl` 文件的第一行\n", "with open(output_file, \"r\", encoding=\"utf-8\") as jsonl_file:\n", "    first_line = jsonl_file.readline().strip()\n", "    if first_line:\n", "        try:\n", "            json_data = json.loads(first_line)\n", "            print(\"第一行 JSON 数据：\", json.dumps(json_data, indent=4, ensure_ascii=False))\n", "        except json.JSONDecodeError:\n", "            print(\"❌ JSON 格式错误！原始数据：\", first_line)\n", "    else:\n", "        print(\"❌ 读取失败，文件为空！\")"]}, {"cell_type": "markdown", "id": "67c9ba1d-be58-4dab-aa82-e269586f7b7c", "metadata": {}, "source": ["- **openr1**"]}, {"cell_type": "code", "execution_count": 19, "id": "175c4164-3ea5-470f-af82-b9754c9f1588", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一行数据：\n", " {\n", "    \"problem\":\"3. As shown in the figure, the side lengths of $\\\\triangle A B C$ are $A B=14, B C$ $=16, A C=26, P$ is a point on the angle bisector $A D$ of $\\\\angle A$, and $B P \\\\perp A D, M$ is the midpoint of $B C$. Find the value of $P M$ $\\\\qquad$\",\n", "    \"solution\":\"3. 6 .\\n\\nFrom the figure, take $B^{\\\\prime}$ on $A C$ such that $A B^{\\\\prime}=A B=14$, then $B^{\\\\prime} C=12$. Since $\\\\triangle A B B^{\\\\prime}$ is an isosceles triangle, we know that the intersection point of $B B^{\\\\prime}$ and $A D$ is $P$ (concurrency of five lines), so $P$ is the midpoint of $B B^{\\\\prime}$.\",\n", "    \"answer\":\"6\",\n", "    \"problem_type\":\"Geometry\",\n", "    \"question_type\":\"math-word-problem\",\n", "    \"source\":\"cn_contest\",\n", "    \"uuid\":\"3fd1e4c6-8bd4-53d2-b40a-7c72360707e4\",\n", "    \"is_reasoning_complete\":[\n", "        true,\n", "        true\n", "    ],\n", "    \"generations\":[\n", "        \"<think>\\nA<PERSON>right, let's tackle this geometry problem. So, we have triangle ABC with sides AB = 14, BC = 16, and AC = 26. Point P is on the angle bisector AD of angle A, and BP is perpendicular to AD. M is the midpoint of BC. We need to find the length of PM. Hmm, okay, let's start by visualizing the triangle and the given points.\\n\\nFirst, I should recall some properties related to angle bisectors and midpoints. The angle bisector theorem states that the angle bisector divides the opposite side in the ratio of the adjacent sides. So, in triangle ABC, AD is the angle bisector of angle A, so it should divide BC into segments proportional to AB and AC. That is, BD\\/DC = AB\\/AC = 14\\/26 = 7\\/13. Since BC is 16, we can find BD and DC.\\n\\nLet me calculate BD and DC. Let BD = 7k and DC = 13k. Then BD + DC = 7k + 13k = 20k = 16. So, k = 16\\/20 = 4\\/5. Therefore, BD = 7*(4\\/5) = 28\\/5 = 5.6 and DC = 13*(4\\/5) = 52\\/5 = 10.4. So, BD = 5.6 and DC = 10.4. Got that.\\n\\nNow, since M is the midpoint of BC, BM = MC = 16\\/2 = 8. So, <PERSON> divides BC into two equal parts of 8 each. So, <PERSON> is located 8 units from B and C.\\n\\nNow, we need to find PM. To do this, perhaps coordinate geometry might help. Let me set up a coordinate system. Let me place point A at the origin (0,0) for simplicity. Then, we can place point B somewhere in the plane, point C somewhere else, and compute coordinates accordingly. But since we have lengths, maybe using coordinates would allow us to compute everything algebraically.\\n\\nAlternatively, maybe using vectors or trigonometry. Let me see. But coordinate geometry seems feasible here.\\n\\nLet me proceed step by step. Let's set point A at (0,0). Let me place AD along the x-axis for simplicity since AD is an angle bisector. Wait, but angle bisector is not necessarily along the x-axis unless we position the triangle that way. Alternatively, maybe align AD along the x-axis. Let me try that.\\n\\nSo, let's set point A at (0,0), and let AD be along the positive x-axis. Then, point D is on BC, and AD is the angle bisector. Then, we can find coordinates of B and C such that AD is the x-axis.\\n\\nWait, but maybe that complicates things because BC is not along any axis. Alternatively, maybe place point B at (0,0), point C at (16,0), but then point A is somewhere above the x-axis. But given that AB = 14 and AC = 26, that might be manageable.\\n\\nWait, if I place B at (0,0), C at (16,0), then BC is along the x-axis. Then, point A is somewhere above the x-axis. Then, AB = 14, AC = 26. Let's find the coordinates of A. Let me call the coordinates of A as (x, y). Then, distance from A to B is sqrt(x^2 + y^2) = 14, and distance from A to C is sqrt((x - 16)^2 + y^2) = 26.\\n\\nSo, we have two equations:\\n\\n1) x² + y² = 14² = 196\\n\\n2) (x - 16)² + y² = 26² = 676\\n\\nSubtracting equation 1 from equation 2:\\n\\n(x - 16)² + y² - x² - y² = 676 - 196\\n\\nExpand (x - 16)^2: x² -32x +256\\n\\nSo, x² -32x +256 + y² -x² - y² = 480\\n\\nSimplify: -32x +256 = 480\\n\\nThen, -32x = 480 -256 = 224\\n\\nSo, x = -224 \\/32 = -7. So, x = -7. Therefore, the coordinates of A are (-7, y). Then, from equation 1, x² + y² = 196. So, (-7)^2 + y² = 196 => 49 + y² =196 => y²=147 => y= sqrt(147)=7*sqrt(3). So, coordinates of A are (-7, 7√3).\\n\\nOkay, so let's recap. Coordinates:\\n\\n- B is at (0,0)\\n\\n- C is at (16,0)\\n\\n- A is at (-7, 7√3)\\n\\nThen, AD is the angle bisector of angle A. We found earlier that BD = 5.6 and DC =10.4, so D is located 5.6 units from B along BC. Since BC is from (0,0) to (16,0), so D is at (5.6, 0). Because starting from B at (0,0), moving 5.6 units along the x-axis gives D at (5.6, 0).\\n\\nSo, AD is the line from A (-7,7√3) to D (5.6,0). Let me write 5.6 as 28\\/5 to keep it exact. 5.6 = 28\\/5. So, D is at (28\\/5, 0).\\n\\nWe need to find the equation of AD. Let's compute the slope of AD first. The slope m is (0 - 7√3)\\/(28\\/5 - (-7)) = (-7√3)\\/(28\\/5 +35\\/5) = (-7√3)\\/(63\\/5) = (-7√3)*(5\\/63) = (-35√3)\\/63 = (-5√3)\\/9.\\n\\nTherefore, the equation of AD is y - 7√3 = (-5√3)\\/9 (x +7). Let's confirm that. Starting at point A (-7,7√3), slope -5√3\\/9.\\n\\nAlternatively, parametric equations for AD. Since it's a line from A to D, we can parametrize it as t going from 0 to 1:\\n\\nx(t) = -7 + t*(28\\/5 +7) = -7 + t*(28\\/5 +35\\/5) = -7 + t*(63\\/5)\\n\\ny(t) = 7√3 + t*(0 -7√3) = 7√3 -7√3 t\\n\\nBut perhaps another approach. Since P is a point on AD such that BP is perpendicular to AD. So, BP ⊥ AD.\\n\\nGiven that, perhaps we can find the coordinates of P by finding where BP is perpendicular to AD.\\n\\nSo, if we can express BP as a line that is perpendicular to AD and passes through B, then the intersection of BP with AD is point P.\\n\\nWait, BP is perpendicular to AD, so BP is a line starting at B (0,0) and going in the direction perpendicular to AD. Since AD has slope -5√3\\/9, the perpendicular slope is the negative reciprocal, which is 9\\/(5√3) = 3√3\\/5.\\n\\nTherefore, the equation of BP is y = (3√3\\/5)x.\\n\\nNow, find the intersection of BP with AD. AD has equation y = (-5√3\\/9)(x +7) +7√3. Wait, let me rederive the equation of AD to be sure.\\n\\nPoint A is (-7,7√3), point D is (28\\/5,0). So, slope m = (0 -7√3)\\/(28\\/5 +7) = (-7√3)\\/(63\\/5) = -5√3\\/9 as before. So, equation of AD: y -7√3 = -5√3\\/9 (x +7). So, y = (-5√3\\/9)(x +7) +7√3.\\n\\nLet's compute that:\\n\\ny = (-5√3\\/9)x - (35√3\\/9) +7√3\\n\\nConvert 7√3 to 63√3\\/9:\\n\\ny = (-5√3\\/9)x -35√3\\/9 +63√3\\/9 = (-5√3\\/9)x +28√3\\/9\\n\\nSo, equation of AD: y = (-5√3\\/9)x +28√3\\/9\\n\\nEquation of BP: y = (3√3\\/5)x\\n\\nFind intersection point P between BP and AD. Set the two equations equal:\\n\\n(3√3\\/5)x = (-5√3\\/9)x +28√3\\/9\\n\\nMultiply both sides by 45 to eliminate denominators:\\n\\n45*(3√3\\/5)x = 45*(-5√3\\/9)x +45*(28√3\\/9)\\n\\nSimplify:\\n\\n9*3√3 x = -5*5√3 x + 5*28√3\\n\\n27√3 x = -25√3 x +140√3\\n\\nBring all terms to left:\\n\\n27√3 x +25√3 x -140√3 =0\\n\\n52√3 x =140√3\\n\\nDivide both sides by √3:\\n\\n52x =140\\n\\nx=140\\/52=35\\/13≈2.692\\n\\nThen, substitute x=35\\/13 into BP equation: y=(3√3\\/5)*(35\\/13)= (105√3)\\/65=21√3\\/13≈2.886\\n\\nSo, coordinates of P are (35\\/13,21√3\\/13)\\n\\nNow, coordinates of M, the midpoint of BC. Since B is at (0,0), C at (16,0), then M is at ((0+16)\\/2, (0+0)\\/2)=(8,0)\\n\\nSo, M is at (8,0). Now, need to find PM. Coordinates of P are (35\\/13,21√3\\/13), coordinates of M are (8,0). The distance PM is sqrt[(8 -35\\/13)^2 + (0 -21√3\\/13)^2]\\n\\nFirst, compute 8 -35\\/13: 8=104\\/13, so 104\\/13 -35\\/13=69\\/13\\n\\nThen, compute the distance squared: (69\\/13)^2 + (21√3\\/13)^2\\n\\nCalculate each term:\\n\\n(69\\/13)^2 = (69^2)\\/(13^2)=4761\\/169\\n\\n(21√3\\/13)^2= (21^2)*(3)\\/(13^2)=441*3\\/169=1323\\/169\\n\\nSum: 4761 +1323=6084\\n\\nThus, sqrt(6084\\/169)=sqrt(36*169)\\/13=6*13\\/13=6\\n\\nWait, wait. 6084 divided by 169. Let me compute that. 169*36=6084. Because 169*30=5070, 169*6=1014, 5070+1014=6084. So sqrt(6084\\/169)=sqrt(36)=6.\\n\\nSo, PM=6. So, the answer is 6. Therefore, PM=6.\\n\\nWait, that seems straightforward. Let me just verify the calculations step by step to make sure.\\n\\nFirst, coordinates of A, B, C:\\n\\nB (0,0), C (16,0), A (-7,7√3). Correct, since AB distance is sqrt((-7-0)^2 + (7√3 -0)^2)=sqrt(49 + 147)=sqrt(196)=14. Correct. AC distance is sqrt((16 +7)^2 + (0 -7√3)^2)=sqrt(23² + (7√3)^2)=sqrt(529 +147)=sqrt(676)=26. Correct.\\n\\nCoordinates of D: BD=28\\/5=5.6, so D is at (5.6,0)=(28\\/5,0). Correct.\\n\\nEquation of AD: slope calculated as -5√3\\/9, equation y = (-5√3\\/9)x +28√3\\/9. Correct.\\n\\nEquation of BP: perpendicular to AD, slope 3√3\\/5, passing through B (0,0), so y=(3√3\\/5)x. Correct.\\n\\nIntersection: solving (3√3\\/5)x = (-5√3\\/9)x +28√3\\/9. Multiplying through by 45, got 27√3 x = -25√3 x +140√3, leading to 52√3 x=140√3, so x=140\\/52=35\\/13. Then y=3√3\\/5*(35\\/13)=21√3\\/13. Correct coordinates of P.\\n\\nMidpoint M is (8,0). Distance PM: sqrt[(8 -35\\/13)^2 + (0 -21√3\\/13)^2]. Calculated 8 -35\\/13=69\\/13. Squared, (69\\/13)^2=4761\\/169. Then (21√3\\/13)^2=1323\\/169. Sum=6084\\/169=36. sqrt(36)=6. So PM=6. All steps check out. So, the answer is 6.\\n\\n**Final Answer**\\n\\\\boxed{6}\\n<\\/think>\\n\\nGiven triangle \\\\( \\\\triangle ABC \\\\) with side lengths \\\\( AB = 14 \\\\), \\\\( BC = 16 \\\\), and \\\\( AC = 26 \\\\). Point \\\\( P \\\\) is on the angle bisector \\\\( AD \\\\) of \\\\( \\\\angle A \\\\), and \\\\( BP \\\\perp AD \\\\). \\\\( M \\\\) is the midpoint of \\\\( BC \\\\). We need to find the value of \\\\( PM \\\\).\\n\\n1. **Using the Angle Bisector Theorem**:\\n   - The angle bisector \\\\( AD \\\\) divides \\\\( BC \\\\) in the ratio \\\\( AB : AC = 14 : 26 = 7 : 13 \\\\).\\n   - Therefore, \\\\( BD = \\\\frac{7}{20} \\\\times 16 = \\\\frac{28}{5} \\\\) and \\\\( DC = \\\\frac{13}{20} \\\\times 16 = \\\\frac{52}{5} \\\\).\\n\\n2. **Coordinates Setup**:\\n   - Place \\\\( B \\\\) at \\\\( (0, 0) \\\\), \\\\( C \\\\) at \\\\( (16, 0) \\\\), and find coordinates of \\\\( A \\\\).\\n   - Solving the system of equations for distances \\\\( AB = 14 \\\\) and \\\\( AC = 26 \\\\), we find \\\\( A \\\\) at \\\\( (-7, 7\\\\sqrt{3}) \\\\).\\n\\n3. **Coordinates of \\\\( D \\\\)**:\\n   - \\\\( D \\\\) is the point on \\\\( BC \\\\) such that \\\\( BD = \\\\frac{28}{5} \\\\), so \\\\( D \\\\) is at \\\\( \\\\left( \\\\frac{28}{5}, 0 \\\\right) \\\\).\\n\\n4. **Equations of Lines**:\\n   - Slope of \\\\( AD \\\\): \\\\( \\\\frac{0 - 7\\\\sqrt{3}}{\\\\frac{28}{5} + 7} = -\\\\frac{5\\\\sqrt{3}}{9} \\\\).\\n   - Equation of \\\\( AD \\\\): \\\\( y = -\\\\frac{5\\\\sqrt{3}}{9}x + \\\\frac{28\\\\sqrt{3}}{9} \\\\).\\n   - Slope of \\\\( BP \\\\) (perpendicular to \\\\( AD \\\\)): \\\\( \\\\frac{9}{5\\\\sqrt{3}} = \\\\frac{3\\\\sqrt{3}}{5} \\\\).\\n   - Equation of \\\\( BP \\\\): \\\\( y = \\\\frac{3\\\\sqrt{3}}{5}x \\\\).\\n\\n5. **Intersection of \\\\( BP \\\\) and \\\\( AD \\\\)**:\\n   - Solving \\\\( \\\\frac{3\\\\sqrt{3}}{5}x = -\\\\frac{5\\\\sqrt{3}}{9}x + \\\\frac{28\\\\sqrt{3}}{9} \\\\):\\n     - \\\\( x = \\\\frac{35}{13} \\\\), \\\\( y = \\\\frac{21\\\\sqrt{3}}{13} \\\\).\\n   - Coordinates of \\\\( P \\\\): \\\\( \\\\left( \\\\frac{35}{13}, \\\\frac{21\\\\sqrt{3}}{13} \\\\right) \\\\).\\n\\n6. **Midpoint \\\\( M \\\\)**:\\n   - \\\\( M \\\\) is the midpoint of \\\\( BC \\\\), so \\\\( M \\\\) is at \\\\( (8, 0) \\\\).\\n\\n7. **Distance \\\\( PM \\\\)**:\\n   - Using distance formula: \\\\( PM = \\\\sqrt{\\\\left( 8 - \\\\frac{35}{13} \\\\right)^2 + \\\\left( 0 - \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} \\\\).\\n   - Simplifying, \\\\( PM = \\\\sqrt{\\\\left( \\\\frac{69}{13} \\\\right)^2 + \\\\left( \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} = \\\\sqrt{\\\\frac{6084}{169}} = 6 \\\\).\\n\\nThus, the value of \\\\( PM \\\\) is \\\\(\\\\boxed{6}\\\\).\",\n", "        \"<think>\\nOkay, so I need to find the length of PM in triangle ABC where AB=14, BC=16, AC=26. P is a point on the angle bisector AD of angle A, and <PERSON> is perpendicular to AD. M is the midpoint of BC. Hmm, let's start by visualizing the problem. \\n\\nFirst, let me sketch triangle ABC. AB is 14, BC is 16, and AC is 26. Wait, those side lengths seem a bit off. If AB=14, BC=16, and AC=26, then triangle inequality might not hold. Let me check. \\n\\nAB + BC = 14 + 16 = 30, which is greater than AC=26. Then AB + AC = 14 + 26 = 40, which is greater than BC=16. BC + AC = 16 + 26 = 42, which is greater than AB=14. So yes, triangle inequalities are satisfied. Alright.\\n\\nNow, point D is on BC such that AD is the angle bisector of angle A. There's a theorem about angle bisectors: the ratio of the two segments created on the opposite side is equal to the ratio of the adjacent sides. So BD\\/DC = AB\\/AC. Let me calculate BD and DC.\\n\\nGiven AB=14, AC=26, so BD\\/DC = 14\\/26 = 7\\/13. Since BD + DC = BC=16, let me set BD = 7k and DC =13k. Then 7k +13k=20k=16, so k=16\\/20=4\\/5. Therefore, BD=7*(4\\/5)=28\\/5=5.6 and DC=13*(4\\/5)=52\\/5=10.4. So D divides BC into BD=28\\/5 and DC=52\\/5.\\n\\nAD is the angle bisector. Now P is a point on AD such that BP is perpendicular to AD. I need to find PM, where M is the midpoint of BC. Since M is the midpoint, BM=MC=8, since BC=16.\\n\\nHmm, okay. Let me think about coordinates. Maybe coordinate geometry can help here. Let me place point B at the origin to make calculations easier. Let me set coordinate system with point B at (0,0). Let me denote coordinates:\\n\\nLet me let point B be at (0,0). Then, since BC is 16 units, and if I place BC along the x-axis, then point C would be at (16,0). Then M, the midpoint of BC, would be at (8,0).\\n\\nBut wait, AB is 14, AC is 26. So point A must be somewhere such that the distance from A to B is 14, and from A to C is 26. Let me find coordinates for point A.\\n\\nLet me set coordinate system with B at (0,0), C at (16,0). Let coordinates of A be (x,y). Then distance AB=14, so sqrt(x^2 + y^2)=14, and AC=26, so sqrt((x-16)^2 + y^2)=26. Let's square both equations:\\n\\nx² + y² = 196 ...(1)\\n\\n(x -16)² + y² = 676 ...(2)\\n\\nSubtract equation (1) from (2):\\n\\n(x -16)² + y² - x² - y² = 676 - 196\\n\\nExpand (x-16)^2: x² -32x +256 -x² = 480\\n\\nSimplify: -32x +256 = 480\\n\\nThen -32x = 480 -256 = 224\\n\\nTherefore x = -224 \\/32 = -7.\\n\\nSo x= -7. Then from equation (1), x² + y²=196 => (-7)^2 + y²=196 =>49 + y²=196 => y²=147 => y= sqrt(147)=7*sqrt(3). So coordinates of A are (-7,7√3).\\n\\nAlright, so coordinates:\\n\\nB: (0,0)\\n\\nC: (16,0)\\n\\nA: (-7,7√3)\\n\\nM is midpoint of BC: (8,0)\\n\\nAD is the angle bisector. We already found D divides BC into BD=28\\/5=5.6 and DC=10.4. So coordinate of D: Since BD=28\\/5=5.6, starting from B(0,0) towards C(16,0), D is at (5.6,0). 5.6 is 28\\/5, so as a coordinate, D is (28\\/5,0).\\n\\nSo angle bisector AD connects A(-7,7√3) to D(28\\/5,0). Now, P is a point on AD such that BP is perpendicular to AD. So we need to find point P on AD where BP is perpendicular to AD. Then, once we have coordinates of P, we can compute PM, the distance from P to M(8,0).\\n\\nSo the plan is:\\n\\n1. Find parametric equations for AD.\\n\\n2. Find the equation of BP such that it's perpendicular to AD.\\n\\n3. Find point P as the intersection of BP and AD.\\n\\n4. Compute coordinates of P.\\n\\n5. Compute distance from P to M(8,0).\\n\\nAlternatively, since BP is perpendicular to AD, and P is on AD, we can parametrize AD and find the foot of the perpendicular from B to AD. That would be point P.\\n\\nYes, that's another way. Since BP is perpendicular to AD, P is the foot of the perpendicular from B to AD.\\n\\nSo, to find P, we can compute the foot of the perpendicular from B(0,0) to line AD.\\n\\nFirst, let's find the equation of line AD.\\n\\nCoordinates of A: (-7,7√3)\\n\\nCoordinates of D: (28\\/5,0)\\n\\nSlope of AD: (0 -7√3)\\/(28\\/5 - (-7)) = (-7√3)\\/(28\\/5 +35\\/5)= (-7√3)\\/(63\\/5)= (-7√3)*(5\\/63)= (-35√3)\\/63= (-5√3)\\/9.\\n\\nSo slope of AD is -5√3\\/9. Therefore, the equation of AD can be written as:\\n\\ny -7√3 = (-5√3\\/9)(x +7)\\n\\nBut maybe easier to parametrize AD. Let's parametrize AD with a parameter t.\\n\\nFrom point A(-7,7√3) to D(28\\/5,0). The vector from A to D is (28\\/5 - (-7), 0 -7√3) = (28\\/5 +35\\/5, -7√3)= (63\\/5, -7√3). So parametric equations:\\n\\nx = -7 + (63\\/5)t\\n\\ny =7√3 -7√3 t\\n\\nWhere t ranges from 0 to 1.\\n\\nAlternatively, we can parametrize it as:\\n\\nx = -7 + (63\\/5)t\\n\\ny =7√3(1 - t)\\n\\nt from 0 to1.\\n\\nSo any point P on AD can be written as (-7 + (63\\/5)t,7√3(1 - t)) for some t between 0 and1.\\n\\nNow, BP is perpendicular to AD. The vector BP is from B(0,0) to P(-7 + (63\\/5)t,7√3(1 - t)).\\n\\nSo the direction vector of BP is (-7 + (63\\/5)t,7√3(1 - t)).\\n\\nThe direction vector of AD is (63\\/5, -7√3). Therefore, the dot product of BP and AD should be zero since they are perpendicular.\\n\\nWait, no. Wait, BP is perpendicular to AD. So the direction vector of AD is (63\\/5, -7√3). The direction vector of BP is (x_p -0, y_p -0) = (x_p, y_p). So we need the vector BP (x_p, y_p) to be perpendicular to the direction vector of AD (63\\/5, -7√3). Therefore, their dot product is zero.\\n\\nSo:\\n\\nx_p*(63\\/5) + y_p*(-7√3) =0\\n\\nBut x_p and y_p are coordinates of P, which is on AD. So x_p = -7 + (63\\/5)t, y_p =7√3(1 - t). Substitute into the equation:\\n\\n[ -7 + (63\\/5)t ]*(63\\/5) + [7√3(1 - t)]*(-7√3) =0\\n\\nLet me compute each term:\\n\\nFirst term: [ -7 + (63\\/5)t ]*(63\\/5)\\n\\n= -7*(63\\/5) + (63\\/5)*(63\\/5) t\\n\\n= -441\\/5 + (3969\\/25) t\\n\\nSecond term: [7√3(1 - t)]*(-7√3)\\n\\n= -49*3*(1 -t )\\n\\n= -147*(1 -t )\\n\\nSo combining both terms:\\n\\n-441\\/5 + (3969\\/25) t -147 +147 t =0\\n\\nLet me convert all terms to 25 denominator:\\n\\n-441\\/5 = -2205\\/25\\n\\n-147 = -3675\\/25\\n\\n147 t = (3675\\/25) t\\n\\nSo equation becomes:\\n\\n-2205\\/25 + (3969\\/25) t -3675\\/25 + (3675\\/25) t =0\\n\\nCombine terms:\\n\\n[ -2205 -3675 ]\\/25 + (3969 +3675)\\/25 t =0\\n\\nCompute numerators:\\n\\n-2205 -3675 = -5880\\n\\n3969 +3675 = 7644\\n\\nSo:\\n\\n-5880\\/25 + 7644\\/25 t =0\\n\\nMultiply both sides by 25:\\n\\n-5880 +7644 t =0\\n\\n7644 t =5880\\n\\nt=5880\\/7644\\n\\nSimplify the fraction:\\n\\nDivide numerator and denominator by 12:\\n\\n5880 ÷12=490\\n\\n7644 ÷12=637\\n\\nWait, 5880 ÷12: 12*490=5880. 7644 ÷12=637. So t=490\\/637\\n\\nCheck if 490 and 637 have a common factor. 490=49*10=7*7*10. 637=7*91=7*13*7. Wait, 7*91=637, 91=13*7. So 637=7*7*13. So 490=7*7*10, 637=7*7*13. So common factors are 7*7=49. So divide numerator and denominator by 49:\\n\\n490 ÷49=10\\n\\n637 ÷49=13. 13*49=637. Yes. So t=10\\/13.\\n\\nTherefore, t=10\\/13.\\n\\nSo coordinates of P are:\\n\\nx_p= -7 + (63\\/5)*(10\\/13)= -7 + (63*10)\\/(5*13)= -7 + (630)\\/(65)= -7 + 126\\/13.\\n\\nConvert -7 to -91\\/13, so x_p= (-91\\/13 +126\\/13)=35\\/13.\\n\\nSimilarly, y_p=7√3(1 -10\\/13)=7√3*(3\\/13)=21√3\\/13.\\n\\nSo coordinates of P are (35\\/13,21√3\\/13).\\n\\nNow, coordinates of M are (8,0). So PM is the distance between (35\\/13,21√3\\/13) and (8,0).\\n\\nCompute the difference in x: 8 -35\\/13= (104\\/13 -35\\/13)=69\\/13.\\n\\nDifference in y:0 -21√3\\/13= -21√3\\/13.\\n\\nTherefore, PM= sqrt[(69\\/13)^2 + (-21√3\\/13)^2]\\n\\nCompute each term:\\n\\n(69\\/13)^2= (69^2)\\/(13^2)=4761\\/169\\n\\n(-21√3\\/13)^2= (441*3)\\/169=1323\\/169\\n\\nSum:4761 +1323=6084. 6084\\/169=6084 ÷169.\\n\\n169*36=6084, since 169*30=5070, 169*6=1014, 5070+1014=6084. So sqrt(6084\\/169)=sqrt(36*169)\\/13= (6*13)\\/13=6. Wait, wait:\\n\\nWait, sqrt(6084\\/169)=sqrt(36*169)\\/13= (6*13)\\/13=6. Wait, let me check:\\n\\nWait 6084\\/169=36. So sqrt(36)=6. Therefore, PM=6.\\n\\nWait, that's a nice integer. So PM=6.\\n\\nWait, let me confirm the steps again. So coordinates of P were found by parametrizing AD, then using the condition that BP is perpendicular to AD. Then calculated t=10\\/13, found coordinates of P as (35\\/13,21√3\\/13). Then distance from P to M(8,0) is sqrt[(69\\/13)^2 + (21√3\\/13)^2]. Then 69^2=4761, (21√3)^2=441*3=1323, sum 4761+1323=6084. 6084 divided by 169=36. sqrt(36)=6. So indeed PM=6. So the answer is 6. So boxed answer is \\\\boxed{6}\\n\\n**Final Answer**\\n\\\\boxed{6}\\n<\\/think>\\n\\nTo find the value of \\\\( PM \\\\) in triangle \\\\( ABC \\\\) with \\\\( AB = 14 \\\\), \\\\( BC = 16 \\\\), \\\\( AC = 26 \\\\), where \\\\( P \\\\) is a point on the angle bisector \\\\( AD \\\\) such that \\\\( BP \\\\perp AD \\\\), and \\\\( M \\\\) is the midpoint of \\\\( BC \\\\):\\n\\n1. **Determine coordinates of points**:\\n   - Place \\\\( B \\\\) at \\\\( (0, 0) \\\\) and \\\\( C \\\\) at \\\\( (16, 0) \\\\).\\n   - Find coordinates of \\\\( A \\\\) using the distances \\\\( AB = 14 \\\\) and \\\\( AC = 26 \\\\). Solving the system of equations, \\\\( A \\\\) is found to be \\\\( (-7, 7\\\\sqrt{3}) \\\\).\\n   - Midpoint \\\\( M \\\\) of \\\\( BC \\\\) is \\\\( (8, 0) \\\\).\\n\\n2. **Find coordinates of \\\\( D \\\\)**:\\n   - \\\\( D \\\\) divides \\\\( BC \\\\) in the ratio \\\\( AB:AC = 7:13 \\\\). Solving, \\\\( BD = \\\\frac{28}{5} \\\\) and \\\\( DC = \\\\frac{52}{5} \\\\). Thus, \\\\( D \\\\) is \\\\( \\\\left( \\\\frac{28}{5}, 0 \\\\right) \\\\).\\n\\n3. **Parametrize line \\\\( AD \\\\)**:\\n   - Parametric equations for \\\\( AD \\\\) are \\\\( x = -7 + \\\\frac{63}{5}t \\\\) and \\\\( y = 7\\\\sqrt{3}(1 - t) \\\\).\\n\\n4. **Find point \\\\( P \\\\)**:\\n   - \\\\( P \\\\) is the foot of the perpendicular from \\\\( B \\\\) to \\\\( AD \\\\). Using the condition that \\\\( BP \\\\perp AD \\\\), solve for parameter \\\\( t \\\\) which gives \\\\( t = \\\\frac{10}{13} \\\\).\\n   - Coordinates of \\\\( P \\\\) are \\\\( \\\\left( \\\\frac{35}{13}, \\\\frac{21\\\\sqrt{3}}{13} \\\\right) \\\\).\\n\\n5. **Calculate distance \\\\( PM \\\\)**:\\n   - Using the distance formula between \\\\( P \\\\left( \\\\frac{35}{13}, \\\\frac{21\\\\sqrt{3}}{13} \\\\right) \\\\) and \\\\( M (8, 0) \\\\):\\n     \\\\[\\n     PM = \\\\sqrt{\\\\left( 8 - \\\\frac{35}{13} \\\\right)^2 + \\\\left( 0 - \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} = \\\\sqrt{\\\\left( \\\\frac{69}{13} \\\\right)^2 + \\\\left( -\\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} = \\\\sqrt{\\\\frac{6084}{169}} = \\\\sqrt{36} = 6\\n     \\\\]\\n\\nThus, the value of \\\\( PM \\\\) is \\\\(\\\\boxed{6}\\\\).\"\n", "    ],\n", "    \"correctness_math_verify\":[\n", "        true,\n", "        true\n", "    ],\n", "    \"correctness_llama\":null,\n", "    \"finish_reasons\":null,\n", "    \"correctness_count\":2,\n", "    \"messages\":[\n", "        {\n", "            \"content\":\"3. As shown in the figure, the side lengths of $\\\\triangle A B C$ are $A B=14, B C$ $=16, A C=26, P$ is a point on the angle bisector $A D$ of $\\\\angle A$, and $B P \\\\perp A D, M$ is the midpoint of $B C$. Find the value of $P M$ $\\\\qquad$\",\n", "            \"role\":\"user\"\n", "        },\n", "        {\n", "            \"content\":\"<think>\\nA<PERSON>right, let's tackle this geometry problem. So, we have triangle ABC with sides AB = 14, BC = 16, and AC = 26. Point P is on the angle bisector AD of angle A, and BP is perpendicular to AD. M is the midpoint of BC. We need to find the length of PM. Hmm, okay, let's start by visualizing the triangle and the given points.\\n\\nFirst, I should recall some properties related to angle bisectors and midpoints. The angle bisector theorem states that the angle bisector divides the opposite side in the ratio of the adjacent sides. So, in triangle ABC, AD is the angle bisector of angle A, so it should divide BC into segments proportional to AB and AC. That is, BD\\/DC = AB\\/AC = 14\\/26 = 7\\/13. Since BC is 16, we can find BD and DC.\\n\\nLet me calculate BD and DC. Let BD = 7k and DC = 13k. Then BD + DC = 7k + 13k = 20k = 16. So, k = 16\\/20 = 4\\/5. Therefore, BD = 7*(4\\/5) = 28\\/5 = 5.6 and DC = 13*(4\\/5) = 52\\/5 = 10.4. So, BD = 5.6 and DC = 10.4. Got that.\\n\\nNow, since M is the midpoint of BC, BM = MC = 16\\/2 = 8. So, <PERSON> divides BC into two equal parts of 8 each. So, M is located 8 units from B and C.\\n\\nNow, we need to find PM. To do this, perhaps coordinate geometry might help. Let me set up a coordinate system. Let me place point A at the origin (0,0) for simplicity. Then, we can place point B somewhere in the plane, point C somewhere else, and compute coordinates accordingly. But since we have lengths, maybe using coordinates would allow us to compute everything algebraically.\\n\\nAlternatively, maybe using vectors or trigonometry. Let me see. But coordinate geometry seems feasible here.\\n\\nLet me proceed step by step. Let's set point A at (0,0). Let me place AD along the x-axis for simplicity since AD is an angle bisector. Wait, but angle bisector is not necessarily along the x-axis unless we position the triangle that way. Alternatively, maybe align AD along the x-axis. Let me try that.\\n\\nSo, let's set point A at (0,0), and let AD be along the positive x-axis. Then, point D is on BC, and AD is the angle bisector. Then, we can find coordinates of B and C such that AD is the x-axis.\\n\\nWait, but maybe that complicates things because BC is not along any axis. Alternatively, maybe place point B at (0,0), point C at (16,0), but then point A is somewhere above the x-axis. But given that AB = 14 and AC = 26, that might be manageable.\\n\\nWait, if I place B at (0,0), C at (16,0), then BC is along the x-axis. Then, point A is somewhere above the x-axis. Then, AB = 14, AC = 26. Let's find the coordinates of A. Let me call the coordinates of A as (x, y). Then, distance from A to B is sqrt(x^2 + y^2) = 14, and distance from A to C is sqrt((x - 16)^2 + y^2) = 26.\\n\\nSo, we have two equations:\\n\\n1) x² + y² = 14² = 196\\n\\n2) (x - 16)² + y² = 26² = 676\\n\\nSubtracting equation 1 from equation 2:\\n\\n(x - 16)² + y² - x² - y² = 676 - 196\\n\\nExpand (x - 16)^2: x² -32x +256\\n\\nSo, x² -32x +256 + y² -x² - y² = 480\\n\\nSimplify: -32x +256 = 480\\n\\nThen, -32x = 480 -256 = 224\\n\\nSo, x = -224 \\/32 = -7. So, x = -7. Therefore, the coordinates of A are (-7, y). Then, from equation 1, x² + y² = 196. So, (-7)^2 + y² = 196 => 49 + y² =196 => y²=147 => y= sqrt(147)=7*sqrt(3). So, coordinates of A are (-7, 7√3).\\n\\nOkay, so let's recap. Coordinates:\\n\\n- B is at (0,0)\\n\\n- C is at (16,0)\\n\\n- A is at (-7, 7√3)\\n\\nThen, AD is the angle bisector of angle A. We found earlier that BD = 5.6 and DC =10.4, so D is located 5.6 units from B along BC. Since BC is from (0,0) to (16,0), so D is at (5.6, 0). Because starting from B at (0,0), moving 5.6 units along the x-axis gives D at (5.6, 0).\\n\\nSo, AD is the line from A (-7,7√3) to D (5.6,0). Let me write 5.6 as 28\\/5 to keep it exact. 5.6 = 28\\/5. So, D is at (28\\/5, 0).\\n\\nWe need to find the equation of AD. Let's compute the slope of AD first. The slope m is (0 - 7√3)\\/(28\\/5 - (-7)) = (-7√3)\\/(28\\/5 +35\\/5) = (-7√3)\\/(63\\/5) = (-7√3)*(5\\/63) = (-35√3)\\/63 = (-5√3)\\/9.\\n\\nTherefore, the equation of AD is y - 7√3 = (-5√3)\\/9 (x +7). Let's confirm that. Starting at point A (-7,7√3), slope -5√3\\/9.\\n\\nAlternatively, parametric equations for AD. Since it's a line from A to D, we can parametrize it as t going from 0 to 1:\\n\\nx(t) = -7 + t*(28\\/5 +7) = -7 + t*(28\\/5 +35\\/5) = -7 + t*(63\\/5)\\n\\ny(t) = 7√3 + t*(0 -7√3) = 7√3 -7√3 t\\n\\nBut perhaps another approach. Since P is a point on AD such that BP is perpendicular to AD. So, BP ⊥ AD.\\n\\nGiven that, perhaps we can find the coordinates of P by finding where BP is perpendicular to AD.\\n\\nSo, if we can express BP as a line that is perpendicular to AD and passes through B, then the intersection of BP with AD is point P.\\n\\nWait, BP is perpendicular to AD, so BP is a line starting at B (0,0) and going in the direction perpendicular to AD. Since AD has slope -5√3\\/9, the perpendicular slope is the negative reciprocal, which is 9\\/(5√3) = 3√3\\/5.\\n\\nTherefore, the equation of BP is y = (3√3\\/5)x.\\n\\nNow, find the intersection of BP with AD. AD has equation y = (-5√3\\/9)(x +7) +7√3. Wait, let me rederive the equation of AD to be sure.\\n\\nPoint A is (-7,7√3), point D is (28\\/5,0). So, slope m = (0 -7√3)\\/(28\\/5 +7) = (-7√3)\\/(63\\/5) = -5√3\\/9 as before. So, equation of AD: y -7√3 = -5√3\\/9 (x +7). So, y = (-5√3\\/9)(x +7) +7√3.\\n\\nLet's compute that:\\n\\ny = (-5√3\\/9)x - (35√3\\/9) +7√3\\n\\nConvert 7√3 to 63√3\\/9:\\n\\ny = (-5√3\\/9)x -35√3\\/9 +63√3\\/9 = (-5√3\\/9)x +28√3\\/9\\n\\nSo, equation of AD: y = (-5√3\\/9)x +28√3\\/9\\n\\nEquation of BP: y = (3√3\\/5)x\\n\\nFind intersection point P between BP and AD. Set the two equations equal:\\n\\n(3√3\\/5)x = (-5√3\\/9)x +28√3\\/9\\n\\nMultiply both sides by 45 to eliminate denominators:\\n\\n45*(3√3\\/5)x = 45*(-5√3\\/9)x +45*(28√3\\/9)\\n\\nSimplify:\\n\\n9*3√3 x = -5*5√3 x + 5*28√3\\n\\n27√3 x = -25√3 x +140√3\\n\\nBring all terms to left:\\n\\n27√3 x +25√3 x -140√3 =0\\n\\n52√3 x =140√3\\n\\nDivide both sides by √3:\\n\\n52x =140\\n\\nx=140\\/52=35\\/13≈2.692\\n\\nThen, substitute x=35\\/13 into BP equation: y=(3√3\\/5)*(35\\/13)= (105√3)\\/65=21√3\\/13≈2.886\\n\\nSo, coordinates of P are (35\\/13,21√3\\/13)\\n\\nNow, coordinates of M, the midpoint of BC. Since B is at (0,0), C at (16,0), then M is at ((0+16)\\/2, (0+0)\\/2)=(8,0)\\n\\nSo, M is at (8,0). Now, need to find PM. Coordinates of P are (35\\/13,21√3\\/13), coordinates of M are (8,0). The distance PM is sqrt[(8 -35\\/13)^2 + (0 -21√3\\/13)^2]\\n\\nFirst, compute 8 -35\\/13: 8=104\\/13, so 104\\/13 -35\\/13=69\\/13\\n\\nThen, compute the distance squared: (69\\/13)^2 + (21√3\\/13)^2\\n\\nCalculate each term:\\n\\n(69\\/13)^2 = (69^2)\\/(13^2)=4761\\/169\\n\\n(21√3\\/13)^2= (21^2)*(3)\\/(13^2)=441*3\\/169=1323\\/169\\n\\nSum: 4761 +1323=6084\\n\\nThus, sqrt(6084\\/169)=sqrt(36*169)\\/13=6*13\\/13=6\\n\\nWait, wait. 6084 divided by 169. Let me compute that. 169*36=6084. Because 169*30=5070, 169*6=1014, 5070+1014=6084. So sqrt(6084\\/169)=sqrt(36)=6.\\n\\nSo, PM=6. So, the answer is 6. Therefore, PM=6.\\n\\nWait, that seems straightforward. Let me just verify the calculations step by step to make sure.\\n\\nFirst, coordinates of A, B, C:\\n\\nB (0,0), C (16,0), A (-7,7√3). Correct, since AB distance is sqrt((-7-0)^2 + (7√3 -0)^2)=sqrt(49 + 147)=sqrt(196)=14. Correct. AC distance is sqrt((16 +7)^2 + (0 -7√3)^2)=sqrt(23² + (7√3)^2)=sqrt(529 +147)=sqrt(676)=26. Correct.\\n\\nCoordinates of D: BD=28\\/5=5.6, so D is at (5.6,0)=(28\\/5,0). Correct.\\n\\nEquation of AD: slope calculated as -5√3\\/9, equation y = (-5√3\\/9)x +28√3\\/9. Correct.\\n\\nEquation of BP: perpendicular to AD, slope 3√3\\/5, passing through B (0,0), so y=(3√3\\/5)x. Correct.\\n\\nIntersection: solving (3√3\\/5)x = (-5√3\\/9)x +28√3\\/9. Multiplying through by 45, got 27√3 x = -25√3 x +140√3, leading to 52√3 x=140√3, so x=140\\/52=35\\/13. Then y=3√3\\/5*(35\\/13)=21√3\\/13. Correct coordinates of P.\\n\\nMidpoint M is (8,0). Distance PM: sqrt[(8 -35\\/13)^2 + (0 -21√3\\/13)^2]. Calculated 8 -35\\/13=69\\/13. Squared, (69\\/13)^2=4761\\/169. Then (21√3\\/13)^2=1323\\/169. Sum=6084\\/169=36. sqrt(36)=6. So PM=6. All steps check out. So, the answer is 6.\\n\\n**Final Answer**\\n\\\\boxed{6}\\n<\\/think>\\n\\nGiven triangle \\\\( \\\\triangle ABC \\\\) with side lengths \\\\( AB = 14 \\\\), \\\\( BC = 16 \\\\), and \\\\( AC = 26 \\\\). Point \\\\( P \\\\) is on the angle bisector \\\\( AD \\\\) of \\\\( \\\\angle A \\\\), and \\\\( BP \\\\perp AD \\\\). \\\\( M \\\\) is the midpoint of \\\\( BC \\\\). We need to find the value of \\\\( PM \\\\).\\n\\n1. **Using the Angle Bisector Theorem**:\\n   - The angle bisector \\\\( AD \\\\) divides \\\\( BC \\\\) in the ratio \\\\( AB : AC = 14 : 26 = 7 : 13 \\\\).\\n   - Therefore, \\\\( BD = \\\\frac{7}{20} \\\\times 16 = \\\\frac{28}{5} \\\\) and \\\\( DC = \\\\frac{13}{20} \\\\times 16 = \\\\frac{52}{5} \\\\).\\n\\n2. **Coordinates Setup**:\\n   - Place \\\\( B \\\\) at \\\\( (0, 0) \\\\), \\\\( C \\\\) at \\\\( (16, 0) \\\\), and find coordinates of \\\\( A \\\\).\\n   - Solving the system of equations for distances \\\\( AB = 14 \\\\) and \\\\( AC = 26 \\\\), we find \\\\( A \\\\) at \\\\( (-7, 7\\\\sqrt{3}) \\\\).\\n\\n3. **Coordinates of \\\\( D \\\\)**:\\n   - \\\\( D \\\\) is the point on \\\\( BC \\\\) such that \\\\( BD = \\\\frac{28}{5} \\\\), so \\\\( D \\\\) is at \\\\( \\\\left( \\\\frac{28}{5}, 0 \\\\right) \\\\).\\n\\n4. **Equations of Lines**:\\n   - Slope of \\\\( AD \\\\): \\\\( \\\\frac{0 - 7\\\\sqrt{3}}{\\\\frac{28}{5} + 7} = -\\\\frac{5\\\\sqrt{3}}{9} \\\\).\\n   - Equation of \\\\( AD \\\\): \\\\( y = -\\\\frac{5\\\\sqrt{3}}{9}x + \\\\frac{28\\\\sqrt{3}}{9} \\\\).\\n   - Slope of \\\\( BP \\\\) (perpendicular to \\\\( AD \\\\)): \\\\( \\\\frac{9}{5\\\\sqrt{3}} = \\\\frac{3\\\\sqrt{3}}{5} \\\\).\\n   - Equation of \\\\( BP \\\\): \\\\( y = \\\\frac{3\\\\sqrt{3}}{5}x \\\\).\\n\\n5. **Intersection of \\\\( BP \\\\) and \\\\( AD \\\\)**:\\n   - Solving \\\\( \\\\frac{3\\\\sqrt{3}}{5}x = -\\\\frac{5\\\\sqrt{3}}{9}x + \\\\frac{28\\\\sqrt{3}}{9} \\\\):\\n     - \\\\( x = \\\\frac{35}{13} \\\\), \\\\( y = \\\\frac{21\\\\sqrt{3}}{13} \\\\).\\n   - Coordinates of \\\\( P \\\\): \\\\( \\\\left( \\\\frac{35}{13}, \\\\frac{21\\\\sqrt{3}}{13} \\\\right) \\\\).\\n\\n6. **Midpoint \\\\( M \\\\)**:\\n   - \\\\( M \\\\) is the midpoint of \\\\( BC \\\\), so \\\\( M \\\\) is at \\\\( (8, 0) \\\\).\\n\\n7. **Distance \\\\( PM \\\\)**:\\n   - Using distance formula: \\\\( PM = \\\\sqrt{\\\\left( 8 - \\\\frac{35}{13} \\\\right)^2 + \\\\left( 0 - \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} \\\\).\\n   - Simplifying, \\\\( PM = \\\\sqrt{\\\\left( \\\\frac{69}{13} \\\\right)^2 + \\\\left( \\\\frac{21\\\\sqrt{3}}{13} \\\\right)^2} = \\\\sqrt{\\\\frac{6084}{169}} = 6 \\\\).\\n\\nThus, the value of \\\\( PM \\\\) is \\\\(\\\\boxed{6}\\\\).\",\n", "            \"role\":\"assistant\"\n", "        }\n", "    ]\n", "}\n"]}], "source": ["import pandas as pd\n", "\n", "file_path = \"/root/autodl-tmp/minideepseek/v3/data/openr1/all/default-00002-of-00010.parquet\"\n", "\n", "try:\n", "    # 读取第一行数据\n", "    df = pd.read_parquet(file_path)\n", "    \n", "    if not df.empty:\n", "        first_row = df.iloc[0]  # 取第一行数据\n", "        print(\"第一行数据：\\n\", first_row.to_json(indent=4, force_ascii=False))\n", "    else:\n", "        print(\"❌ 读取失败，文件为空！\")\n", "\n", "except FileNotFoundError:\n", "    print(f\"❌ 文件未找到：{file_path}\")\n", "except Exception as e:\n", "    print(f\"❌ 发生错误：{e}\")"]}, {"cell_type": "markdown", "id": "1319fd9f-bc88-4b78-a9b7-67d0cf07bb16", "metadata": {}, "source": ["- **ape210K**"]}, {"cell_type": "code", "execution_count": 21, "id": "60ce4c5e-ee21-457d-b551-40a96696c6b9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>question</th>\n", "      <th>question_chinese</th>\n", "      <th>chain</th>\n", "      <th>result</th>\n", "      <th>result_float</th>\n", "      <th>equation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ape210k__01099539</td>\n", "      <td>The fifth grade students participated in the voluntary book donation activity. Class Five 1 donated 500 books, Class Five 2 donated 80% of Class Five 1, Class Five 3 donated 120% of Class Five 2, Class Five 1 and Class Five 3 Donate more books than who? (Please compare the two methods).</td>\n", "      <td>五年级同学参加义务捐书活动，五1班捐了500本，五2班捐的本数是五1班80%，五3班捐的本数是五2班120%，五1班和五3班比谁捐书多？(请用两种方法比较一下)．</td>\n", "      <td>&lt;result&gt;1&lt;/result&gt;</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>x=1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  id                                                                                                                                                                                                                                                                                         question                                                                   question_chinese               chain result  result_float equation\n", "0  ape210k__01099539  The fifth grade students participated in the voluntary book donation activity. Class Five 1 donated 500 books, Class Five 2 donated 80% of Class Five 1, Class Five 3 donated 120% of Class Five 2, Class Five 1 and Class Five 3 Donate more books than who? (Please compare the two methods).  五年级同学参加义务捐书活动，五1班捐了500本，五2班捐的本数是五1班80%，五3班捐的本数是五2班120%，五1班和五3班比谁捐书多？(请用两种方法比较一下)．  <result>1</result>      1           1.0      x=1"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 指定 Parquet 文件路径\n", "file_path = \"/root/autodl-tmp/minideepseek/v3/data/ape210k/data/train-00000-of-00001-b9f022a8492442e4.parquet\"\n", "\n", "# 读取 Parquet 文件\n", "df = pd.read_parquet(file_path, engine=\"pyarrow\")\n", "\n", "# 设置 Pandas 选项，确保完整显示内容\n", "pd.set_option(\"display.max_columns\", None)  # 显示所有列\n", "pd.set_option(\"display.max_colwidth\", None)  # 显示所有单元格内容\n", "pd.set_option(\"display.width\", 1000)  # 设置输出宽度，防止换行\n", "\n", "# 显示第一行\n", "df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "4608d100-b9a7-4f96-b27f-f01142d233ab", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}