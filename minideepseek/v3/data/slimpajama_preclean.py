import json
import multiprocessing
import string
import hashlib
import re
import simhash
from tqdm import tqdm

# **输入 & 输出路径**
input_file = "/root/autodl-tmp/minideepseek/v3/data/basic_clean/processed_slimpajama.jsonl"
output_file = "/root/autodl-tmp/minideepseek/v3/data/basic_clean/processed_slimpajama_cleaned.jsonl"

# **批量大小 & 进程数**
BATCH_SIZE = 150_000  # ✅ 你可以调整（50K ~ 200K 试试）
NUM_WORKERS = 8        # ✅ 进程数，视 CPU 线程数调整

# **全局去重集合（用于存储哈希和 SimHash）**
global_hash_set = set()
global_simhash_set = set()

# **清理文本**
#def clean_text(text):
#    """ 过滤掉不可见字符，仅保留标准字符 """
#    text = ''.join(c for c in text if c in string.printable)  # 只保留可打印字符
#    return text[:10000]  # 限制最大长度

# **计算 SimHash**
#def compute_simhash(text):
#    text = clean_text(text)
#    if not text.strip():
#        return 0  # **空文本返回固定值，避免 SimHash 崩溃**
#    return simhash.Simhash(text).value

# **计算文本哈希**
def compute_hash(text):
    """ 计算文本的 MD5 哈希值，用于精准去重 """
    return hashlib.md5(text.encode()).hexdigest()

# **判断低质量文本**
def is_low_quality(text):
    """ 过滤低质量文本 """
    if len(text) < 20 or len(text) > 100000:  # 过短 or 过长
        return True
    return False

# **定义文本处理**
def process_batch(lines):
    """ 处理一个 batch，并返回去重 + 过滤后的数据 """
    cleaned_batch = []
    local_hash_set = set()
    local_simhash_set = set()

    for line in lines:
        try:
            data = json.loads(line)
            text = data.get("text", "").strip()

            if not text or is_low_quality(text):
                continue  # **跳过低质量内容**

            # **哈希去重（完全重复文本）**
            hash_value = compute_hash(text)
            if hash_value in global_hash_set or hash_value in local_hash_set:
                continue  # **完全重复，跳过**
            
            # **哈希去重（语义近似文本）**
            #simhash_value = compute_simhash(text)
            #if any(abs(simhash_value - h) < 3 for h in global_simhash_set) or \
            #   any(abs(simhash_value - h) < 3 for h in local_simhash_set):
            #    continue  # **近似重复，跳过**

            # **记录已处理文本**
            local_hash_set.add(hash_value)
            cleaned_batch.append(json.dumps({"text": text}, ensure_ascii=False) + "\n")

        except Exception as e:
            print(f"❌ Error processing line: {e}")

    # **更新全局去重集合**
    global_hash_set.update(local_hash_set)
    global_simhash_set.update(local_simhash_set)

    return cleaned_batch

# **数据流式处理**
def fast_jsonl_processing():
    total_lines = 27724440 # **统计总行数**
    print(f"📊 预计处理 {total_lines} 行数据...")

    with open(input_file, "r", encoding="utf-8") as fin, open(output_file, "w", encoding="utf-8") as fout:
        pool = multiprocessing.Pool(NUM_WORKERS)
        batch = []
        results = []

        removed_count = 0  # **统计被过滤掉的文本**
        kept_count = 0

        # ✅ **进度条**
        with tqdm(total=total_lines, desc="Processing", unit=" lines") as pbar:
            for line in fin:
                batch.append(line)
                if len(batch) >= BATCH_SIZE:
                    result = pool.apply_async(process_batch, (batch,))
                    results.append(result)
                    batch = []

                # ✅ **边处理边写入**
                while results and results[0].ready():
                    cleaned_data = results.pop(0).get()
                    fout.writelines(cleaned_data)  # **写入**
                    pbar.update(len(batch))  # **更新进度条**
                    fout.flush()

                    kept_count += len(cleaned_data)
                    removed_count += BATCH_SIZE - len(cleaned_data)

            # ✅ **处理剩余数据**
            if batch:
                result = pool.apply_async(process_batch, (batch,))
                results.append(result)

            # ✅ **确保所有任务完成**
            for result in results:
                cleaned_data = result.get()
                fout.writelines(cleaned_data)
                pbar.update(len(batch))
                fout.flush()

                kept_count += len(cleaned_data)
                removed_count += BATCH_SIZE - len(cleaned_data)

        pool.close()
        pool.join()

    print(f"✅ 处理完成，清理后的数据已保存到: {output_file}")
    print(f"📉 过滤掉 {removed_count:,} 行（{(removed_count / total_lines) * 100:.2f}%）")
    print(f"📈 保留 {kept_count:,} 行（{(kept_count / total_lines) * 100:.2f}%）")

# **运行**
if __name__ == "__main__":
    fast_jsonl_processing()