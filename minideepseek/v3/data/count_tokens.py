import json
from transformers import AutoTokenizer
from multiprocessing import Pool
from glob import glob
from tqdm import tqdm  # 进度条库

tokenizer = AutoTokenizer.from_pretrained("/root/autodl-tmp/minideepseek/v3/tokenizer")

def count_tokens(file_path):
    token_count = 0
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data = json.loads(line)  # 读取 JSONL
            text = data.get("text", "")  # 确保 JSON 里有 "text" 键
            tokens = tokenizer.encode(text, add_special_tokens=False, truncation=True, max_length=131072)  # 避免超长警告
            token_count += len(tokens)
    return file_path, token_count

if __name__ == "__main__":
    files = glob("/root/autodl-tmp/minideepseek/v3/data/deep_clean/**/*.jsonl", recursive=True)
    
    with Pool(32) as p:  # 32 进程并行处理
        results = []
        with tqdm(total=len(files), desc="Processing JSONL Files") as pbar:
            for result in p.imap_unordered(count_tokens, files):
                results.append(result)
                pbar.update(1)  # 每处理一个文件，进度条更新 1

    for file, count in results:
        print(f"{file}: {count} tokens")