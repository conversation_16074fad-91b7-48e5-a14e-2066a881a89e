# Process config example including:
#   - all global arguments
#   - all ops and their arguments

project_name: 'minideepseekv3'
dataset_path: '/root/autodl-tmp/minideepseek/v3/data/basic_clean/processed_openr1/'  # path to your dataset directory or file
export_path: '/root/autodl-tmp/minideepseek/v3/data/deep_clean/djed_openr1/djed_openr1.jsonl'

text_keys: 'text'

export_shard_size: 300000000                                        # 300MB shard size of exported dataset in Byte. In default, it's 0, which means export the whole dataset into only one file. If it's set a positive number, the exported dataset will be split into several dataset shards, and the max size of each shard won't larger than the export_shard_size
export_in_parallel: false                                   # whether to export the result dataset in parallel to a single file, which usually takes less time. It only works when export_shard_size is 0, and its default number of processes is the same as the argument np. **Notice**: If it's True, sometimes exporting in parallel might require much more time due to the IO blocking, especially for very large datasets. When this happens, False is a better choice, although it takes more time.
np: 64                                                       # number of subprocess to process your dataset
text_keys: 'text'                                        # the key name of field where the sample texts to be processed, e.g., `text`, `instruction`, `output`, ...
                                                            # Note: currently, we support specify only ONE key for each op, for cases requiring multiple keys, users can specify the op multiple times. We will only use the first key of `text_keys` when you set multiple keys.
suffixes: []                                                # the suffix of files that will be read. For example: '.txt', 'txt' or ['txt', '.pdf', 'docx']
use_cache: false                                             # whether to use the cache management of Hugging Face datasets. It might take up lots of disk space when using cache
ds_cache_dir:  /root/autodl-tmp/minideepseek/v3/data/data_jucier_cache                                          # cache dir for Hugging Face datasets. In default, it\'s the same as the environment variable `HF_DATASETS_CACHE`, whose default value is usually "~/.cache/huggingface/datasets". If this argument is set to a valid path by users, it will override the default cache dir
use_checkpoint: false                                       # whether to use the checkpoint management to save the latest version of dataset to work dir when processing. Rerun the same config will reload the checkpoint and skip ops before it. Cache will be disabled when using checkpoint. If args of ops before the checkpoint are changed, all ops will be rerun from the beginning.
temp_dir:  /root/autodl-tmp/minideepseek/v3/data/data_jucier_cache/temp
open_tracer: true                                          # whether to open the tracer to trace the changes during process. It might take more time when opening tracer
op_list_to_trace: []                                        # only ops in this list will be traced by tracer. If it's empty, all ops will be traced. Only available when tracer is opened.
trace_num: 10                                               # number of samples to show the differences between datasets before and after each op. Only available when tracer is opened.
op_fusion: true                                            # whether to fuse operators that share the same intermediate variables automatically. Op fusion might reduce the memory requirements slightly but speed up the whole process.
cache_compress: zstd                                        # the compression method of the cache file, which can be specified in ['gzip', 'zstd', 'lz4']. If this parameter is None, the cache file will not be compressed. We recommend you turn on this argument when your input dataset is larger than tens of GB and your disk space is not enough.

# eoc_special_token: '<|__dj__eoc|>'                          # the special token that represents the end of a chunk in the text. In default, it's "<|__dj__eoc|>". You can specify your own special token according to your input dataset.

# only for data analysis
save_stats_in_one_file: true                               # whether to store all stats result into one file


# process schedule
# a list of several process operators with their arguments
process:
  - clean_email_mapper:
  - clean_links_mapper:
#  - fix_unicode_mapper: #一般经过清洗的文本修复上不会有大问题
  - punctuation_normalization_mapper:
  - whitespace_normalization_mapper:
  - clean_copyright_mapper:

    # 单样本文本字符数
  - text_length_filter:
      max_len: 100000 # 略超过95%中位数

    # 单样本所含单词数
  - words_num_filter:
      min_num: 150 # 保证推理文本不会太短
      max_num: 20000 # 略超过95%中位数

    # 平均行长度
  - average_line_length_filter:
      min_len: 30 # 保证推理文本不太短
      max_len: 10000  # 给得也比较宽松

    # 最长行长度
  - maximum_line_length_filter:
      max_len: 70000  # 略超过95%中位数

    # 字符重复率
  - character_repetition_filter:
      max_ratio: 0.5  # 极值太大、设置超过95%中位数

    # 单词重复率
  - word_repetition_filter:
      rep_len: 10
      max_ratio: 0.5 # 极值太大、设置超过95%中位数

    # 特殊字符比例
  - alphanumeric_filter:
      tokenization: false
      min_ratio: 0.0  # 不设下限
      max_ratio: 0.8  # 上限允许0.5

#  - document_simhash_deduplicator:
#      tokenization: space
#      window_size: 2 # 值越小去重越宽松，更小的窗口、降低查重率
#      lowercase: True
#      ignore_pattern: '\p{P}'
#      num_blocks: 8
#      hamming_distance: 6 # 值越小去重越严格，由原本的4改为6
