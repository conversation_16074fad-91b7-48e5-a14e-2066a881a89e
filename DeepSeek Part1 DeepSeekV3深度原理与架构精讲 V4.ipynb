import math
from dataclasses import dataclass
from typing import Tuple, Optional, Literal

import torch
from torch import nn
import torch.nn.functional as F
import torch.distributed as dist

X (4,3) => Q (4,3)

import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt

# 假设一个 4x5 的矩阵（4个样本，30个专家）
scores = torch.randn(4, 30)  # 随机生成一个得分矩阵

# 计算softmax和sigmoid的结果
softmax_scores = F.softmax(scores, dim=-1)
sigmoid_scores = torch.sigmoid(scores)

# 创建图表：每个样本画一个图，4个样本对应4个图
fig, axes = plt.subplots(4, 1, figsize=(8, 12))

# 绘制每个样本的Softmax和Sigmoid折线图
for i in range(4):
    axes[i].plot(softmax_scores[i].detach().numpy(), 'b-', label='Softmax', marker='o')  # 蓝色：Softmax
    axes[i].plot(sigmoid_scores[i].detach().numpy(), 'r-', label='Sigmoid', marker='o')  # 红色：Sigmoid
    axes[i].set_title(f'Sample {i+1}')
    axes[i].set_xlabel('Experts')
    axes[i].set_ylabel('Activation')
    axes[i].legend()

plt.tight_layout()
plt.show()

# 假设一个 2x5 的矩阵（2个样本，5个专家）
scores1 = torch.randn(1, 5)  # 随机生成一个得分矩阵
scores2 = torch.randn(1, 5)
sigmoid_scores_1 = torch.sigmoid(scores1)
sigmoid_scores_2 = torch.sigmoid(scores2)

sigmoid_scores_1

sigmoid_scores_2

# 对si_t进行归一化
# 让一个token上所有专家的激活值除以所有专家的总激活值
si_t_1 = sigmoid_scores_1/sigmoid_scores_1.sum()
si_t_2 = sigmoid_scores_2/sigmoid_scores_2.sum()

si_t_1

si_t_2

Pi = (si_t_1 + si_t_2)/2

Pi

        ex1 ex2  ex3 ... exn
token1   0   1    1       0        
token2   1   1    0       0
         0.5 1    0.5   ...

# 假设一个 2x5 的矩阵（2个样本，5个专家）
scores1 = torch.randn(1, 5)  # 随机生成一个得分矩阵
scores2 = torch.randn(1, 5)
sigmoid_scores_1 = torch.sigmoid(scores1)
sigmoid_scores_2 = torch.sigmoid(scores2)

# 对si_t进行归一化
# 让一个token上所有专家的激活值除以所有专家的总激活值
si_t_1 = sigmoid_scores_1/sigmoid_scores_1.sum()
si_t_2 = sigmoid_scores_2/sigmoid_scores_2.sum()

# 选择每一行的topk个值，其他部分为0
k = 2  # 假设选择top2个值

# 获取每行的topk值的索引
topk_indices_1 = torch.topk(si_t_1, k, dim=-1).indices
topk_indices_2 = torch.topk(si_t_2, k, dim=-1).indices

# 创建一个与原矩阵同形状的全零矩阵
topk_mask_1 = torch.zeros_like(sigmoid_scores_1)
topk_mask_2 = torch.zeros_like(sigmoid_scores_2)

# 将topk索引位置的值设为1
topk_mask_1.scatter_(1, topk_indices_1, 1)
topk_mask_2.scatter_(1, topk_indices_2, 1)

# 输出结果
topk_mask_1

topk_mask_2

fi = (topk_mask_1 + topk_mask_2)/2

fi

tp(model para), dp, ep

700 ep ==> 64

ep == index ==> experts_list[0:63]
                experts_list[64:127] 

gate ==> touch every gpus

linear/embedding  ==> nn.Linear(512,32)  nn.Linear(32,1024)

     row  ==> 512/ world_size  => 64
     column  ==> 1024 / world_size  ==> troch.dsirtibuted all_reduce()

一个nn.linear的流程是

nn.Linear(in_features, out_features)

x (seq_len, d)

W (new_d, d)

W.T (d, new_d)

x (seq_len, new_d)

线性层的本质是 y = x * W.T

将行进行分割，也就是在d上进行分割，将w.T分割成

(d1, new_d) == gpu 0
(d2, new_d)    1
(d3, new_d)    2
(d4, new_d)

x (seq_len, d)   (d1, new_d)

如果是将列进行分割，也就是在new_d上进行分割，将w.T分割成：

(d, new_d1)  
(d, new_d2)  
(d, new_d3)
(d, new_d4)

X (seq_len, d)

import torch

# 假设3个token，2个组，1组4个专家
scores = torch.randn(size=(3,2,4))

scores

group_scores = scores.amax(dim=-1)

group_scores.shape #聚合之后，结构变成3个token，每个token2个分数

group_scores #取每组的最大值

# 使用每组最高的两个值、并对这两个值求和
group_scores = scores.topk(2, dim=-1)[0].sum(dim=-1)

group_scores

1.8303 + 1.5419 #两组中的top2，进行加和就等于scores.topk(2, dim=-1)[0].sum(dim=-1)的结果

scores.topk(2, dim=-1) #返回两个对象，一个是topk的值，一个是topk所在的索引

x = torch.randn(3,10) # 原本是10个维度
scores = torch.randn(3,5,4) # 5个组，1组4个专家
group_scores = torch.randn(3,5) # 假设5个组

group_scores

# indices = group_scores.topk(self.topk_groups, dim=-1)[1]
topk_groups = 2

indices = group_scores.topk(topk_groups, dim=-1)[1]
indices

# mask = scores.new_ones(x.size(0), self.n_groups, dtype=bool).scatter_(1, indices, False)

n_groups = 5

# 生成 3个token、每个token 5个1的矩阵
scores.new_ones(3,5)

# 按照topk输出的索引、将被选中的位置标记为false，这部分为掩码
mask = scores.new_ones(3,5, dtype=bool).scatter_(1, indices, False)
mask

#scores = scores.masked_fill_(mask.unsqueeze(-1), float("-inf")).flatten(1)

# 将掩码矩阵升维，增加一个维度
mask.unsqueeze(-1)

scores # 3个token。每个token5个组，每个组里4个专家

# 按照掩码、将没有被选中的组用inf填写
scores.masked_fill_(mask.unsqueeze(-1), float("-inf"))

# 将掩码后的分数拉平、变成3个token、每个20个分数
scores.flatten(1)

original_scores = torch.randn(3,20) # 3个token，20个专家
scores = original_scores

scores

#执行topk，获得索引
topk = 2
indices = torch.topk(scores, topk, dim=-1)[1]

#按照索引，取出相应的值
weights = original_scores.gather(1, indices)

weights

experts (1000)

0~99 gpu0 experts （0...99）
100~199 gpu1       （100...199）

gpu0, gpu1, 2, 3 （进程process，独立的环境）

rank、local_rank、某些数据的索引、结构  ==> 以环境变量方式定义到特定gpu上

model.py, pretrain.py

64

rank 0  start 0 * 64 = 0
        end 0 + 64 = 64

[0,64)

rank 1  start 1 * 64 = 64
        end 64 + 64 = 128

[64,128)

import torch

#(token, topk)

indices = torch.randint(low=0,high=5,size=(3,2))

indices

idx, top = torch.where(indices == 1)

idx

top

#(token, topk)

weights = torch.randn((3,2))

weights[(0,1),(1,0)]

weights[idx, top, None]



import torch

x = torch.randn((3,6)) # 3个token，d_model=6

x

#假设3个token，20个专家，选择了topk5个专家出来
topk_ids = torch.randint(low=0,high=20, size=(3,5))

topk_ids

cnts = topk_ids.new_zeros((topk_ids.shape[0], 20)) # 20个专家

cnts

cnts.scatter_(1, topk_ids, 1)

tokens_per_expert = cnts.sum(dim=0)

tokens_per_expert #每个专家被多少个token给选中

ep_size = 4

tokens_per_ep_rank = tokens_per_expert.view(ep_size, -1).sum(dim=1)

tokens_per_ep_rank

tokens_per_expert_group = tokens_per_expert.new_empty(
                tokens_per_expert.shape[0]
            )

tokens_per_expert #20个专家，代表每个专家被多少个token给激活

tokens_per_expert_group # 全0，代表一共有多少个专家被激活

topk_ids

idxs = topk_ids.view(-1).argsort() #拉平，对拉平后的序列进行排序、并返回排序后的元素在原始序列中的索引

idxs

sorted_tokens = x[idxs // topk_ids.shape[1]]

sorted_tokens # 取出每个专家所对应的token的值（每个token选了5个专家，一共15个）

sorted_tokens_shape = sorted_tokens.shape

gathered_tokens = sorted_tokens.new_empty(
                tokens_per_expert_group.sum(dim=0).item(), sorted_tokens.shape[1]
            ) # 每个专家所对应的token的值

gathered_tokens # 

[0.01,0.5,0.9, 10**10]
                488

DP --> data 大, model 小
TP --> data 一致、model不一致（weights）  ==> sum
       data 不一样，model不一致  ==> concat
EP --> 64个专家并行、一致的数据不同的模型、测试/推理的时候会使用不一致的数据

embedding层实现张量并行

Pipeline

